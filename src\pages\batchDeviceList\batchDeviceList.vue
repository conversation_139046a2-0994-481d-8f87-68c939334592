<template>
  <div v-if="!jumpLoading" class="device-list">
    <CustomNavTitle title="设备列表" :showBack="true" background="#fff" />

    <nut-searchbar
      v-model="searchValue"
      placeholder="输入设备编号搜索设备"
      @update:model-value="handleSearchInput"
    >
      <template #rightin>
        <Search2 color="#D4D4D4" />
      </template>
    </nut-searchbar>

    <scroll-view
      class="list-content"
      scroll-y
      :lowerThreshold="200"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <div class="device-item" @click="toReactive(item)" v-for="item in deviceList" :key="item.id">
        <div class="device-item-left">
          <img :src="iconMap[type]" alt="" />
        </div>
        <div class="device-item-right">
          <div class="name">
            <span>{{ item.serialNum }}</span>
            <span class="status" :style="{ color: item.activated ? '#019E59' : '#FF6363' }">
              {{ item.activated ? '已激活' : '未激活' }}
            </span>
          </div>
          <div class="count">
            <div v-if="item.activeBy && item.activated">激活人：{{ item.activeBy }}</div>
          </div>
        </div>
      </div>
      <emput v-if="deviceList.length === 0" message="暂无设备数据" />
      <!-- <nut-empty v-if="deviceList.length === 0" description="暂无设备数据" /> -->
    </scroll-view>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import BaseInfoAPI from '@/api/device/baseInfo'
import Taro from '@tarojs/taro'
import { Search2 } from '@nutui/icons-vue-taro'
import IconShachongdeng from '../../assets/device/<EMAIL>'
import IconChongqing from '../../assets/device/<EMAIL>'
import IconShangqing from '../../assets/device/<EMAIL>'
import { eventCenter, getCurrentInstance } from '@tarojs/taro'

const { type, batchNum, innerModelName, serialNum, id } =
  Taro.getCurrentInstance().router?.params || {}

const jumpLoading = ref(false)
// 若是批次列表直接扫码或搜索设备编号跳转，则整个页面处于加载状态，查询到设备信息后无感跳转至激活页面
if (serialNum) {
  jumpLoading.value = true
  Taro.showLoading({
    title: '加载中...',
    mask: true,
  })
  const queryData = {
    serialNum,
    batchNum,
    type,
    innerModelName,
    id,
  }
  jumpLoading.value = false
  Taro.hideLoading()
  Taro.navigateTo({
    url: `/pages/deviceActivation/activation?data=${encodeURIComponent(JSON.stringify(queryData))}`,
  })
}

const iconMap = {
  1: IconShachongdeng,
  3: IconChongqing,
  2: IconShangqing,
}

const searchValue = ref('')
const searchTimer = ref<NodeJS.Timeout | null>(null)
const deviceList = ref<any>([])
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

const getList = async () => {
  loading.value = true
  try {
    const { pageNum, pageSize } = pagination
    const response = await BaseInfoAPI.deviceList({
      serialNum: searchValue.value,
      pageNum,
      pageSize,
      type,
      batchNum,
      orderByActivated: true,
    })
    deviceList.value =
      pagination.pageNum === 1 ? response.records : [...deviceList.value, ...response.records]

    pagination.total = response.total || 0
    pagination.pageNum = response.current || 1

    hasMore.value = deviceList.value.length < pagination.total
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  const instance = getCurrentInstance()
  if (instance?.router?.onShow) {
    eventCenter.on(instance.router.onShow, () => {
      // 通过设备列表或激活页面至此页面，获取设备列表
      if (!jumpLoading.value) {
        getList()
      }
    })
  }
})

// 重置列表
const resetList = () => {
  deviceList.value = []
  pagination.pageNum = 1
  hasMore.value = true
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    resetList()
    await getList()
  } finally {
    refreshing.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value) return
  if (!hasMore.value) {
    Taro.showToast({
      title: '没有更多数据了',
      icon: 'none',
    })
    return
  }
  pagination.pageNum++
  getList()
}

// 设备搜索处理
const handleSearchInput = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(async () => {
    getList()
  }, 500)
}

// 跳转激活
const toReactive = async (data: any) => {
  if (data.activated) {
    Taro.showModal({
      title: '提示',
      content: '设备已激活',
      cancelText: '重新绑定',
      confirmText: '我知道了',
      success: (result) => {
        if (!result.confirm) {
          const { type, innerModelName, serialNum, id } = data || {}
          const queryData = {
            serialNum,
            type,
            innerModelName,
            id,
          }
          Taro.navigateTo({
            url: `/pages/deviceActivation/activation?data=${encodeURIComponent(
              JSON.stringify(queryData)
            )}`,
          })
        }
      },
    })
    return
  }
  const { type, innerModelName, serialNum } = data || {}
  const queryData = {
    serialNum,
    type,
    innerModelName,
  }
  Taro.navigateTo({
    url: `/pages/deviceActivation/activation?data=${encodeURIComponent(JSON.stringify(queryData))}`,
  })
}
</script>
<style scoped lang="scss">
.device-list {
  background: #f5f6fa;
  height: 100vh;
}

.list-content {
  height: calc(100vh - 160px);
  .device-item {
    width: 690px;
    padding: 40px 30px;
    box-sizing: border-box;
    margin: 30px auto;
    border-radius: 20px;
    background: #fff;
    display: flex;
    .device-item-left {
      margin-right: 20px;
      img {
        width: 60px;
        height: 60px;
      }
    }
    .device-item-right {
      flex: 1;
      font-size: 28px;
      color: #909090;
      .name {
        display: flex;
        justify-content: space-between;
        font-size: 32px;
        color: #000;
        font-weight: bold;
        margin-bottom: 10px;
        .status {
          font-size: 28px;
          font-weight: 400;
        }
      }
      .batch {
        margin: 24px 0 16px;
      }
      .count {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
