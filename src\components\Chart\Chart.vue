<script lang="ts" setup>
import { onMounted, onUnmounted, shallowRef, defineProps, defineEmits } from 'vue'
import * as echarts from 'echarts'
import { noop, useDebounceFn, useEventListener } from '@vueuse/core'
import type { PropType } from 'vue'
import type { ECBasicOption } from 'echarts/types/dist/shared'
import { watchEffect } from 'vue'

const props = defineProps({
  option: {
    type: Object as PropType<echarts.EChartsCoreOption>,
    required: true,
  },
  isSvg: {
    type: Boolean,
    default: true,
  },
  clearWhenUpdate: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits<{
  (e: 'finished'): void
}>()

// 公共配置选项
const baseOption: ECBasicOption = {
  backgroundColor: '', // 去掉背景色
}

let offWatch = () => {}
onMounted(() => {
  initChart()
  offWatch = watchEffect(() => {
    if (chartsRef.value) {
      props.clearWhenUpdate && myChart.clear()
      hideTooltip()
      props.option && myChart?.setOption({ ...baseOption, ...props.option })
    }
  })
})
// 在 <script setup> 语法中，没有 this，需要使用外部定义的 myChart 变量
const hideTooltip = () => {
  if (myChart) {
    myChart.dispatchAction({
      type: 'hideTip',
    })
  }
}

// 初始化图表
const chartsRef = shallowRef<HTMLDivElement>()
let myChart: echarts.ECharts
let offResize = noop
const onResize = useDebounceFn(() => {
  myChart.resize()
}, 100)
// function initChart() {
//   myChart = echarts.init(chartsRef.value, 'dark', {
//     renderer: props.isSvg ? 'svg' : 'canvas',
//   })
//   myChart.on('finished', () => {
//     emits('finished')
//   })
//   offResize = useEventListener('resize', onResize)
//   // myChart.setOption({ ...baseOption, ...props.option })
// }

function initChart() {
  if (!chartsRef.value) {
    console.error('chartsRef is not available')
    return
  }
  try {
    myChart = echarts.init(chartsRef.value, 'dark', {
      renderer: props.isSvg ? 'svg' : 'canvas',
    })
    myChart.on('finished', () => {
      emits('finished')
    })
    offResize = useEventListener('resize', onResize)
  } catch (error) {
    console.error('Failed to initialize ECharts:', error)
  }
}

// 当option发生变化后重新执行setOption()
// watch(
//   () => props.option,
//   (val) => {
//     if (myChart) {
//       myChart.setOption({ ...baseOption, ...val })
//     }
//   }
// )

// 当option发生变化后重新执行setOption()
// 引入 watchEffect

// 在容器节点被销毁时，总是应调用 echartsInstance.dispose 以销毁实例释放资源，避免内存泄漏
onUnmounted(() => {
  echarts.dispose(myChart)
  offResize()
  offWatch()
})
function getChart() {
  return myChart
}

defineExpose({
  getChart,
})
</script>

<template>
  <div ref="chartsRef" class="chart" />
</template>

<style scoped lang="scss">
// :where选择器，权值为0，方便外部覆盖
:where(.chart) {
  width: 100% !important;
  height: 100%;
}
</style>
