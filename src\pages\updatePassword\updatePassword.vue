<template>
  <div class="page-content">
    <CustomNavTitle title="修改密码" background="transparent" />
    <div class="update-password-container">
      <p class="label-text">请输入旧密码</p>
      <nut-input
        :type="pwdVisible0 ? 'text' : 'password'"
        :adjust-position="false"
        v-model="formData.oldPassword"
        placeholder="请输入"
        :border="false"
      >
        <template #right>
          <img
            :src="pwdVisible0 ? pwdVisibleIcon : pwdUnVisibleIcon"
            class="visible-icon"
            @click="pwdVisible0 = !pwdVisible0"
          />
        </template>
      </nut-input>

      <p class="label-text">请输入新密码</p>
      <nut-input
        :type="pwdVisible1 ? 'text' : 'password'"
        :adjust-position="false"
        v-model="formData.password"
        placeholder="请输入"
        :border="false"
      >
        <template #right>
          <img
            :src="pwdVisible1 ? pwdVisibleIcon : pwdUnVisibleIcon"
            class="visible-icon"
            @click="pwdVisible1 = !pwdVisible1"
          />
        </template>
      </nut-input>

      <p class="label-text">请确认新密码</p>
      <nut-input
        :adjust-position="false"
        :type="pwdVisible ? 'text' : 'password'"
        v-model="formData.confirmPassword"
        placeholder="请输入"
        :border="false"
      >
        <template #right>
          <img
            :src="pwdVisible ? pwdVisibleIcon : pwdUnVisibleIcon"
            class="visible-icon"
            @click="pwdVisible = !pwdVisible"
          />
        </template>
      </nut-input>

      <div class="submit-btn">
        <nut-button type="primary" block @click="handleSubmit">提交</nut-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Taro from '@tarojs/taro'
import AuthAPI, { type UpdatePwdRequest } from '../../api/auth'
import pwdVisibleIcon from '../../assets/icon_kejian.png'
import pwdUnVisibleIcon from '../../assets/icon_bukejian.png'

const formData = ref<UpdatePwdRequest>({
  oldPassword: '',
  password: '',
  confirmPassword: '',
})

const pwdVisible = ref(false)
const pwdVisible1 = ref(false)
const pwdVisible0 = ref(false)

const handleSubmit = async () => {
  if (!formData.value.oldPassword) {
    Taro.showToast({
      title: '请输入旧密码',
      icon: 'none',
    })
    return
  }
  if (!formData.value.password) {
    Taro.showToast({
      title: '请输入新密码',
      icon: 'none',
    })
    return
  }

  if (formData.value.password.length < 8) {
    Taro.showToast({
      title: '新密码长度不得少于8位',
      icon: 'none',
    })
    return
  }

  if (formData.value.password) {
    // 弱口令检测
    const weakPasswords = ['admin321', '12345678', '87654321', 'admin123', 'root1234']
    if (weakPasswords.includes(formData.value.password.toLowerCase())) {
      Taro.showToast({
        title: '新密码过于简单，请勿使用弱口令',
        icon: 'none',
      })
      return
    }
  }

  if (formData.value.password) {
    // 至少包含两种字符类型
    const types = [
      /[a-z]/, // 小写
      /[A-Z]/, // 大写
      /[0-9]/, // 数字
      /[~!@#$%^&*()\-=+\|\[\]:"<>,\.\/\?]/, // 特殊字符
    ]
    let count = 0
    types.forEach((reg) => {
      if (reg.test(formData.value.password)) count++
    })
    if (count < 2) {
      Taro.showToast({
        title: '新密码必须包含大写字母、小写字母、数字、特殊字符中至少两种组合',
        icon: 'none',
      })
      return
    }
  }

  if (!formData.value.confirmPassword) {
    Taro.showToast({
      title: '请再次输入新密码',
      icon: 'none',
    })
    return
  }
  if (formData.value.password !== formData.value.confirmPassword) {
    Taro.showToast({
      title: '两次输入的密码不一致',
      icon: 'none',
    })
    return
  }

  await AuthAPI.updatePassword(formData.value)
  Taro.showModal({
    title: '提示',
    content: '密码修改成功，请重新登录',
    showCancel: false,
    success: async (res) => {
      if (res.confirm) {
        await AuthAPI.logout()
        Taro.removeStorageSync('token')
        Taro.navigateTo({
          url: '/pages/login/index',
        })
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.update-password-container {
  padding: 50px;
  box-sizing: border-box;
}

.page-content {
  background: url('../../assets/home-bg.png');
  background-size: cover;
}

.submit-btn {
  margin-top: 110px;
}
.label-text {
  font-size: 30px;
  color: #101010;
  margin: 50px 0 30px 50px;
}

:deep(.nut-input) {
  width: 650px;
  height: 98px;
  background: #ffffff;
  box-shadow: 0px 8px 14px 1px rgba(1, 101, 61, 0.05);
  border-radius: 20px 20px 20px 20px;
  .input-text {
    height: 60px;
    font-size: 30px;
  }
}

:deep(.nut-form-item) {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 4px 16px;
}
.visible-icon {
  width: 38px;
  height: 30px;
}
</style>
