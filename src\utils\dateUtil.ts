import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';

dayjs.extend(isoWeek);

// 生成某一年所有周的列表（格式示例：[{ label: '2023-W01', start: '2023-01-02', end: '2023-01-08' }]）
export const generateWeekOptions = (year: number) => {
  const weeks = new Array<Object>;
  const startDate = dayjs().year(year).startOf('year').startOf('isoWeek');
  const endDate = dayjs().year(year).endOf('year');

  let currentWeek = startDate;
  let weekCount = 1;

  while (currentWeek.isBefore(endDate)) {
    let weekStart = currentWeek.startOf('isoWeek');
    const weekEnd = currentWeek.endOf('isoWeek');
    if(weekStart.get('year') != year){
        
        
        weekStart = dayjs().year(year).month(0).date(1)
        console.log(weekStart.format('YYYY-MM-DD'),'wsssssss');
    }
    weeks.push({
      text: `第${weekCount.toString().padStart(2, '0')}周（${weekStart.format('MM/DD')} - ${weekEnd.format('MM/DD')}）`,
      value: weekCount,
      start: weekStart.format('YYYY-MM-DD'),
      end: weekEnd.format('YYYY-MM-DD')
    });

    currentWeek = currentWeek.add(1, 'week');
    weekCount++;
  }

  return weeks;
};

export const generateYearOptions = () => {
    const years = new Array<Object>;
    
    // 获取当前年份（如2023）
    const currentYear = dayjs().year();
    // 计算起始年份（当前年-3）
    const startYear = currentYear - 3;
  
    // 遍历生成近四年选项
    for (let year = startYear; year <= currentYear; year++) {
      const yearStart = dayjs().year(year).startOf('year');
      const yearEnd = dayjs().year(year).endOf('year');
      
      years.push({
        text: year,
        value: year,
        start: yearStart.format('YYYY-MM-DD'),
        end: yearEnd.format('YYYY-MM-DD')
      });
    }
  
    return years;
};