export default defineAppConfig({
  pages: [
    'pages/home/<USER>',
    'pages/deviceActivation/index',
    'pages/deviceActivation/activation',
    'pages/batchDeviceList/batchDeviceList',
    'pages/workOrder/index',
    'pages/workOrder/list',
    'pages/workOrderDetails/installDetails',
    'pages/afterSaleOrderDetails/afterSaleOrderDetails',
    'pages/login/index',
    'pages/deviceMap/deviceMap',
    'pages/forgetPassword/index',
    'pages/updatePassword/updatePassword',

    'pages/deviceActivation/index',
    'pages/deviceActivation/activation',
    'pages/workOrder/index',
    'pages/workOrder/list',
    'pages/login/index',
    'pages/forgetPassword/index',
    'pages/deviceManagement/index',
    'pages/deviceManagement/list',
    'pages/deviceManagement/lamp/index',
    'pages/deviceManagement/lamp/detail',
    'pages/deviceManagement/lamp/statistics',
    'pages/deviceManagement/lamp/map',
    'pages/deviceManagement/monitor/index',
    'pages/deviceManagement/monitor/detail',
    'pages/deviceManagement/soilMoisture/index',
    'pages/deviceManagement/soilMoisture/detail',
    'pages/deviceManagement/soilMoisture/statistics',
    'pages/deviceManagement/insectPestSituation/index',
    'pages/deviceManagement/insectPestSituation/detail',
    'pages/deviceManagement/insectPestSituation/analys',
    'pages/deviceManagement/insectPestSituation/recordIndex',
    'pages/deviceManagement/component/addAlarm',
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
  },
  permission: {
    'scope.camera': {
      desc: '用于扫描二维码',
    },
  },
})
