<template>
  <div class="home-container">
    <CustomNavTitle
      :title="active === 0 ? '首页' : active === 1 ? '设备地图' : '个人中心'"
      :background="active === 1 ? 'white' : 'transparent'"
      :showBack="false"
    />
    <div v-if="active === 0">
      <div class="banner">
        <img :src="homeBanner" alt="" />
      </div>
      <scroll-view class="list-content" scroll-y>
        <template v-if="auth('211000')">
          <p class="group-title" style="margin-top: 0">设备管理</p>
          <div class="menu-list">
            <DeviceManage />
          </div>
        </template>

        <template v-if="auth('212000')">
          <p class="group-title">工单管理</p>
          <div class="menu-list">
            <WorkOrder />
          </div>
        </template>

        <template v-if="auth('213000')">
          <p class="group-title">设备激活</p>
          <div class="menu-list">
            <div
              v-if="auth('mobile:home:activate:light:list')"
              class="menu-item"
              @click="toReactive(1)"
            >
              <image src="../../assets/icon_shachongdeng(1).png"> </image>
              <p>智慧杀虫灯</p>
            </div>
            <div
              v-if="auth('mobile:home:activate:analyzer:list')"
              class="menu-item"
              @click="toReactive(3)"
            >
              <image src="../../assets/icon_chongqingfenxi(1).png"> </image>
              <p>虫情分析仪</p>
            </div>
            <div
              v-if="auth('mobile:home:activate:machine:list')"
              class="menu-item"
              @click="toReactive(2)"
            >
              <image src="../../assets/icon_shangqing(1).png"> </image>
              <p>墒情气象一体机</p>
            </div>
          </div>
        </template>
      </scroll-view>
    </div>

    <DeviceMap v-if="active === 1"></DeviceMap>

    <div v-if="active === 2" class="user-center">
      <div class="user-info">
        <image src="../../assets/icon_tx.png"></image>
        <div class="user-name">{{ nickname }}</div>
      </div>

      <div @click="toUpdatePassword" class="function-item">
        <image class="icon" src="../../assets/icon_mima.png" alt="" />
        <span>修改密码</span>
        <image class="arrow" src="../../assets/icon_right.png" alt="" />
      </div>

      <nut-button type="primary" class="bottom-btn" size="large" @click="handleLogout"
        >注 销</nut-button
      >
    </div>

    <nut-tabbar v-if="userStore.user.roles?.length" class="bottom-tabbar" v-model="active">
      <nut-tabbar-item v-if="auth('210000')" tab-title="首页" :name="0">
        <template #icon>
          <img :src="active === 0 ? iconHomeActive : iconHome" alt="" />
        </template>
      </nut-tabbar-item>
      <nut-tabbar-item v-if="auth('220000')" tab-title="设备地图" :name="1">
        <template #icon>
          <img :src="active === 1 ? iconDeviceMapActive : iconDeviceMap" alt="" />
        </template>
      </nut-tabbar-item>
      <nut-tabbar-item tab-title="我的" :name="2">
        <template #icon>
          <img :src="active === 2 ? iconMineActive : iconMine" alt="" />
        </template>
      </nut-tabbar-item>
    </nut-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import iconHomeActive from '../../assets/icon_shouye_xz.png'
import iconHome from '../../assets/icon_shouye.png'
import iconMine from '../../assets/icon_wode.png'
import iconMineActive from '../../assets/icon_wode_xz.png'
import iconDeviceMap from '../../assets/tab-icon2.png'
import iconDeviceMapActive from '../../assets/tab-icon2-active.png'
import homeBanner from '../../assets/banner.png'
import Taro from '@tarojs/taro'
import AuthAPI from '../../api/auth'
import DeviceManage from '../deviceManagement/index.vue'
import WorkOrder from '../workOrder/index.vue'
import DeviceMap from '../deviceMap/deviceMap.vue'
import { useUserStore } from '@/store/user'
import { auth } from '@/store/permisson'

const active = ref(2)

watch(active, () => {
  console.log(active.value)
})

const nickname = ref('')

const userStore = useUserStore()
watch(
  () => userStore.user,
  () => {
    if (!userStore.user) return
    nickname.value = userStore.user.nickname
    if (auth('210000')) active.value = 0
    else if (auth('220000')) active.value = 1
    else active.value = 2
  },
  { immediate: true }
)

const handleLogout = () => {
  Taro.showModal({
    title: '提示',
    content: '您确定注销并退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        AuthAPI.logout().then(() => {
          Taro.removeStorageSync('token')
          Taro.navigateTo({
            url: '/pages/login/index',
          })
        })
      }
    },
  })
}

const toUpdatePassword = () => {
  Taro.navigateTo({
    url: '/pages/updatePassword/updatePassword',
  })
}

const toReactive = (type: number) => {
  Taro.navigateTo({ url: '/pages/deviceActivation/index?type=' + type })
}
</script>

<style scoped lang="scss">
.list-content {
  height: calc(100vh - 430px);
}
image {
  width: 109px;
  height: 109px;
}
p {
  font-size: 26px;
}

.group-title {
  font-size: 32px;
  font-weight: bold;
  margin: 30px 30px 25px;
  color: #101010;
}
.home-container {
  height: 100vh;
  background: url('../../assets/home-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.bottom-tabbar {
  position: fixed;
  bottom: 0;
  width: 100%;
}
:deep(.nut-tabbar) {
  padding-bottom: 20px;
  padding-top: 10px;
}
.banner {
  width: 100vw;
  height: 238px;
  img {
    width: 100%;
    height: 100%;
  }
}
.menu-list {
  padding: 40px 0;
  background: #fff;
  width: 690px;
  margin: 0 auto;
  box-sizing: border-box;
  border-radius: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  display: flex;
  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    img {
      width: 130px;
      height: 130px;
    }
    p {
      font-size: 22px;
      margin-top: 24px;
    }
  }
}
.menu-item {
  width: 25%;
}

.user-center {
  width: 100%;
  padding: 30px;
  box-sizing: border-box;
  .user-info {
    background: url('../../assets/banner_bg.png');
    background-size: 106% 106%;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 300px;
    margin-top: 30px;
    margin-bottom: 20px;
    padding: 40px 50px 60px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    image {
      width: 130px;
      height: 130px;
    }
    .user-name {
      font-size: 34px;
      color: #fff;
      margin-left: 30px;
    }
  }
  .function-item {
    width: 100%;
    height: 102px;
    background: #ffffff;
    border-radius: 20px 20px 20px 20px;
    margin-bottom: 150px;
    padding: 0 30px;
    box-sizing: border-box;
    font-size: 30px;
    color: #101010;
    display: flex;
    align-items: center;
    .icon {
      width: 29px;
      height: 32px;
      margin-right: 30px;
    }
    .arrow {
      margin-left: auto;
      width: 16px;
      height: 28px;
    }
  }
}
.bottom-btn {
  width: 100%;
}
</style>
