import request from '../../utils/request'

export default class SoilMoistureAPI {
  // 列出参数配置信息
  static paramSoil(deviceId: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/device/smd/${deviceId}/param`,
      method: 'get',
    })
  }

  // 图表数据
  static chartSoil(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/device/smd/${data.deviceId}/chart/${data.paramCode}/${data.unit}?query=${data.query}`,
      method: 'get',
    })
  }
}
