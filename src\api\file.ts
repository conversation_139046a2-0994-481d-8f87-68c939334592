import { Minio, initMinio, putObject } from 'minio-js'
import dayjs from 'dayjs'
import request from '../utils/request'
import Taro from '@tarojs/taro'

/**
 * 获取凭证
 * */
function getUploadToken() {
  return request<MinioData>({
    url: '/api/v1/files/getCredentials',
    method: 'get',
  })
}

function urlToFile(url) {
  return fetch(url)
    .then((response) => response.blob())
    .then((blob) => new File([blob], 'filename'))
}

export class FileAPI {
  static minioConfig = {}
  static bucket: string

  static minioClient: Minio

  /**
   * 初始化minio配置信息，获取凭证，进入页面后调用(如获取用户信息时)
   * */
  static init() {
    getUploadToken().then((res) => {
      const { port, host, credentials, bucketName } = res
      const endPoint = host.split('//')[1]
      const useSSL = host.startsWith('https')
      this.minioConfig = {
        endPoint,
        port: Number(port),
        useSSL,
        accessKey: credentials.accessKey,
        secretKey: credentials.secretKey,
        sessionToken: credentials.sessionToken,
      }
      this.bucket = bucketName
      this.minioClient = new Minio.Client(this.minioConfig)
    })
  }

  /**
   * 上传文件
   * @param file
   * @param dir 上传目录名
   * */
  static upload = (file: File, dir: string): Promise<FileInfo> => {
    return new Promise(async (resolve, reject) => {
      const reader = new FileReader()
      const ArrayBufferFile = await urlToFile(file.url)

      reader.readAsArrayBuffer(ArrayBufferFile)
      reader.onload = (e) => {
        const res = e.target.result
        initMinio(this.minioConfig)
        const objectName = `${dir}/${dayjs().format('YYYY-MM-DD')}/${encodeURIComponent(file.name)}`
        // 上传
        putObject(this.bucket, res, objectName, (err) => {
          if (err) {
            Taro.showToast({
              title: '上传失败',
              icon: 'none',
            })
            reject(err)
          } else {
            getFileUrl(objectName).then((url) => {
              resolve({ url, name: file.name, objectName })
            })
          }
        })
      }
      reader.onerror = function (error) {
        console.error('文件读取失败:', error, 5655555555)
        // 这里可以添加更多的错误处理逻辑，比如提示用户等
      }
    })
  }

  /**
   * 移除文件
   * */
  static remove = (filePath: string) => {
    console.log(filePath)
    return new Promise((resolve, reject) => {
      const basePath = `/${this.bucket}/`
      const startIndex = filePath.indexOf(basePath) + basePath.length
      const objectName = filePath.slice(Math.max(0, startIndex))
      console.log(basePath, startIndex, objectName)
      this.minioClient
        .removeObject(this.bucket, objectName)
        .then(() => {
          resolve(null)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

/**
 * 私有桶获取可访问url
 * @param objectName 文件objectName
 * @param expiryInSeconds 过期时间（秒），默认一天有效期
 */
export function getFileUrl(objectName: string, expiryInSeconds = 24 * 60 * 60): Promise<string> {
  return FileAPI.minioClient.presignedGetObject(FileAPI.bucket, objectName, expiryInSeconds)
}

export interface FileInfo {
  name: string
  url: string
  objectName?: string
}

export interface MinioConfig {
  endPoint: string
  port: number
  useSSL: boolean
  accessKey: string
  secretKey: string
  sessionToken: string
}

export interface MinioData {
  host: string
  port: string
  bucketName: string
  credentials: MinioDataCredentials
}
export interface MinioDataCredentialsExpiration {}
export interface MinioDataCredentials {
  accessKey: string
  secretKey: string
  sessionToken: string
  expiration: MinioDataCredentialsExpiration
  expired: boolean
}
