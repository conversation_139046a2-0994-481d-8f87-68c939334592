<template>
  <div class="login-container">
    <CustomNavTitle title="忘记密码" :showBack="true" />

    <nut-form
      ref="formRef"
      class="form-container"
      :model-value="codeValidateOk ? resetForm : formData"
      :rules="rules"
    >
      <!-- 第一步：手机号和验证码 -->
      <template v-if="!codeValidateOk">
        <p class="label-text">请输入手机号</p>
        <nut-form-item :label-width="80" prop="username">
          <nut-input
            v-model="formData.username"
            :adjust-position="false"
            placeholder="请输入"
            type="text"
            @blur="customBlurValidate('username')"
          >
          </nut-input>
        </nut-form-item>

        <p class="label-text">请输入验证码</p>
        <nut-form-item :label-width="80" prop="password">
          <nut-input :adjust-position="false" v-model="formData.password" placeholder="请输入">
            <template #right>
              <div class="getCode" @click="getVerificationCode">
                {{ countDownText }}
              </div>
            </template>
          </nut-input>
        </nut-form-item>

        <nut-button class="login-button" type="success" size="large" @click="submit"
          >下一步</nut-button
        >
      </template>

      <!-- 第二步：设置新密码 -->
      <template v-else>
        <p class="label-text">请输入新密码</p>
        <nut-form-item :label-width="80" prop="newPassword">
          <nut-input
            :adjust-position="false"
            :type="pwdVisible1 ? 'text' : 'password'"
            v-model="resetForm.newPassword"
            placeholder="请输入新密码"
            @blur="customBlurValidate('newPassword')"
          >
            <template #right>
              <img
                :src="pwdVisible1 ? pwdVisibleIcon : pwdUnVisibleIcon"
                class="visible-icon"
                @click="pwdVisible1 = !pwdVisible1"
              />
            </template>
          </nut-input>
        </nut-form-item>

        <p class="label-text">请确认新密码</p>
        <nut-form-item :label-width="80" prop="confirmPassword">
          <nut-input
            :adjust-position="false"
            :type="pwdVisible ? 'text' : 'password'"
            v-model="resetForm.confirmPassword"
            placeholder="请再次输入新密码"
            @blur="customBlurValidate('confirmPassword')"
          >
            <template #right>
              <img
                :src="pwdVisible ? pwdVisibleIcon : pwdUnVisibleIcon"
                class="visible-icon"
                @click="pwdVisible = !pwdVisible"
              />
            </template>
          </nut-input>
        </nut-form-item>

        <nut-button class="login-button" type="success" size="large" @click="resetPassword"
          >提交</nut-button
        >
      </template>
    </nut-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import AuthAPI from '../../api/auth'
import type { LoginData } from '../../api/auth'
import Taro from '@tarojs/taro'
import CustomNavTitle from '../../components/CustomNavTitle/CustomNavTitle.vue'
import pwdVisibleIcon from '../../assets/icon_kejian.png'
import pwdUnVisibleIcon from '../../assets/icon_bukejian.png'

const formData = ref<LoginData>({
  username: '',
  password: '',
  authType: '0',
})

const pwdVisible = ref(false)
const pwdVisible1 = ref(false)

// 重置密码表单
const resetForm = reactive({
  newPassword: '',
  confirmPassword: '',
})

const formRef = ref<any>(null)

const codeValidateOk = ref(false)

// 倒计时相关变量
const countDown = ref(0)
const isCountingDown = computed(() => countDown.value > 0)
const countDownText = computed(() =>
  isCountingDown.value ? `${countDown.value}秒后重试` : '获取验证码'
)

// 验证码倒计时
let timer: number | null = null

// 开始倒计时
const startCountDown = () => {
  countDown.value = 60
  timer = window.setInterval(() => {
    countDown.value--
    if (countDown.value <= 0) {
      clearInterval(timer!)
      timer = null
    }
  }, 1000)
}

// 获取验证码
const getVerificationCode = async () => {
  if (isCountingDown.value) return
  // 先校验手机号
  try {
    const { valid } = await formRef.value?.validate('username')
    if (valid && formData.value.username) {
      // 调用发送验证码接口
      await AuthAPI.setPhoneCode({
        phone: formData.value.username,
        type: 'forget-password',
      })

      // 发送成功后开始倒计时
      startCountDown()

      Taro.showToast({
        title: '验证码发送成功',
        icon: 'success',
        duration: 3000,
      })
    }
  } catch (error) {
    console.error('手机号验证失败:', error)
  }
}

const rules = {
  username: [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
  ],
  password: [
    { required: true, message: '请输入验证码' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
    // { min: 6, message: '密码至少6个字符' },
    { validator: asyncValidator, message: '密码至少8个字符' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (val: string) => val === resetForm.newPassword,
      message: '两次密码输入不一致',
    },
  ],
}

function asyncValidator(val) {
  const weakPasswords = ['admin321', '12345678', '87654321', 'admin123', 'root1234']
  // 至少包含两种字符类型
  const types = [
    /[a-z]/, // 小写
    /[A-Z]/, // 大写
    /[0-9]/, // 数字
    /[~!@#$%^&*()\-=+\|\[\]:"<>,\.\/\?]/, // 特殊字符
  ]
  let count = 0
  types.forEach((reg) => {
    if (reg.test(val)) count++
  })
  return new Promise((resolve, reject) => {
    console.log('模拟异步验证中...')
    setTimeout(() => {
      if (val.length < 8) {
        reject('密码至少8个字符')
      } else if (weakPasswords.includes(val.toLowerCase())) {
        reject('密码过于简单，请勿使用弱口令')
      } else if (count < 2) {
        reject('密码必须包含大写字母、小写字母、数字、特殊字符中至少两种组合')
      } else {
        resolve('')
      }
    }, 1000)
  })
}

const submit = async () => {
  try {
    const { valid } = await formRef.value?.validate()
    if (valid && formData.value.username && formData.value.password) {
      // 调用验证验证码接口
      try {
        await AuthAPI.checkPhoneCode({
          phone: formData.value.username,
          phoneCode: formData.value.password,
          type: 'forget-password',
        })

        codeValidateOk.value = true
      } catch (error) {
        console.error('验证码验证失败:', error)
        Taro.showToast({
          title: '验证码错误',
          icon: 'error',
          duration: 3000,
        })
      }
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置密码
const resetPassword = async () => {
  try {
    // const { valid } = await formRef.value?.validate(['newPassword', 'confirmPassword'])
    formRef.value?.validate().then(async ({ valid, errors }) => {
      if (valid) {
        // 调用重置密码接口
        await AuthAPI.resetCaptcha({
          phone: formData.value.username,
          phoneCode: formData.value.password,
          password: resetForm.newPassword,
          confirmPassword: resetForm.confirmPassword,
        })

        Taro.showToast({
          title: '密码重置成功',
          icon: 'success',
          duration: 3000,
        })

        // 跳转到登录页面
        setTimeout(() => {
          Taro.reLaunch({
            url: '/pages/login/index',
          })
        }, 2000)
      }
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 失去焦点校验
const customBlurValidate = (prop: string) => {
  formRef.value?.validate(prop).then(({ valid, errors }: { valid: boolean; errors: any[] }) => {
    if (valid) {
      console.log('success:', formData.value)
    } else {
      console.warn('error:', errors)
    }
  })
}
</script>

<style lang="scss" scoped>
.label-text {
  font-size: 30px;
  padding-bottom: 30px;
  padding-left: 30px;
}
.login-container {
  width: 100%;
  height: 100%;
  background: url('../../assets/<EMAIL>');
  background-size: 100% 100%;
  box-sizing: border-box;
  .form-container {
    padding: 50px;
    padding-top: 300px;
  }
}

.eye-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  height: 100%;
}

:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 100%;
  height: 98px;
  margin: 0 auto 70px;
  border-radius: 20px;
}
.visible-icon {
  width: 38px;
  height: 30px;
}
.login-button {
  background: linear-gradient(270deg, #06bb6c 0%, #019e59 100%);
  box-shadow: 0px 8px 14px 1px rgba(1, 101, 61, 0.1);
}
.getCode {
  color: #01a463;
}
</style>
