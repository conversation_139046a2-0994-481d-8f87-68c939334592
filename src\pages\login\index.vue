<template>
  <div class="login-container">
    <div class="logo">
      <img :src="LogoImage" alt="logo" />
      <p class="logo-title">物华智慧农业平台</p>
    </div>

    <CustomTabs
      :tab-list="tabList"
      v-model="formData.authType"
      @change="tabChange"
      :vertical-screen="false"
    />
    <nut-form ref="formRef" :model-value="formData" :rules="rules">
      <!-- 密码登录 -->
      <template v-if="formData.authType === 0">
        <nut-form-item style="margin-bottom: 40px" :label-width="80" prop="username">
          <nut-input
            :adjust-position="false"
            v-model="formData.username"
            placeholder="请输入账号"
            type="text"
            @blur="customBlurValidate('username')"
          >
            <template #left>
              <span class="label-text">
                <img class="login-label-icon" :src="AccountIcon" />
                <span class="line"></span>
              </span>
            </template>
          </nut-input>
        </nut-form-item>
        <nut-form-item style="margin-bottom: 40px" :label-width="80" prop="password">
          <nut-input
            :adjust-position="false"
            v-model="formData.password"
            placeholder="请输入密码"
            :type="pwdVisible ? 'text' : 'password'"
            @blur="customBlurValidate('password')"
          >
            <template #left>
              <span class="label-text">
                <img class="login-label-icon" :src="PasswordIcon" />
                <span class="line"></span>
              </span>
            </template>
            <template #right>
              <img
                :src="pwdVisible ? pwdVisibleIcon : pwdUnVisibleIcon"
                class="visible-icon"
                @click="pwdVisible = !pwdVisible"
              />
            </template>
          </nut-input>
        </nut-form-item>
      </template>

      <!-- 验证码登录 -->
      <template v-else>
        <nut-form-item style="margin-bottom: 40px" :label-width="80" prop="phone">
          <nut-input
            :adjust-position="false"
            v-model="formData.phone"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            @blur="customBlurValidate('phone')"
          >
            <template #left>
              <span class="label-text">
                <img class="login-label-icon" :src="AccountIcon" />
                <span class="line"></span>
              </span>
            </template>
          </nut-input>
        </nut-form-item>
        <nut-form-item style="margin-bottom: 40px" :label-width="80" prop="phoneCode">
          <nut-input
            :adjust-position="false"
            v-model="formData.phoneCode"
            placeholder="请输入验证码"
            type="number"
            maxlength="6"
            @blur="customBlurValidate('phoneCode')"
          >
            <template #left>
              <span class="label-text">
                <img class="login-label-icon" :src="captchaIcon" />
                <span class="line"></span>
              </span>
            </template>
            <template #right>
              <div class="send-code" :class="{ disabled: counting }" @click="sendCode">
                {{ counting ? `${countdown}s后重新获取` : '获取验证码' }}
              </div>
            </template>
          </nut-input>
        </nut-form-item>
      </template>

      <nut-form-item v-if="showCode" :label-width="80" prop="captchaCode">
        <nut-input
          :adjust-position="false"
          v-model="formData.captchaCode"
          placeholder="请输入验证码"
          type="text"
          @blur="customBlurValidate('captchaCode')"
        >
          <template #left>
            <span class="label-text">
              <img class="login-label-icon" :src="captchaIcon" />
              <span class="line"></span>
            </span>
          </template>
          <template #right>
            <div @click="getCaptcha">
              <image class="captcha" :src="captchaBase64" />
            </div>
          </template>
        </nut-input>
      </nut-form-item>

      <div class="login-options" v-if="formData.authType === 0">
        <nut-checkbox :icon-size="16" v-model="rememberPassword">记住密码</nut-checkbox>
        <div class="forget-password" @click="goForgetPassword">忘记密码？</div>
      </div>

      <nut-button class="login-button" type="success" size="large" @click="submit">登录</nut-button>
    </nut-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import AuthAPI from '../../api/auth'
import type { LoginData } from '../../api/auth'
import LogoImage from '../../assets/<EMAIL>'
import AccountIcon from '../../assets/icon_zhanghao.png'
import PasswordIcon from '../../assets/icon_mima.png'
import captchaIcon from '../../assets/icon_yanzhengma.png'
import pwdVisibleIcon from '../../assets/icon_kejian.png'
import pwdUnVisibleIcon from '../../assets/icon_bukejian.png'
import Taro from '@tarojs/taro'
import { useUserStore } from '@/store/user'
import { FileAPI } from '@/api/file'
import CustomTabs from '@/components/CustomTabs.vue'

const formData = ref<LoginData>({
  username: '',
  password: '',
  authType: 0,
  phone: '',
  phoneCode: '',
  captchaKey: '',
  captchaCode: '',
})

const pwdVisible = ref(false)
const rememberPassword = ref(false)
const counting = ref(false)
const countdown = ref(60)

// 验证密码登录功能块
const tabList = [
  { title: '密码登录', paneKey: 0 },
  { title: '验证码登录', paneKey: 1 },
]

const formRef = ref<any>(null)

const rules = {
  username: [{ required: true, message: '请输入账号' }],
  password: [
    { required: true, message: '请输入密码' },
    // {
    //   validator: (value) => {
    //     // if (!value) return callback()
    //     // 不能和用户名一样
    //     if (value === formData.value.username) {
    //       return Promise.reject('密码不能和用户名一样')
    //     }
    //     return Promise.resolve()
    //   },
    // },

    //     // 弱口令检测
    //     const weakPasswords = ['admin321', '12345678', '87654321', 'admin123', 'root1234']
    //     if (weakPasswords.includes(value.toLowerCase())) {
    //       return Promise.reject('密码过于简单，请勿使用弱口令')
    //     }

    //     // 至少包含两种字符类型
    //     const types = [
    //       /[a-z]/, // 小写
    //       /[A-Z]/, // 大写
    //       /[0-9]/, // 数字
    //       /[~!@#$%^&*()\-=+\|\[\]:"<>,\.\/\?]/, // 特殊字符
    //     ]
    //     let count = 0
    //     types.forEach((reg) => {
    //       if (reg.test(value)) count++
    //     })
    //     if (count < 2) {
    //       return Promise.reject('密码必须包含大写字母、小写字母、数字、特殊字符中至少两种组合')
    //     }

    //     return Promise.resolve()
    //   },
    //   trigger: 'blur',
    // },
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    {
      validator: (value) => {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          return Promise.reject('请输入正确的手机号')
        }
        return Promise.resolve()
      },
    },
  ],
  phoneCode: [
    { required: true, message: '请输入验证码' },
    {
      validator: (value) => {
        if (!/^\d{6}$/.test(value)) {
          return Promise.reject('验证码为6位数字')
        }
        return Promise.resolve()
      },
    },
  ],
}

// 监听登录方式切换
watch(
  () => formData.value.authType,
  () => {
    formRef.value?.reset()
  }
)

// 发送验证码
const sendCode = async () => {
  if (counting.value) return

  try {
    // 只验证手机号字段
    const { valid } = await formRef.value?.validate('phone')
    if (!valid) return

    counting.value = true
    countdown.value = 60

    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        counting.value = false
      }
    }, 1000)

    await AuthAPI.setPhoneCode({
      phone: formData.value.phone || '',
      type: 'login',
    })

    Taro.showToast({
      title: '验证码已发送',
      icon: 'success',
    })
  } catch (error) {
    console.error('发送验证码失败', error)
  }
}

/**
 * 获取验证码
 * */
// 验证码图片Base64字符串
const captchaBase64 = ref()
function getCaptcha() {
  AuthAPI.getCaptcha().then((data: any) => {
    formData.value.captchaKey = data.captchaKey
    captchaBase64.value = data.captchaBase64
  })
}
getCaptcha()

const userStore = useUserStore()
// 初始化时检查是否有保存的账号密码
onMounted(() => {
  const savedUsername = Taro.getStorageSync('savedUsername')
  const savedPassword = Taro.getStorageSync('savedPassword')
  if (savedUsername && savedPassword) {
    formData.value.username = savedUsername
    formData.value.password = savedPassword
    rememberPassword.value = true
  }
})

const showCode = ref(false)
const submit = async () => {
  const { valid } = await formRef.value?.validate()
  if (valid) {
    let loginParams: LoginData

    if (formData.value.authType === 0) {
      // 密码登录
      loginParams = {
        authType: 0,
        username: formData.value.username,
        password: formData.value.password,
        captchaKey: formData.value.captchaKey || '',
        captchaCode: formData.value.captchaCode || '',
      }
    } else {
      // 验证码登录
      loginParams = {
        authType: 1,
        username: '',
        password: '',
        phone: formData.value.phone || '',
        phoneCode: formData.value.phoneCode || '',
        captchaKey: formData.value.captchaKey || '',
        captchaCode: formData.value.captchaCode || '',
      }
    }

    try {
      const res = await AuthAPI.login(loginParams)

      // 处理记住密码
      if (rememberPassword.value) {
        Taro.setStorageSync('savedUsername', formData.value.username)
        Taro.setStorageSync('savedPassword', formData.value.password)
      } else {
        Taro.removeStorageSync('savedUsername')
        Taro.removeStorageSync('savedPassword')
      }

      // 登录成功后的处理
      Taro.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000,
      })
      Taro.setStorageSync('token', res.accessToken)
      userStore.getUserInfo()
      FileAPI.init()

      Taro.navigateTo({
        url: '/pages/home/<USER>',
      })
    } catch (err) {
      showCode.value = err
      getCaptcha()
    }
  }
}

// 失去焦点校验
const customBlurValidate = (prop) => {
  formRef.value?.validate(prop).then(({ valid, errors }) => {
    if (valid) {
      console.log('success:', formData.value)
    } else {
      console.warn('error:', errors)
    }
  })
}

const goForgetPassword = () => {
  Taro.navigateTo({
    url: '/pages/forgetPassword/index',
  })
}
</script>

<style lang="scss" scoped>
:deep(.nut-checkbox__icon) {
  position: relative;
  top: 3px;
}
:deep(.nut-tabs__titles) {
  background: transparent !important;
  margin-bottom: 30px;
}
:deep(.nut-tabs__list) {
  background: transparent !important;
}
.captcha {
  width: 200px;
  height: 50px;
}
.login-container {
  width: 100vw;
  min-height: 100vh;
  padding: 50px;
  background: url('../../assets/<EMAIL>');
  background-size: 100% auto;
  padding-top: 300px;
  box-sizing: border-box;
}
.logo {
  margin: -100px auto 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 245px;
    height: 62px;
  }
  .logo-title {
    font-size: 38px;
    font-weight: 600;
    color: #333;
    margin-top: 55px;
  }
}
.label-text {
  .login-label-icon {
    width: 35px;
    height: 40px;
    margin-left: 25px;
  }
  .line {
    display: inline-block;
    margin: 0 25px;
    width: 1px;
    height: 40px;
    border-left: 1px solid #d4d4d4;
    position: relative;
    top: 3px;
  }
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 100%;
  height: 98px;
  margin: 0 auto 70px;
  border-radius: 20px;
}
.visible-icon {
  width: 38px;
  height: 30px;
}
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50px;
  padding: 0 10px;

  :deep(.nut-checkbox) {
    .nut-checkbox__label {
      font-size: 22px;
      color: #555555;
    }
  }
}
.forget-password {
  font-size: 22px;
  color: #555555;
}
.login-button {
  background: linear-gradient(270deg, #06bb6c 0%, #019e59 100%);
  box-shadow: 0px 8px 14px 1px rgba(1, 101, 61, 0.1);
}
.send-code {
  font-size: 24px;
  color: #019e59;
  padding: 0 10px;

  &.disabled {
    color: #999;
  }
}
</style>
