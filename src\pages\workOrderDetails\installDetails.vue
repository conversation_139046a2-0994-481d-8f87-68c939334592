<template>
  <Scanner v-if="showScanner" @scanResult="handleScanResult" />
  <PositionSelect
    v-if="showPositionSelect"
    :showSearch="showSearch"
    @close="showPositionSelect = false"
    @change="getPosition"
    :modelValue="form.coordinate"
  ></PositionSelect>
  <div v-if="!showPositionSelect && !showScanner" class="page-content">
    <CustomNavTitle title="安装工单详情" />

    <div class="card">
      <div class="title">
        <span>工单号：{{ info?.workOrderNo || '--' }}</span>
        <span class="count">{{
          `${info?.handlerDeviceCount || 0}/${info?.deviceCount || 0}`
        }}</span>
      </div>
      <div class="content">
        <div>
          <span class="label">设备名称</span>
          <span class="value">{{ info?.order?.deviceTypeName || '--' }}</span>
        </div>
        <div>
          <span class="label">设备型号</span>
          <span class="value">{{ info?.order?.deviceModelName || '--' }}</span>
        </div>
        <div>
          <span class="label">客户</span>
          <span class="value">{{ info?.order?.customerName || '--' }}</span>
        </div>
        <div>
          <span class="label">客户地址</span>
          <span class="value btn" @click="showAddress()">{{ info?.order?.address }}</span>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="sub-title">设备编号</div>

      <nut-input
        :disabled="showHandle"
        v-model="deviceNo"
        placeholder="输入设备编号搜索设备"
        clearable
      >
        <template #right>
          <Scan2
            color="#909090"
            @click="
              () => {
                if (!showHandle) {
                  showScanner = true
                }
              }
            "
          />
        </template>
      </nut-input>

      <scroll-view
        v-show="!showHandle"
        :scroll-y="true"
        v-if="filteredDeviceList.length > 0"
        class="scroll-view"
      >
        <div
          class="device-item"
          v-for="item in filteredDeviceList"
          :key="item.id"
          @click="handleDeviceClick(item)"
        >
          <div class="device-item-left-title">{{ item.deviceNo }}</div>
          <div class="status" :class="{ uninstalled: item.handlerStatus === 0 }">
            {{ item.handlerStatus === 1 ? '已安装' : '未安装' }}
          </div>
        </div>
      </scroll-view>
      <!-- <nut-empty
        v-if="filteredDeviceList.length === 0 && !showHandle"
        description="暂无数据"
      ></nut-empty> -->
      <emput v-if="filteredDeviceList.length === 0 && !showHandle" message="暂无数据" />

      <div v-if="showHandle">
        <div class="sub-title">安装地址</div>
        <nut-input v-model="form.address" disabled placeholder="定位获取或地图选择">
          <template #right>
            <div class="custom-button" @click="handleSelectAddress">选择</div>
          </template>
        </nut-input>

        <div class="sub-title">
          <span>完成图片</span>
          <span class="upload-tip">最多3张</span>
        </div>
        <div class="upload-list">
          <FileUpload v-model="fileList" :limit="3" />
        </div>
      </div>
    </div>

    <div class="bottom-buttons gap-x-4">
      <nut-button v-if="showHandle" type="default" class="flex-1" @click="handleCancel"
        >取消</nut-button
      >
      <nut-button
        v-if="showHandle && auth('mobile:home:workOrder:install:install')"
        class="flex-1"
        type="primary"
        @click="handleSubmit"
        >{{ handlerStatus === 0 ? '确定' : '保存' }}</nut-button
      >
    </div>

    <nut-overlay v-model:visible="showOverlay" :close-on-click-overlay="false"></nut-overlay>
  </div>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref, computed } from 'vue'
import WorkOrderAPI from '../../api/workOrder'
import { Scan2 } from '@nutui/icons-vue-taro'
import Scanner from '../../components/Scanner/Scanner.vue'
import FileUpload from '../../components/Upload/fileUpload.vue'
import { FileAPI } from '../../api/file'
import PositionSelect from '../../components/PositionSelect/PositionSelect.vue'
import { MAP_CONFIG } from '../../config/map'
import gcoord from 'gcoord'
import { auth } from '@/store/permisson'

const info = ref<any>({})
const showSearch = ref(true)
const id = Taro.getCurrentInstance().router?.params?.id

const getInfo = async () => {
  const res = await WorkOrderAPI.getDetail(Number(id))
  info.value = res
}

getInfo()

const deviceNo = ref('')

// 添加计算属性用于过滤设备列表
const filteredDeviceList = computed(() => {
  if (!deviceNo.value) {
    return info.value?.deviceRecordList || []
  }
  return (info.value?.deviceRecordList || []).filter((item: any) =>
    item.deviceNo.toLowerCase().includes(deviceNo.value.toLowerCase())
  )
})

// 查看客户地址
const showAddress = () => {
  showSearch.value = false
  form.value.coordinate = info.value.order.coordinate
  showPositionSelect.value = true
}

const showScanner = ref(false)

const handleScanResult = (result: string) => {
  showScanner.value = false
  const urlParams = new URLSearchParams(result.split('?')[1])
  const serialNum = urlParams.get('serialNum')
  if (!serialNum) {
    Taro.showModal({
      title: '提示',
      content: '设备未激活，请前往首页-设备激活模块进行设备激活，注意对应的批次编号。',
      showCancel: false,
    })

    return
  }
  deviceNo.value = serialNum || ''
  const device = filteredDeviceList.value.find((item: any) => item.deviceNo === result)
  if (device) {
    handleDeviceClick(device)
  }
}

const showHandle = ref(false)

const fileList = ref<any[]>([])
const handlerStatus = ref(0)
const showOverlay = ref(false)
const handleDeviceClick = (item: any) => {
  if (!auth('mobile:home:workOrder:install:install') && item.handlerStatus !== 1) {
    Taro.showToast({ title: '暂无安装权限', icon: 'none' })
    return
  }
  deviceNo.value = item.deviceNo
  showHandle.value = true

  handlerStatus.value = item.handlerStatus
  form.value.deviceId = item.deviceId
  form.value.id = item.id

  if (item.handlerStatus === 1) {
    // 已安装设备进入编辑状态
    form.value.address = item.address
    form.value.coordinate = item.coordinate
    fileList.value = item.handlerImage.map((item: any, index) => {
      return {
        name: item.name,
        url: item.url,
        index,
      }
    })
  } else {
    form.value.address = null
    fileList.value = []
    // 未安装设备获取当前定位作为安装地址

    const geolocation =
      navigator.geolocation ||
      window.Geolocation ||
      window.webkitGeolocation ||
      window.mozGeolocation

    if (geolocation) {
      Taro.showLoading({
        title: '定位中...',
      })
      try {
        showOverlay.value = true
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords
            const result = gcoord.transform(
              [longitude, latitude], // 经纬度坐标
              gcoord.WGS84, // 当前坐标系
              gcoord.BD09 // 目标坐标系
            )
            form.value.coordinate.longitude = longitude
            form.value.coordinate.latitude = latitude
            const response = await fetch(
              `https://api.tianditu.gov.cn/geocoder?postStr={"lon":${longitude},"lat":${latitude},"ver":1}&type=geocode&tk=${MAP_CONFIG.TIANDITU_KEY}`
            )
            const data = await response.json()
            form.value.address = data?.result?.formatted_address
            Taro.hideLoading()
            showOverlay.value = false
          },
          () => {
            if (!showPositionSelect.value) {
              Taro.showModal({
                title: '提示',
                content: '获取定位失败，请手动选择安装地址',
                showCancel: false,
              })
            }
            Taro.hideLoading()
            showOverlay.value = false
          },
          { timeout: 3 * 1000 }
        )
      } finally {
        Taro.hideLoading()
        showOverlay.value = false
      }
    } else {
      Taro.hideLoading()
      showOverlay.value = false
      Taro.showModal({
        title: '提示',
        content: '当前浏览器不支持定位获取，请手动选择安装地址',
        showCancel: false,
      })
    }
  }
}

const showPositionSelect = ref(false)

const handleSelectAddress = () => {
  showPositionSelect.value = true
  showSearch.value = true
}

const form = ref<any>({
  deviceWorkOrderId: '',
  deviceId: '',
  handlerImage: [],
  address: null,
  coordinate: {
    longitude: '',
    latitude: '',
  },
})

const submitLoading = ref(false)
const handleSubmit = async () => {
  form.value.handlerImage = fileList.value.map((item) => {
    return {
      name: item.name,
      url: item.url,
    }
  })
  form.value.deviceWorkOrderId = info.value.id

  submitLoading.value = true
  try {
    const fn = handlerStatus.value === 0 ? WorkOrderAPI.completeInstall : WorkOrderAPI.updateInstall
    if (!form.value.coordinate.latitude) {
      form.value.coordinate = null
    }
    await fn(form.value)
    Taro.showToast({
      title: handlerStatus.value === 0 ? '安装成功' : '修改成功',
      icon: 'success',
    })
    deviceNo.value = ''
    showHandle.value = false
    fileList.value = []
    getInfo()
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  showHandle.value = false
  fileList.value = []
  deviceNo.value = ''
}

const getPosition = (position: any) => {
  form.value.coordinate.longitude = position.coordinate.longitude
  form.value.coordinate.latitude = position.coordinate.latitude
  form.value.address = position.address || null
}

FileAPI.init()
</script>

<style scoped lang="scss">
image {
  width: 110px;
  height: 110px;
}
.bottom-buttons {
  width: 690px;
  position: absolute;
  left: 50%;
  bottom: 50px;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  .nut-button {
    width: 300px !important;
  }
}
.card {
  width: 690px;
  background: #ffffff;
  border-radius: 20px;
  padding: 40px 30px;
  margin: 30px auto;
  box-sizing: border-box;
  .title {
    height: 75px;
    border-bottom: 1px solid #e7e9ee;
    display: flex;
    justify-content: space-between;
    font-size: 32px;
    font-weight: bold;
    align-items: center;
    padding-bottom: 30px;
    box-sizing: border-box;
    margin-bottom: 35px;
    .count {
      color: #019e59;
    }
  }
  .sub-title {
    font-size: 28px;
    color: #5b5b5b;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .upload-tip {
      color: #909090;
      font-size: 24px;
    }
  }
  .content {
    font-size: 28px;

    > div {
      margin-bottom: 20px;
    }
    .span2 {
      display: flex;
      justify-content: space-between;
      > div {
        display: flex;
      }
    }
    > div {
      display: flex;
    }
    .label {
      color: #909090;
      width: 120px;
      text-align: justify;
      text-align-last: justify;
      margin-right: 8px;
    }
    .value {
      color: #101010;
      flex: 1;
      text-align: right;
    }
    .btn {
      color: #019e59;
    }
  }
  .custom-button {
    font-size: 28px;
    color: #019e59;
    width: 100px;
    height: 46px;
    background: #ecfcf1;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #019e59;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

:deep(.nut-input) {
  margin: 35px 0;
  box-sizing: border-box;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 10px 20px;
  .nut-input-inner {
    height: 58px;
  }
}
.scroll-view {
  height: calc(100vh - 910px);
  .device-item {
    width: 627px;
    height: 78px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #d4d4d4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 35px;
    padding: 30px 20px;
    box-sizing: border-box;
    font-size: 28px;
    .status {
      color: #019e59;
      &.uninstalled {
        color: #909090;
      }
    }
  }
}
.upload-list {
  width: 630px;
  height: 166px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d4d4d4;
  margin-top: 24px;
  padding: 28px;
  box-sizing: border-box;
  font-size: 28px;
}
</style>
