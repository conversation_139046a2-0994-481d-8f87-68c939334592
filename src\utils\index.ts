/**
 * 重构地图的flyTo方法，使其支持像素偏移
 * */
export function flyToWithPixelOffset(map, latitude, longitude, zoom, offsetX = 0, offsetY = 0) {
  if (offsetX === 0 && offsetY === 0) {
    map.flyTo([latitude, longitude], zoom)
    return
  }

  const targetPoint = map.latLngToContainerPoint([latitude, longitude])
  const offsetPoint = BM.point(targetPoint.x - offsetX, targetPoint.y - offsetY)
  const offsetLatLng = map.containerPointToLatLng(offsetPoint)
  map.flyTo([offsetLatLng.lat, offsetLatLng.lng], zoom)
}
