<template>
  <!-- <div class="nut-empty">
    <div class="nut-empty__box">
      <img class="nut-empty__box--img empty-image" :src="imageUrl" alt="" />
    </div>
    <div class="nut-empty__description empty-text">{{ message }}</div>
  </div> -->
  <div :class="showBg?'bg-emput':''" >
  <div class="empty-container">
    <img class="empty-image" :src="imageUrl" alt="" />
      <p class="empty-text">{{ message }}</p>
    </div>  
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import homeBanner from '@/assets/icon_zanwu.png'

// 父组件传递的提示语和背景图路径
defineProps({
  message: {
    type: String,
    default: '暂无数据',
  },
  imageUrl: {
    type: String,
    default: homeBanner, // 默认本地图片路径
  },
  showBg: {
    type: <PERSON>olean,
    default: true, // 默认显示背景
  },
});
</script>

<style scoped>
.bg-emput{
  background-color: #fff;
  border-radius: 20px;
  width: 690px;
  margin: 30px auto;
}
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 88px 20px;
  text-align: center;
  /* margin-top: 50px; */
}

.empty-image {
  width: 212px;
  height: 188px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 30px;
}

.empty-text {
  font-weight: 500;
  font-size: 28px;
  color: #000000;
}
</style>