# VImage 组件

## 简介

VImage 是一个用于处理私有化访问图片的 Vue3 组件，它会自动将私有图片 URL 转换为临时可访问的 URL，并提供了图片预览功能。基于taro原生image组件，其用法与原生一致。

## 使用方法

```vue
<template>
  <VImage
    :src="imageUrl"
    :preview-src-list="imageList"
  />
</template>

<script setup lang="ts">
import VImage from '@/components/VImage/VImage'

const imageUrl = 'your-private-image-url'
const imageList = ['image-url-1', 'image-url-2', 'image-url-3']
</script>
```

## 属性

| 属性名              | 类型     | 默认值 | 说明                                                                                  |
| ------------------- | -------- | ------ | ------------------------------------------------------------------------------------- |
| src                 | string   | -      | 图片资源地址，会自动转换为可访问 URL                                                  |
| previewSrcList      | string[] | []     | 预览图片列表，点击图片时可浏览的图片集合                                              |
| showMenuByLongpress | boolean  | false  | 是否开启长按图片显示菜单                                                              |

其他属性会通过 `v-bind="$attrs"` 传递到内部的 `<image>` 元素。
#### 更多属性请参考 [Taro Image API](https://docs.taro.zone/docs/components/media/image)

## 图片模式说明

- `scaleToFill`：不保持纵横比缩放图片，使图片完全适应
- `aspectFit`：保持纵横比缩放图片，使图片的长边能完全显示出来（可能会有空白）
- `aspectFill`：保持纵横比缩放图片，只保证图片的短边能完全显示出来（可能会裁剪）
- `widthFix`：宽度不变，高度自动变化，保持原图宽高比不变
- `heightFix`：高度不变，宽度自动变化，保持原图宽高比不变

## 事件

组件内部会监听点击事件，当点击图片时会触发预览功能，使用 Taro.previewImage API 来预览图片。

## 工作原理

1. 组件接收私有的图片 URL 作为输入
2. 使用 `getFileUrl` API 将私有 URL 转换为可访问的临时 URL
3. 监听图片点击事件，使用 Taro.previewImage 实现图片预览
4. 如果提供了 previewSrcList，则可以在预览模式下左右滑动浏览多张图片

## 使用场景

- 显示需要权限访问的私有图片
- 需要图片预览功能的场景
- 图片集合展示与预览
