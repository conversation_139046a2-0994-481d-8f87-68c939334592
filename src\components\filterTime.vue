<template>
  <div class="filterBox">
    <div class="chooseContent">
     <div class="title">更新时间</div>
     <!-- <div class="content">
      <div v-for="(item,index) in statusList" :key="index" class="nameBtn" :class="statusIndex===item.id?'nameBtnActive':''" @click="statusClick(item.id,'statusIndex')">
        {{ item.name }}
      </div>
     </div> -->
     <div class="updateTime" @click="openDate">
      <div class="deteTime" :style="{ color: date[0] ?'':'#A1A3A1' }">{{date[0] || '开始时间'}}</div>
      <div>~</div>
      <div class="deteTime" :style="{ color: date[1] ?'':'#A1A3A1' }">{{date[1] || '结束时间'}}</div>
      <img :src="rili" alt="" class="rili">
     </div>

     <div class="title">病虫数量</div>
     <div class="updateTime">
      <div class="deteTime">
        <!-- {{ filterData.insectCountStart }} -->
      <nut-input  
        v-model="insectCountStart" 
        placeholder="开始数量" 
        @blur="changeCountStart"
        @update:model-value="insectCountStart = insectCountStart.replace(/[^0-9]/g, '')"
      />
      </div>
      <div>~</div>
      <div class="deteTime">
        <!-- {{ filterData.insectCountEnd}} -->
        <nut-input v-model="insectCountEnd" placeholder="结束数量" @blur="changeCountEnd"  
        @update:model-value="insectCountEnd = insectCountEnd.replace(/[^0-9]/g, '')"/>
      </div>
     </div>
  
    </div>

     <div class="btnFliter">
       <div class="resetBtn" @click="resetClick">重置</div>
       <div class="sureBtn" @click="sureClick">确定</div>
     </div>
  </div>
  <nut-calendar
    v-model:visible="show"
    :default-value="date"
    type="range"
    start-date="2023-01-01"
    end-date="2080-01-01"
    @close="show = false"
    @choose="choose"
    @select="select"
  >
  </nut-calendar>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineEmits } from 'vue'
import { defineProps } from 'vue';
import BaseInfoAPI from '@/api/device/baseInfo'
import rili from '@/assets/icon_rili.png'
import Taro from '@tarojs/taro'


const show = ref(false)
// const date = ref(['2023-01-01', '2023-01-02'])
const date = ref()
const choose = (param) => {
  date.value = [param[0][3], param[1][3]]
  console.log(date.value)
}
const select = (param) => {
  console.log(param)
}


// 更新时间打开，同时将 date 置空
function openDate() {
  show.value = true
}

// // 父组件传递的提示语和背景图路径
// defineProps({
//   showRight: {
//     type: Boolean,
//     default: true, // 默认显示背景
//   },
//   filterData: {
//     type: Object,
//     default: () => ({}) // 默认值为一个空对象
//   }
// });



const emits = defineEmits<{
  (e: 'change', value: any): void
}>()

const statusList=ref([{id:'',name:'全部'},{id:'2',name:'离线'},{id:'1',name:'在线'}])

// 设备状态
const statusIndex=ref('')
const areaIndex=ref('')
const customerIndex=ref('')

const insectCountStart=ref()
const insectCountEnd=ref()

// 在 <script setup> 中，defineProps 只需定义一次，并且可以直接使用 props
const props = defineProps<{
  filterData?: {
    // online: any;
    // areaId: any;
    // customerId: any;
    startTime: any, // 更新开始时间
    endTime: any, // 更新结束时间
    insectCountStart: any, // 昆虫数量开始
    insectCountEnd: any, // 昆虫数量结束
  };
}>();
console.log(props.filterData)

// 从 props 中获取 filterData 的值并赋值给对应的 ref
date.value=[props.filterData?.startTime || '', props.filterData?.endTime || '']
insectCountStart.value=props.filterData?.insectCountStart || '';
insectCountEnd.value=props.filterData?.insectCountEnd || '';


function changeCountStart(val){
  console.log(insectCountStart,'start',val)
  // if(insectCountEnd.value && insectCountStart.value){
  //   if(insectCountEnd.value < insectCountStart.value){
  //     Taro.showToast({
  //     title: '开始数量不能大于结束数量',
  //     icon: 'none',
  //   })
  //   }
  // }
  
}
function changeCountEnd(val){
  console.log(insectCountEnd,'end',val)
  // if(insectCountEnd.value && insectCountStart.value){
  //   if(insectCountEnd.value < insectCountStart.value){
  //     Taro.showToast({
  //     title: '开始数量不能大于结束数量',
  //     icon: 'none',
  //   })
  //   }
  // }
}


// 重置
function resetClick(){
 date.value=[]
 insectCountStart.value=''
 insectCountEnd.value=''
}

// 确定
function sureClick(){
  if(insectCountEnd.value && insectCountStart.value){
    if(Number(insectCountEnd.value) < Number(insectCountStart.value)){
      Taro.showToast({
      title: '开始数量不能大于结束数量',
      icon: 'none',
    })
    return
    }
  }
  const data={
    startTime: date? date.value[0]: '', // 更新开始时间
    endTime: date? date.value[1]: '', // 更新结束时间
    insectCountStart: insectCountStart.value, // 昆虫数量开始
    insectCountEnd: insectCountEnd.value, // 昆虫数量结束

  }
  console.log(data)
  emits('change',data)
}


const areaList=ref()
// 获取下来筛选列表
const getAreaList = async () => {
  areaList.value = await BaseInfoAPI.areaOptions()
  areaList.value.unshift({value:'',label:'全部'})
  // areaList.value.forEach(x => {
  //   x.show=false
  // });
  console.log(areaList)
}
// getAreaList()

const customerList=ref()
// 获取下来筛选列表
const getCustomerList = async () => {
 customerList.value = await BaseInfoAPI.customerOptions()
 customerList.value.unshift({id:'',name:'全部'})
//  customerList.value.forEach(x => {
//   x.show=false
//  }); 
  console.log(areaList)
}
// getCustomerList()

</script>

<style scoped lang="scss">
.filterBox{
  width: 100%;
  padding: 30px;
  box-sizing: border-box;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  .updateTime{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    width: 571px;
    height: 78px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #D4D4D4;
    font-weight: 400;
    font-size: 28px;
    color: #101010;
    .deteTime{
      width: 240px;
      text-align: center;
    }
    .rili{
      width: 30px;
      height: 30px;
    }
  }



  .chooseContent{
    max-height: 88%;
    overflow-y: auto;
  }
  .title{
   font-family: PingFang SC, PingFang SC;
   font-weight: bold;
   font-size: 28px;
   color: #101010;
   margin-top: 40px;
   margin-bottom: 26px;
  }
  .content{
    display: flex;
    flex-wrap: wrap;
    .nameBtn{
      width: 190px;
      height: 58px;
      background: #F7F8FC;
      border-radius:6px;
      margin-right: 22px;
      padding-left:20px ;
      padding-right:20px ;
      box-sizing: border-box;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-weight: 500;
      font-size: 24px;
      color: #19191A;
      margin-bottom: 26px;
      text-align: center;
      line-height: 58px;
    }
    .nameBtn:nth-child(3n){
       margin-right: 0px;
    }
    .nameBtnActive{
      background: rgba(2,164,93,0.1);
      border: 1px solid #02A45D;
      font-weight: bold;
      font-size: 24px;
      color: #02A45D;
    }
  }
}

.btnFliter{
  // width: 100%;
  width: 614px;
  height: 74px;
  line-height: 74px;
  background: #02A45D;
  border-radius: 12px;
  display: flex;
  position: absolute;
  bottom: 50px;
  .resetBtn{
    // width: 50%;
    width: 308px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 12px;
    border: 1px solid #02A45D;
    font-weight: bold;
    font-size: 26px;
    color: #02A45D;
    text-align: center;
  }
  .sureBtn{
    width: 50%;
    font-weight: bold;
    font-size: 26px;
    color: #FFFFFF;
    text-align: center;
  }
}

::v-deep .nut-input{
  width: 100%;
  line-height: 0px;
  font-weight: 400 !important;
  font-size: 28px !important;
}
::v-deep .input-text{
  font-weight: 400 !important;
  font-size: 28px !important;
}

</style>