import path from 'path'
import fs from 'fs'

export default {
  env: {
    NODE_ENV: '"development"',
  },
  defineConstants: {},
  mini: {},
  h5: {
    devServer: {
      https: {
        key: fs.readFileSync(path.join(__dirname, '../ssl/server.key')), // SSL 私钥路径
        cert: fs.readFileSync(path.join(__dirname, '../ssl/server.crt')), // SSL 证书路径
      },
      host: '0.0.0.0', // 允许外部访问
      port: 10086, // 可选: 指定端口
      proxy: {
        '/api/v1': {
          // target: 'http://************:80',
          // target: 'http://************:18991',
          target: 'https://whzl.vankeytech.com:9905',
          changeOrigin: true,
          secure: false, // 如果是https接口，需要配置这个参数
          logLevel: 'debug',
        },
      },
    },
  },
}
