<template>
  <div class="page-content">
      <CustomNavTitle :title="deviceInfo.id?'编辑告警':'新增告警'" :showBack="true" background="#fff" />

      <div class="page-alarm">
        <scroll-view :scroll-y="true" class="scroll-view mb-3">
          <div v-for="(item,index) in alarmItemsData" :key="index">
            <div class="form-box-bg mb-2.4">
              <div class="form-title">
                <span style="color: #f65757; margin-right: 5px">*</span>
                <span>告警项</span>
              </div>
            </div>
            <nut-input
              v-model="item.itemName"
              placeholder="请选择告警项"
              readonly
              @click="handlerPopup(alarmSettingsData, 'itemId', item.itemId, index)"
            >
              <template #right>
                <IconFont name="rect-down" color="#B4BAC1"></IconFont>
              </template>
            </nut-input>
            


            <div class="form-box-bg mb-2.4">
              <div class="form-title">
                <span style="color: #f65757; margin-right: 5px">*</span>
                <span>告警阈值</span>
              </div>
            </div>
            <div v-if="!valueMap[`${item.itemId}`]">
                <nut-input  
                  v-model="item.value" 
                  :max="(item.maxValue || item.maxValue === 0) ?item.maxValue : undefined" 
                  :min="(item.minValue || item.minValue === 0) ?item.minValue : undefined" 
                  :placeholder="
                  (item.minValue || item.minValue === 0 ? `最小值为${item.minValue}` : '') +
                  (item.maxValue || item.maxValue === 0 ? ` 最大值为${item.maxValue}` : '')
                " >
                  <template #right>
                    <div>{{ item.unit }}</div>
                  </template>
                </nut-input> 
            </div>
            <div v-else>
              <nut-input
                v-model="item.valueName"
                placeholder="请选择"
                readonly
                @click="handlerPopup(valueMap[`${item.itemId}`], 'value', item.value, index)"
              >
                <template #right>
                  <IconFont name="rect-down" color="#B4BAC1"></IconFont>
                </template>
              </nut-input>
            </div>

            <div class="form-box-bg mb-2.4">
              <div class="form-title">
                <span style="color: #f65757; margin-right: 5px">*</span>
                <span>条件</span>
              </div>
            </div>
            <nut-input
              v-model="item.operatorName"
              placeholder="请选择"
              readonly
              @click="
                  item.itemId ? handlerPopup(item.options, 'operator', item.operator, index) : ''
                "
            >
              <template #right>
                <IconFont name="rect-down" color="#B4BAC1"></IconFont>
              </template>
            </nut-input>

            <div class="form-box-bg mb-2.4">
              <div class="form-title">
                <span style="color: #f65757; margin-right: 5px">*</span>
                <span>平台推送方式</span>
              </div>
            </div>
            <div class="checkBox">
              <nut-checkbox-group v-model="item.managePushMethod" style="width: 100%;">
                <nut-checkbox :label="1" icon-size="20" style="width:50%;"> 后台 </nut-checkbox> 
                <nut-checkbox :label="2" icon-size="20" style="width: 30%;"> 短信 </nut-checkbox>
              </nut-checkbox-group>
            </div>

            <div class="form-box-bg mb-2.4">
              <div class="form-title">
                <!-- <span style="color: #f65757; margin-right: 5px">*</span> -->
                <span>客户推送方式</span>
              </div>
            </div>
            <div class="checkBox">
              <nut-checkbox-group v-model="item.customerPushMethod" style="width: 100%;">
                <nut-checkbox :label="1" icon-size="20" style="width:50%;"> 后台 </nut-checkbox> 
                <nut-checkbox :label="2" icon-size="20" style="width: 30%;"> 短信 </nut-checkbox>
              </nut-checkbox-group>
            </div>
          </div>

        </scroll-view>

        <nut-button
          class="add-button"
          size="large"
          type="primary"
          :loading="submitLoading"
          @click="submitAlarmSetting"
          >提交</nut-button
        >
      </div>
     <!-- 告警项 -->
    <nut-popup v-model:visible="showPopup" position="bottom">
      <nut-picker
        v-model="choose"
        :columns="optionList"
        :title="option === 'itemId' ? '告警项' : option === 'value' ? '阈值' : '条件'"
        @confirm="chooseOption"
        @cancel=";(showPopup = false), (choose = []), (optionList = [])"
      />
    </nut-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import Taro from '@tarojs/taro'
import { IconFont, Scan2 } from '@nutui/icons-vue-taro'
import BaseInfoAPI from '@/api/device/baseInfo'

// Define props to receive values from the parent component
const props = defineProps({
  // innerModel: {
  //   type: [Number, String],
  //   required: true
  // },
  // id: {
  //   type: [Number, String],
  //   required: true
  // },
  // deviceId: {
  //   type: [Number, String],
  //   required: true
  // },
  deviceInfo: {
    type: Object,
    default: () => ({})
  }
})

console.log(props, 'props')

const emits = defineEmits<{
  (e: 'finished'): void
}>()
/**
 * 获取设备告警项
 */
// const alarmSettingslData = ref() // 告警设置信息
const alarmItemsData = ref<Array<{ 
  id?: string; 
  value?: string | number; 
  itemId?: string | number; 
  operator?: string | number; 
  operatorName?: string; 
  options?: Array<{ value: string | number; text: string }>; 
  managePushMethod?: number[]; 
  customerPushMethod?: number[]; 
  valueName?: string; 
  unit?: string; 
}>>([]) // 告警设置信息

const alarmSettingsData = ref([]) // 设备可配置项
const showPopup = ref(false) // 条件下拉
const options = ref(['等于', '不等于', '大于', '小于', '大于等于', '小于等于', '之间']) // 条件列表
const choose = ref([]) // 通用Picker选项
const optionList = ref([]) // 通用选项列表
const alarmItemIndex = ref() // 打开下拉的item项索引
const option = ref('') // 通用选项item
// const formRefs = reactive<Record<number, any>>({}) // ref
  const formRefs = ref<Array<InstanceType<any>>>(new Array()) // ref
const valueMap = ref<Record<number, any>>({}) // 告警项 valueList



 alarmItemAdd()
 /**
 * 新增告警项行
 */
 function alarmItemAdd() {
  if( props.deviceInfo.id){
    alarmItemsData.value.push({
    ...props.deviceInfo,
    options: props.deviceInfo.supportOperators?.map((val) => {
      return { value: val, text: options.value[val - 1] }
    }) || [],
    operatorName:  options.value[props.deviceInfo.operator - 1] || '' ,
    })
    if (props.deviceInfo.valueList?.length > 0) {
    valueMap.value[`${props.deviceInfo.itemId}`] = props.deviceInfo.valueList.map((val) => {
      return { value: val.value, text: val.label }
    })
    const valueItem = props.deviceInfo.valueList.find(val => val.value === alarmItemsData.value[0].value);
    alarmItemsData.value[0].valueName = valueItem ? valueItem.label : '';
    }
  }else{

  alarmItemsData.value.push({
    id: '',
    value: '',
    itemId: '',
    operator: '',
    operatorName: '',
    // pushChannel: [],
    // pushMethod: [],
    managePushMethod:[],
    customerPushMethod:[],
  })
}

}


const router = Taro.getCurrentInstance().router

// 扫码相关
const showScanner = ref(false)
const handleScanResult = (result: string) => {
  const urlParams = new URLSearchParams(result.split('?')[1])
  const serialNum = urlParams.get('serialNum')
  if (serialNum) {
    formData.value.deviceNo = serialNum
  }
  showScanner.value = false
}

// 表单数据
const formData = ref<SaleRequest>({})

// CommonAPI.customerInfo().then((res) => {
//   formData.value.contactsPhone = res.phone
//   formData.value.contacts = res.name
//   formData.value.address = res.address
// })

/**
 * 问题类型列表
 * */
// const problemTypeOptions = ref<{ text: string; value: number }[]>([])
// CommonAPI.issueList().then((res) => {
//   problemTypeOptions.value = res.map((item) => ({ text: item.label, value: item.value }))
// })
// .............................告警设置 start..........................
/**
 * 检验数组
 */
 function validateList(list) {
  if (list.length > 0) {
    return Promise.resolve()
  } else {
    return false
  }
}

/**
 * 显示选项框
 */
 function handlerPopup(list, item, value, index) {
  console.log(list, item, value, index,'showPopup')
  alarmItemIndex.value = index
  optionList.value = list
  option.value = item
  choose.value = [value]
  showPopup.value = true
}

/**
 * 选择选项
 */
 function chooseOption({ selectedValue, selectedOptions }) {
  console.log(selectedValue, selectedOptions, '选择选项')
  switch (option.value) {
    case 'itemId':
      alarmItemsData.value[alarmItemIndex.value].itemId = selectedValue[0]
      alarmItemsData.value[alarmItemIndex.value].itemName = selectedOptions[0].text
      alarmItemsData.value[alarmItemIndex.value].options = selectedOptions[0].options
      alarmItemsData.value[alarmItemIndex.value].operator = ''
      alarmItemsData.value[alarmItemIndex.value].operatorName = ''
      alarmItemsData.value[alarmItemIndex.value].value = ''
      alarmItemsData.value[alarmItemIndex.value].unit = selectedOptions[0].unit
      // alarmItemsData.value[alarmItemIndex.value].pushChannel = []
      // alarmItemsData.value[alarmItemIndex.value].pushMethod = []
      alarmItemsData.value[alarmItemIndex.value].managePushMethod = []
      alarmItemsData.value[alarmItemIndex.value].customerPushMethod = []
    
      break
    case 'operator':
      alarmItemsData.value[alarmItemIndex.value].operator = selectedValue[0]
      alarmItemsData.value[alarmItemIndex.value].operatorName = String(options.value[selectedValue[0] - 1])
      break
    case 'value':
      alarmItemsData.value[alarmItemIndex.value].value = selectedValue[0]
      alarmItemsData.value[alarmItemIndex.value].valueName = selectedOptions[0].text
      break
    default:
      break
  }
  showPopup.value = false
  choose.value = []
  optionList.value = []
  option.value = ''
  alarmItemIndex.value = ''
}

getDeviceAlarmSetting()
// /**
//  * 根据类型获取设备可配置项
//  */
 function getDeviceAlarmSetting() {
  let data={}
  const deviceInfo=props.deviceInfo
  if(deviceInfo.deviceType===2){
    // 修复 'deviceInfo' 已声明但未使用的问题，确保正确使用该变量
    const deviceInfo = props.deviceInfo;
    data = { 
      modelId: Number(deviceInfo.innerModel), 
      deviceId: deviceInfo.deviceId 
    };
  }else{
    data={ modelId: Number(deviceInfo.innerModel), }
  }
  BaseInfoAPI.getDeviceAlarmItemSetting(data).then((res) => {
    alarmSettingsData.value = res || []
    alarmSettingsData.value.forEach((item) => {
      item.value = item.id
      item.text = item.name
      item.options = item.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      if (item.valueList?.length > 0) {
        valueMap.value[`${item.id}`] = item.valueList.map((val) => {
          return { value: val.value, text: val.label }
        })
      }
    })
    console.log(alarmSettingsData.value,'alarmSettingsData.value')
    // getDeviceAlarmItems()
  })
}

/**
 * 提交告警设置
 */
 const submitLoading = ref(false)
 function submitAlarmSetting() {
  console.log(alarmItemsData.value, '提交告警设置')
  // setConfigItem
  const row=alarmItemsData.value[0]
  if (!row.itemId) {
    Taro.showToast({
      title: '请选择告警项',
      icon: 'none',
    })
    return
  }

  if (!row.value || row.value < 0) {
    Taro.showToast({
      title: '请输入告警阈值',
      icon: 'none',
    })
    return
  }

  if (!row.operator) {
    Taro.showToast({
      title: '请选择条件',
      icon: 'none',
    })
    return
  }
  if (!row.managePushMethod?.length) {
    Taro.showToast({
      title: '请选择平台推送方式',
      icon: 'none',
    })
    return
  }

  const data = {
    id: row.id ? row.id : undefined,
    itemId: row.itemId,
    // itemName: row.itemName,
    value: row.value,
    deviceId: props?.deviceInfo?.deviceId,
    deviceType: props?.deviceInfo?.deviceType,
    operator: row.operator,
    // pushChannel: row.pushChannel,
    // pushMethod: row.pushMethod,
    managePushMethod: row.managePushMethod,
    customerPushMethod: row.customerPushMethod,
  }
  const fn = row.id ? BaseInfoAPI.updateConfigItem : BaseInfoAPI.setConfigItem
  submitLoading.value = true
  fn({ deviceId: props?.deviceInfo?.deviceId, data }).then((res) => {
    submitLoading.value = false
    Taro.showToast({
      title: '操作成功',
      icon: 'success',
    })
    emits('finished')
  }).finally(() => {
    submitLoading.value = false
  })

}



</script>

<style lang="scss" scoped>
.page-alarm{
  padding: 0 30px;
  box-sizing: border-box;
}
.scroll-view {
  height: calc(100vh - 270px);
  box-sizing: border-box;
}

.form-title {
  font-weight: bold;
  font-size: 32px;
  color: #101010;
  margin-top: 30px;
}

.nut-input {
  width: 100%;
  height: 80px;
  background: #ffffff;
  border-radius: 20px;
  --nutui-input-padding: 24px;
  font-size: 28px;
}

:deep(.taro-textarea) {
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 20px;
  padding: 24px;
  font-size: 28px;
}

.upload-list {
  width: 100%;
  background: #ffffff;
  border-radius: 20px;
  margin-top: 24px;
  padding: 28px;
  box-sizing: border-box;
  font-size: 28px;
  margin-bottom: 60px;
}

:deep(.nut-button) {
  border-radius: 44px;
  font-size: 32px;
  height: 88px;
}

:deep(.nut-checkbox) {
  margin-top: 20px;
}

.add-button {
  background: linear-gradient(270deg, #06bb6c 0%, #019e59 100%);
}
.checkBox {
  width: 690px;
  height: 88px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;
  display: flex;
  justify-content: space-between;
  padding: 0 45px;
  box-sizing: border-box;
}
// :deep(.nut-checkbox__icon) {
//     border-radius: 0 !important; /* 移除圆角 */
//     background-clip: padding-box; /* 防止背景色溢出边框 */
// }
.margin-left30 {
  margin-left: 30px;

}
</style>
