<template>
  <nut-navbar class="custom-nav-title" :style="{ background }" :title="title" @click-back="onClick">
    <template #left>
      <IconFont v-if="showBack" color="#000" name="left"></IconFont>
    </template>
  </nut-navbar>
</template>

<script setup lang="ts">
import { toRefs, watch } from 'vue'
import Taro from '@tarojs/taro'
import { IconFont } from '@nutui/icons-vue-taro'

const props = withDefaults(
  defineProps<{
    title: string
    showBack?: boolean
    background?: string
  }>(),
  {
    title: '',
    showBack: true,
    background: '#fff',
  }
)
const { title, showBack, background } = toRefs(props)

watch(
  title,
  (newVal) => {
    if (newVal) {
      // document.title = newVal
       // 在 Taro 中，可以使用 Taro.setNavigationBarTitle 方法来修改页面标题。以下是修改后的代码：
       Taro.setNavigationBarTitle({
        title: newVal as string
      })
    }
  },
  { immediate: true }
)

const onClick = () => {
  Taro.navigateBack()
}
</script>

<style lang="scss" scoped>
.custom-nav-title {
  display: none;
  padding-top: 10px;
  padding-bottom: 10px;
}
:deep(.nut-navbar) {
  box-shadow: unset !important;
}
</style>
