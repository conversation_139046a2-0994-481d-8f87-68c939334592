<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="333381e1-1329-4560-84af-4d331b54698d" name="更改" comment="✨feat(安装工单)：扫描无效二维码添加提示功能">
      <change beforePath="$PROJECT_DIR$/.eslintrc.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/.eslintrc.cjs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="tsconfig.json" />
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="xiongjia &lt;<EMAIL>&gt;" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main2.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;wenyuan&quot;,
      &quot;fullname&quot;: &quot;文渊&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.vankeytech.com/frontend/wuhua-h5.git&quot;,
    &quot;second&quot;: &quot;d4ba98c3-2933-4046-baec-37da58f3db74&quot;
  }
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="AISelfReview" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2w24fhy0RK2lEhUq0VgxrfsraHN" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Vankey/wuhua/whhua-h5-manage/src/assets/device&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;E:\\Vankey\\wuhua\\whhua-h5-manage\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;npm.build:h5.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev:h5.executor&quot;: &quot;Run&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;E:\\Vankey\\wuhua\\whhua-h5-manage\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;terminal&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\Vankey\\wuhua\\whhua-h5-manage\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\device" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\deviceMap" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\styles" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\mapIcons" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\components" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\mapIcons\bottom" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\assets\device" />
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\api" />
    </key>
    <key name="es6.move.members.recent.items">
      <recent name="E:\Vankey\wuhua\whhua-h5-manage\src\utils\index.ts" />
    </key>
  </component>
  <component name="RunManager" selected="npm.build:h5">
    <configuration name="build:h5" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:h5" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev:h5" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev:h5" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build:h5" />
        <item itemvalue="npm.dev:h5" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.27812.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="333381e1-1329-4560-84af-4d331b54698d" name="更改" comment="" />
      <created>1745226439813</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745226439813</updated>
      <workItem from="1745226440998" duration="1167000" />
      <workItem from="1745283655578" duration="2263000" />
      <workItem from="1745370057429" duration="3651000" />
      <workItem from="1745456428077" duration="579000" />
      <workItem from="1745462484909" duration="3903000" />
      <workItem from="1745486768282" duration="563000" />
      <workItem from="1745542852444" duration="4794000" />
      <workItem from="1745580195024" duration="574000" />
      <workItem from="1745715911056" duration="13721000" />
      <workItem from="1745802182735" duration="4975000" />
      <workItem from="1745812818132" duration="2689000" />
      <workItem from="1745841923062" duration="2479000" />
      <workItem from="1745894058132" duration="11423000" />
      <workItem from="1745975449394" duration="8676000" />
      <workItem from="1746493618813" duration="1144000" />
      <workItem from="1746514577137" duration="558000" />
      <workItem from="1746520263746" duration="558000" />
      <workItem from="1746523884371" duration="486000" />
      <workItem from="1746670479965" duration="3027000" />
      <workItem from="1746752491263" duration="559000" />
      <workItem from="1746753585959" duration="541000" />
      <workItem from="1746758175419" duration="73000" />
      <workItem from="1746758275722" duration="1697000" />
      <workItem from="1746791778406" duration="275000" />
      <workItem from="1746941501650" duration="1148000" />
      <workItem from="1746959213745" duration="554000" />
      <workItem from="1747011751711" duration="1144000" />
      <workItem from="1747098146015" duration="1307000" />
      <workItem from="1747184588546" duration="566000" />
      <workItem from="1747205075066" duration="2044000" />
      <workItem from="1747270704999" duration="1402000" />
      <workItem from="1747281664563" duration="4144000" />
      <workItem from="1747357288753" duration="2233000" />
      <workItem from="1747616730444" duration="12087000" />
      <workItem from="1747702975014" duration="3112000" />
      <workItem from="1747720234884" duration="593000" />
      <workItem from="1747725074439" duration="577000" />
      <workItem from="1747730802772" duration="27000" />
      <workItem from="1747730901583" duration="2357000" />
      <workItem from="1747806255352" duration="4459000" />
      <workItem from="1747875934296" duration="581000" />
      <workItem from="1747880461545" duration="1099000" />
      <workItem from="1747884388053" duration="547000" />
      <workItem from="1747885220783" duration="2482000" />
      <workItem from="1747909892458" duration="1964000" />
      <workItem from="1747962037357" duration="8853000" />
      <workItem from="1747992652139" duration="3026000" />
      <workItem from="1747999346611" duration="5003000" />
      <workItem from="1748135125689" duration="581000" />
      <workItem from="1748136713220" duration="4495000" />
      <workItem from="1748221149150" duration="427000" />
      <workItem from="1748221657843" duration="269000" />
      <workItem from="1748225459108" duration="30000" />
      <workItem from="1748225768755" duration="2264000" />
      <workItem from="1748228200872" duration="576000" />
      <workItem from="1748308138484" duration="6577000" />
      <workItem from="1748394103856" duration="590000" />
      <workItem from="1748504197693" duration="16000" />
      <workItem from="1749611192349" duration="5875000" />
      <workItem from="1749689880945" duration="4417000" />
      <workItem from="1749776806499" duration="8076000" />
      <workItem from="1750035969618" duration="575000" />
      <workItem from="1750043461499" duration="7554000" />
      <workItem from="1750057087661" duration="888000" />
      <workItem from="1750122394043" duration="584000" />
      <workItem from="1750131155636" duration="1197000" />
      <workItem from="1750147259755" duration="259000" />
      <workItem from="1750208536219" duration="578000" />
      <workItem from="1750215536338" duration="5004000" />
      <workItem from="1750295087533" duration="3357000" />
      <workItem from="1750381364743" duration="11507000" />
      <workItem from="1750640546768" duration="7166000" />
      <workItem from="1750663287200" duration="10091000" />
      <workItem from="1750727336765" duration="8438000" />
      <workItem from="1750812775936" duration="573000" />
      <workItem from="1750813503598" duration="148000" />
      <workItem from="1750813808188" duration="225000" />
      <workItem from="1750814067452" duration="269000" />
      <workItem from="1750814370833" duration="14168000" />
      <workItem from="1750901320228" duration="2241000" />
      <workItem from="1750921510221" duration="2455000" />
      <workItem from="1750991572942" duration="2778000" />
      <workItem from="1751015898026" duration="569000" />
      <workItem from="1751025609215" duration="562000" />
      <workItem from="1751082014141" duration="577000" />
      <workItem from="1751245439404" duration="566000" />
      <workItem from="1751249394960" duration="380000" />
      <workItem from="1751249819345" duration="142000" />
      <workItem from="1751249988329" duration="939000" />
      <workItem from="1751340068992" duration="139000" />
      <workItem from="1752212516013" duration="842000" />
      <workItem from="1752218910881" duration="14000" />
      <workItem from="1753778935162" duration="2439000" />
      <workItem from="1753781631881" duration="623000" />
    </task>
    <task id="LOCAL-00005" summary="✨feat(设备激活)：扫码取值优化">
      <option name="closed" value="true" />
      <created>1745733495656</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745733495656</updated>
    </task>
    <task id="LOCAL-00006" summary="✨ feat(webpack配置): 打包文件名添加hash值">
      <option name="closed" value="true" />
      <created>1745736858738</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1745736858738</updated>
    </task>
    <task id="LOCAL-00007" summary="✨ feat(设备管理): 优化扫码函数，调整代码格式">
      <option name="closed" value="true" />
      <created>1745739139672</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1745739139672</updated>
    </task>
    <task id="LOCAL-00008" summary="✨ feat(webpack配置): 打包文件名添加hash值">
      <option name="closed" value="true" />
      <created>1745745296673</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1745745296673</updated>
    </task>
    <task id="LOCAL-00009" summary="✨ feat(监控): 样式修改">
      <option name="closed" value="true" />
      <created>1745909548148</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1745909548148</updated>
    </task>
    <task id="LOCAL-00010" summary="✨ feat(文件上传): 移除客户信息显示，添加证书和私钥文件">
      <option name="closed" value="true" />
      <created>1745914554139</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1745914554139</updated>
    </task>
    <task id="LOCAL-00011" summary="✨ feat(监控): 样式修改">
      <option name="closed" value="true" />
      <created>1745922558406</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1745922558406</updated>
    </task>
    <task id="LOCAL-00012" summary="✨feat(虫情分析): 图表样式修改">
      <option name="closed" value="true" />
      <created>1746000826805</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746000826805</updated>
    </task>
    <task id="LOCAL-00013" summary="✨feat(监控): 删除冗余功能">
      <option name="closed" value="true" />
      <created>1746008050522</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746008050522</updated>
    </task>
    <task id="LOCAL-00014" summary="✨feat(全局): 设备名称修改">
      <option name="closed" value="true" />
      <created>1746673190352</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746673190352</updated>
    </task>
    <task id="LOCAL-00015" summary="fix(杀虫灯)：处理切换到参数统计时白屏问题">
      <option name="closed" value="true" />
      <created>1747215232653</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1747215232653</updated>
    </task>
    <task id="LOCAL-00016" summary="fix(监控)：图片禁止长按保存">
      <option name="closed" value="true" />
      <created>1747290969927</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747290969927</updated>
    </task>
    <task id="LOCAL-00017" summary="fix(工单管理)：修改type传递错误">
      <option name="closed" value="true" />
      <created>1747291474766</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1747291474766</updated>
    </task>
    <task id="LOCAL-00018" summary="✨feat(全局): 添加权限">
      <option name="closed" value="true" />
      <created>1747705851045</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1747705851045</updated>
    </task>
    <task id="LOCAL-00019" summary="fix: 部分bug修改">
      <option name="closed" value="true" />
      <created>1747807913552</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1747807913552</updated>
    </task>
    <task id="LOCAL-00020" summary="fix: 虫情图表优化">
      <option name="closed" value="true" />
      <created>1747811564830</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747811564830</updated>
    </task>
    <task id="LOCAL-00021" summary="fix">
      <option name="closed" value="true" />
      <created>1747899655215</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747899655215</updated>
    </task>
    <task id="LOCAL-00022" summary="fix">
      <option name="closed" value="true" />
      <created>1747909901148</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747909901148</updated>
    </task>
    <task id="LOCAL-00023" summary="fix">
      <option name="closed" value="true" />
      <created>1747968565118</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747968565118</updated>
    </task>
    <task id="LOCAL-00024" summary="fix">
      <option name="closed" value="true" />
      <created>1747970819321</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747970819321</updated>
    </task>
    <task id="LOCAL-00025" summary="fix">
      <option name="closed" value="true" />
      <created>1747982935800</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747982935800</updated>
    </task>
    <task id="LOCAL-00026" summary="fix">
      <option name="closed" value="true" />
      <created>1747984098044</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747984098044</updated>
    </task>
    <task id="LOCAL-00027" summary="fix">
      <option name="closed" value="true" />
      <created>1747988952511</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747988952511</updated>
    </task>
    <task id="LOCAL-00028" summary="fix: 权限">
      <option name="closed" value="true" />
      <created>1747995318074</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747995318074</updated>
    </task>
    <task id="LOCAL-00029" summary="fix: 权限">
      <option name="closed" value="true" />
      <created>1747996015238</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747996015238</updated>
    </task>
    <task id="LOCAL-00030" summary="fix: 权限">
      <option name="closed" value="true" />
      <created>1747996059681</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747996059681</updated>
    </task>
    <task id="LOCAL-00031" summary="fix: 监控mouseup mousedown替换为touchstart touchend">
      <option name="closed" value="true" />
      <created>1748227838517</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1748227838517</updated>
    </task>
    <task id="LOCAL-00032" summary="fix">
      <option name="closed" value="true" />
      <created>1748326280528</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1748326280528</updated>
    </task>
    <task id="LOCAL-00033" summary="fix">
      <option name="closed" value="true" />
      <created>1748332450723</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1748332450723</updated>
    </task>
    <task id="LOCAL-00034" summary="✨feat: 监控替换成Jessibuca">
      <option name="closed" value="true" />
      <created>1749694496959</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1749694496959</updated>
    </task>
    <task id="LOCAL-00035" summary="feat: 安装工单支持修改定位和图片、设备地图点击地址进入导航">
      <option name="closed" value="true" />
      <created>1750053628005</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1750053628005</updated>
    </task>
    <task id="LOCAL-00036" summary="fix: 优化安装工单表单逻辑和样式调整">
      <option name="closed" value="true" />
      <created>1750056461608</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1750056461608</updated>
    </task>
    <task id="LOCAL-00037" summary="✨feat: 监控添加放大缩小功能">
      <option name="closed" value="true" />
      <created>1750315352260</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1750315352260</updated>
    </task>
    <task id="LOCAL-00038" summary="fix">
      <option name="closed" value="true" />
      <created>1750650142893</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1750650142893</updated>
    </task>
    <task id="LOCAL-00039" summary="✨feat: 杀虫灯详情添加地图">
      <option name="closed" value="true" />
      <created>1750669644226</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1750669644226</updated>
    </task>
    <task id="LOCAL-00040" summary="✨feat: 虫情分析仪详情添加地图">
      <option name="closed" value="true" />
      <created>1750670370574</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1750670370574</updated>
    </task>
    <task id="LOCAL-00041" summary="✨feat: 墒情设备详情添加地图">
      <option name="closed" value="true" />
      <created>1750671592550</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1750671592550</updated>
    </task>
    <task id="LOCAL-00042" summary="✨feat: 设备详情地图修改">
      <option name="closed" value="true" />
      <created>1750757143466</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750757143466</updated>
    </task>
    <task id="LOCAL-00043" summary="✨feat: 设备详情地图修改">
      <option name="closed" value="true" />
      <created>1750757362587</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750757362587</updated>
    </task>
    <task id="LOCAL-00044" summary="✨feat: 设备详情地图修改">
      <option name="closed" value="true" />
      <created>1750832349177</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750832349177</updated>
    </task>
    <task id="LOCAL-00045" summary="✨feat: 设备地图图标修改">
      <option name="closed" value="true" />
      <created>1750836492978</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750836492978</updated>
    </task>
    <task id="LOCAL-00046" summary="✨feat: 设备地图图标修改">
      <option name="closed" value="true" />
      <created>1750837302508</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750837302508</updated>
    </task>
    <task id="LOCAL-00047" summary="✨feat: 设备地详情地图图标修改">
      <option name="closed" value="true" />
      <created>1750838313749</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750838313749</updated>
    </task>
    <task id="LOCAL-00048" summary="✨feat: “去这里”样式修改">
      <option name="closed" value="true" />
      <created>1750842681071</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750842681071</updated>
    </task>
    <task id="LOCAL-00049" summary="✨feat: 安装扫码优化">
      <option name="closed" value="true" />
      <created>1750919994717</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750919994717</updated>
    </task>
    <task id="LOCAL-00050" summary="fix">
      <option name="closed" value="true" />
      <created>1750924112135</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1750924112135</updated>
    </task>
    <task id="LOCAL-00051" summary="fix">
      <option name="closed" value="true" />
      <created>1750996466668</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750996466668</updated>
    </task>
    <task id="LOCAL-00052" summary="✨feat: 杀虫灯图标新增不亮灯图标">
      <option name="closed" value="true" />
      <created>1752212860617</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1752212860617</updated>
    </task>
    <task id="LOCAL-00053" summary="✨feat(安装工单)：扫描无效二维码添加提示功能">
      <option name="closed" value="true" />
      <created>1753781049121</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753781049121</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/main2.0" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix(杀虫灯)：处理切换到参数统计时白屏问题" />
    <MESSAGE value="fix(监控)：图片禁止长按保存" />
    <MESSAGE value="fix(工单管理)：修改type传递错误" />
    <MESSAGE value="✨feat(全局): 添加权限" />
    <MESSAGE value="fix: 部分bug修改" />
    <MESSAGE value="fix: 虫情图表优化" />
    <MESSAGE value="fix: 权限" />
    <MESSAGE value="fix: 监控mouseup mousedown替换为touchstart touchend" />
    <MESSAGE value="✨feat: 监控替换成Jessibuca" />
    <MESSAGE value="feat: 安装工单支持修改定位和图片、设备地图点击地址进入导航" />
    <MESSAGE value="fix: 优化安装工单表单逻辑和样式调整" />
    <MESSAGE value="✨feat: 监控添加放大缩小功能" />
    <MESSAGE value="✨feat: 杀虫灯详情添加地图" />
    <MESSAGE value="✨feat: 虫情分析仪详情添加地图" />
    <MESSAGE value="✨feat: 墒情设备详情添加地图" />
    <MESSAGE value="✨feat: 设备详情地图修改" />
    <MESSAGE value="✨feat: 设备地图图标修改" />
    <MESSAGE value="✨feat: 设备地详情地图图标修改" />
    <MESSAGE value="✨feat: “去这里”样式修改" />
    <MESSAGE value="✨feat: 安装扫码优化" />
    <MESSAGE value="fix" />
    <MESSAGE value="✨feat: 杀虫灯" />
    <MESSAGE value="✨ feat(虫情分析仪新增拍照是否开启闪光灯):" />
    <MESSAGE value="✨feat: 杀虫灯图标新增不亮灯图标" />
    <MESSAGE value="✨feat(安装工单)：扫描无效二维码添加提示功能" />
    <option name="LAST_COMMIT_MESSAGE" value="✨feat(安装工单)：扫描无效二维码添加提示功能" />
  </component>
</project>