// 换成本地的服务器js文件即可
const script = [
  'https://map.vankeytech.com:9100/bigemap.js/v2.1.0/bigemap.js',
  //如果有更多的JS要引用 ，也一起放到这个数组中
  '/cluster/bm.markercluster.js',
]
export default new Promise((resolve) => {
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  // 换成本地的服务器css文件即可
  link.href = 'https://map.vankeytech.com:9100/bigemap.js/v2.1.0/bigemap.css'
  document.head.appendChild(link)
  const loads = script.map((v) => {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = false
    script.src = v
    document.head.appendChild(script)
    return script
  })
  const end = loads.pop()
  end.onload = resolve
})
