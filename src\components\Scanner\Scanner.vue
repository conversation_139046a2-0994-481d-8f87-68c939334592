<template>
  <CustomNavTitle title="扫码" :showBack="true" />
  <div class="scanner-container">
    <view class="reader-box" v-if="isScaning">
      <view class="reader" id="reader"></view>
    </view>

    <nut-button type="primary" class="cancel-btn" size="large" @click="goBack">取 消</nut-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Html5Qrcode } from 'html5-qrcode'

const html5Qrcode = ref<Html5Qrcode | null>(null)
const isScaning = ref(false)

const emits = defineEmits(['scanResult'])

const openQrcode = () => {
  isScaning.value = true
  Html5Qrcode.getCameras().then((devices) => {
    if (devices && devices.length) {
      html5Qrcode.value = new Html5Qrcode('reader')
      html5Qrcode.value.start(
        {
          facingMode: 'environment',
        },
        {
          fps: 5, //设置扫码识别速度
          qrbox: 280, //设置二维码扫描框大小
        },
        (decodeText, decodeResult) => {
          if (decodeText) {
            stopScan().then(() => {
              emits('scanResult', decodeText)
            })
          }
        },
        (errorMessage) => {
          // console.error(errorMessage)
        }
      )
    }
  })
}

const goBack = () => {
  stopScan().then(() => {
    emits('scanResult', '')
  })
}

onMounted(() => {
  openQrcode()
})

// onBeforeUnmount(() => {
//   stopScan()
// })

const stopScan = () => {
  return new Promise<void>((resolve, reject) => {
    isScaning.value = false
    if (html5Qrcode.value) {
      html5Qrcode.value.stop()
      resolve()
    }
  })
}
</script>

<style scoped lang="scss">
.scanner-container {
  height: 100vh;
  background-color: #454a46;
  position: relative;
  z-index: 99;
}
.reader-box {
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.7);
}

.reader {
  width: 100%;
  // width: 540rpx;
  // height: 540rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.cancel-btn {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  bottom: 30px;
  width: 690px;
}
:deep(#qr-shaded-region) {
  > div {
    background-color: #06bb6c !important;
  }
}
</style>
