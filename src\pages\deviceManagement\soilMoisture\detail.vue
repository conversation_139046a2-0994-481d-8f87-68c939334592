<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />

  <div v-else class="page-content">
    <CustomNavTitle title="墒情气象一体机" :showBack="true" background="#fff" />

    <div v-if="baseData.coordinate?.latitude" ref="mapContainer" class="h-30" />

    <div class="batch-item info-box-title">
      <div class="batch-item-left">
        <img :src="baseData?.online ? IconDevice : UnIconDevice" alt="" />
      </div>
      <div class="batch-item-right">
        <div class="name">{{ baseData.serialNum || '--' }}</div>
        <div class="batch">设备型号：{{ baseData.innerModelName || '--' }}</div>
        <div class="batch">安装状态：{{ baseData.installed ? '已安装' : '未安装' }}</div>

        <div v-if="baseData.coordinate?.latitude">
          <span class="vertical-mid">{{ baseData.address }}</span>
          <span
            @click.stop="openGMap"
            class="ml-1 inline-flex items-center vertical-mid gap-x-0.5"
            text="#1688d1 2.4"
          >
            <image src="@/assets/deviceMap/locate.png" alt="" class="w-2.4 h-2.5" />
            导航
          </span>
        </div>
      </div>
    </div>

    <div v-if="tabList?.length > 0" class="p-3">
      <div class="tab-top-border">
        <CustomTabs
          style="border-top-left-radius: 4px; border-top-right-radius: 4px"
          v-model="status"
          :tab-list="tabList"
        />
      </div>
      <div v-if="status === 0">
        <div
          class="monitor-box-bg border-bottom-20"
          style="margin-top: 0; border-radius: 0 !important"
        >
          <div class="monitor-title">状态参数</div>
          <div class="monitor-line"></div>
          <div style="display: flex; flex-wrap: wrap">
            <div
              class="text-item"
              :style="{ width: item.channel ? '100%' : '100%' }"
              v-for="(item, index) in paramsList"
              :key="index"
            >
              <div>
                {{ item.paramName }}
                <!-- class="text-22" -->
                <span v-if="item.channel">({{ item.channel }}通道)</span>
              </div>
              <div class="text-1010">
                {{
                  deviceDetailData[item.columnName] || deviceDetailData[item.columnName] == 0
                    ? deviceDetailData[item.columnName]
                    : '--'
                }}
                {{ item.unit }}
              </div>
            </div>
            <div class="monitor-line"></div>
            <div class="text-item" style="width: 100%">
              <div>设备状态</div>
              <div :class="deviceDetailData?.online ? 'text-#019E59' : 'text-1010'">
                {{ deviceDetailData?.online ? '在线' : '离线' }}
              </div>
            </div>
            <div class="text-item" style="width: 100%">
              <div>本次开机时长</div>
              <div>{{ runningTimeDisplay || '--' }}</div>
            </div>
            <div class="text-item" style="width: 100%">
              <div>数据更新时间</div>
              <div>{{ deviceDetailData?.time || '--' }}</div>
            </div>

            <div class="monitor-line"></div>
            <div class="text-item flex-between" style="width: 100%">
              <div class="text-item-left" style=""></div>
              <div class="" style="color: #019e59">
                <div class="completeGreenDetail" @click="goStatistics">参数统计</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="status === 1" class="monitor-box-bg">
        <div class="text-item flex-between">
          <div>客户</div>
          <div class="text-item-right">{{ customerData?.name || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>客户区域</div>
          <div class="text-item-right">{{ customerData?.areaName || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人</div>
          <div class="text-item-right">{{ customerData?.respPerson || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人电话</div>
          <div class="text-item-right">{{ customerData?.phone || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>通讯地址</div>
          <div class="text-item-right">{{ customerData?.address || '--' }}</div>
        </div>
      </div>
      <div v-if="status === 2">
        <div class="monitor-box-bg">
          <div class="monitor-title">安装信息</div>
          <div class="monitor-line"></div>
          <div class="text-item flex-between">
            <div>工单</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.workOrderNo || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装人员</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installerName || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>手机号码</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.phoneNumber || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装时间</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installTime || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装经纬度</div>
            <div class="text-item-right">
              <span v-if="afterSalesData?.installInfo?.coordinate">
                {{ afterSalesData?.installInfo?.coordinate?.longitude }},
                {{ afterSalesData?.installInfo?.coordinate?.latitude }}
              </span>
              <span v-else>--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div class="text-item-left">安装照片</div>
            <div class="text-item-right">
              <VImage
                v-for="item in afterSalesData?.installInfo?.images"
                :key="item"
                class="img"
                :src="item.url"
                :preview-src-list="afterSalesData?.installInfo?.images.map((item) => item.url)"
              />
              <span
                v-if="
                  !afterSalesData?.installInfo?.images ||
                  afterSalesData?.installInfo?.images?.length < 1
                "
                >暂无图片</span
              >
            </div>
          </div>
        </div>
        <div
          class="monitor-box-bg"
          v-if="afterSalesData?.workOrders && afterSalesData?.workOrders?.length > 0"
        >
          <div class="monitor-title">工单记录</div>
          <!--  -->
          <div
            v-for="(item, index) in afterSalesData?.workOrders"
            :key="index"
            style="position: relative"
          >
            <div class="monitor-line"></div>
            <div class="text-item">
              <div class="text-item-left">工单</div>
              <div class="text-item-right">{{ item.workOrderNo || '--' }}</div>
            </div>
            <div class="text-item">
              <div class="text-item-left">工单类型</div>
              <div class="text-item-right">
                {{ item.type === 1 ? '安装工单' : item.type === 2 ? '售后工单' : '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">故障原因</div>
              <div class="text-item-right" style="width: 80%">
                {{ item.issueTypeName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交人</div>
              <div class="text-item-right" style="width: 80%">
                {{ item.createByName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交时间</div>
              <div class="text-item-right" style="width: 80%">
                {{ item.createTime || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="status === 3" class="mb-20">
        <div v-for="(item, index) in alarmItemsData" :key="index" class="monitor-box-bg">
          <div class="text-item flex-between">
            <div>告警项</div>
            <div class="text-item-right">{{ item.itemName || '--' }}</div>
          </div>
          <div class="text-item flex-between">
            <div>告警阈值</div>
            <div class="text-item-right">
              <span v-if="item.valueList && item.valueList.length > 0">
                {{
                  item.valueList.some((x) => x.value === item.value)
                    ? item.valueList.filter((x) => x.value === item.value)[0].label
                    : item.value
                }}
              </span>
              <span v-else
                >{{ item.value || '--' }}
                <span>{{ item.unit || '' }}</span>
              </span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>条件</div>
            <div class="text-item-right">
              {{ item.operator ? String(options[item.operator - 1]) : '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>平台推送方式</div>
            <div class="text-item-right">
              <span v-if="item.managePushMethod && item.managePushMethod.includes(1)">后台</span>
              <span
                v-if="
                  item.managePushMethod &&
                  item.managePushMethod.includes(1) &&
                  item.managePushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.managePushMethod && item.managePushMethod.includes(2)">短信</span>
              <span v-if="!item.managePushMethod || item.managePushMethod.length === 0">--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>客户推送方式</div>
            <div class="text-item-right">
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(1)"
                >后台</span
              >
              <span
                v-if="
                  item.customerPushMethod &&
                  item.customerPushMethod.includes(1) &&
                  item.customerPushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(2)"
                >短信</span
              >
              <span v-if="!item.customerPushMethod || item.customerPushMethod.length === 0"
                >--</span
              >
            </div>
          </div>
          <div class="monitor-line"></div>
          <div class="flex justify-end items-center">
            <div class="completeGreen" @click="addAlarm(item)">编辑</div>
            <div class="deleteRed left-15" @click="deleteAlarm(item)">删除</div>
          </div>
        </div>
        <emput v-if="!alarmItemsData || alarmItemsData?.length < 1" message="暂无告警设置数据" />
      </div>
    </div>

    <div v-show="status === 3" class="position-absolute w-100% bottom-0 flex justify-center">
      <div class="mt-2.5 position-relative w-80vw mb-4">
        <nut-button type="primary" size="large" @click="addAlarm"> 新增 </nut-button>
      </div>
    </div>

    <nut-dialog
      title="确定删除该告警设置？"
      v-model:visible="visibleShowAlarm"
      @cancel="onCancelAlarm"
      @ok="onOkAlarm"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Taro, { eventCenter, getCurrentInstance } from '@tarojs/taro'
import IconDevice from '../../../assets/device/<EMAIL>'
import UnIconDevice from '../../../assets/device/<EMAIL>'
import BaseInfoAPI from '../../../api/device/baseInfo'
import Scanner from '@/components/Scanner/Scanner.vue'
import SoilMoistureAPI from '@/api/device/soilMoisture'
import loadBigemap from '@/utils/bigemap'
import gcoord from 'gcoord'
import { classMap, DEFAULT_CENTER, deviceBottomIconMap, deviceIconMap2 } from '@/config/map'
import iconBottomAlarm from '@/assets/mapIcons/bottom/alarm.webp'
import iconBottomOffline from '@/assets/mapIcons/bottom/offline.webp'

const mapContainer = ref<HTMLElement>()

let map: any
let BM: any

const deviceInfo = ref<any>({})

const id = ref()
onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))

  getDetailBase()
  // 监听从back页面返回的事件
  const router = getCurrentInstance()?.router
  if (router?.onShow) {
    eventCenter.on(router.onShow, () => {
      getDeviceAlarmItems()
    })
  }
})

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = async () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [DEFAULT_CENTER.latitude, DEFAULT_CENTER.longitude],
    zoom: 16,
    minZoom: 5,
    zoomControl: false,
    maxZoom: 18,
  })

  drawMarker(baseData.value)
}

const status = ref(0)
const tabList = ref([
  {
    title: '设备信息',
    paneKey: 0,
    count: '',
  },
  {
    title: '客户信息',
    paneKey: 1,
    count: '',
  },
  {
    title: '售后信息',
    paneKey: 2,
    count: '',
  },
  {
    title: '告警设置',
    paneKey: 3,
    count: '',
  },
])

const baseData = ref({
  version: '',
  iccd: '',
  coordinate: {},
}) // 基础信息

const deviceDetailData = ref({
  staticMode: '',
  locateEnabled: '',
  disabled: '',
  forced: '',
  controlConfig: {
    normalMode: {
      startTime: '', // 起始时间
      endTime: '', // 截止时间
      interval: '', // 拍照间隔时间
    },
    sleepMode: {
      enabled: false, // 是否开启
      startDate: '', // 起始日期
      endDate: '', // 截至日期
      photoTime: '', // 拍照时间
    },
  },
}) // 设备信息
const customerData = ref({}) // 客户信息
const afterSalesData = ref() // 售后信息
const alarmItemsData = ref() // 告警设置信息
const options = ref(['等于', '不等于', '大于', '小于', '大于等于', '小于等于', '之间']) // 条件列表
const valueMap = ref<Record<number, any>>({}) // 告警项 valueList

// 获取设备基础信息
async function getDetailBase() {
  const res = await BaseInfoAPI.getBaseInfoId(deviceInfo.value.id)
  baseData.value = Object.assign({}, res)

  tabList.value = []
  // 如果设备有定位，标绘该设备
  if (res.coordinate) {
    loadBigeMap()
  }

  if (res) {
    if (res?.internetEnable) {
      tabList.value.push({
        title: '设备信息',
        paneKey: 0,
        count: '',
      })
    }
    if (res?.customerId) {
      tabList.value.push({
        title: '客户信息',
        paneKey: 1,
        count: '',
      })
    }
    if (res?.installed) {
      tabList.value.push({
        title: '售后信息',
        paneKey: 2,
        count: '',
      })
    }
    if (res?.internetEnable) {
      tabList.value.push({
        title: '告警设置',
        paneKey: 3,
        count: '',
      })
    }

    if (res?.internetEnable) {
      getTabList()
      getDeviceInfo()
      getDeviceAlarmItems()
      // getDeviceAlarmSetting();
    }
    if (res?.customerId) {
      getDeviceCustomer()
    }
    if (res?.installed) {
      getDeviceAfter()
    }
    tabList.value.sort((a, b) => a.paneKey - b.paneKey)
    status.value = tabList.value[0]?.paneKey ?? 0
  } else {
    tabList.value = []
    status.value = 0
  }
}

/**
 * 跳转高德地图
 * */
function openGMap() {
  const { longitude: lonEnd, latitude: latEnd } = baseData.value.coordinate
  const destination = baseData.value.address
  window.location.href = `https://uri.amap.com/marker?position=${lonEnd},${latEnd}&name=${destination}&src=mypage&coordinate=wgs84&callnative=1`
}

function drawMarker(item) {
  // 截取name最后三位
  const num = item.serialNum.slice(-3)

  let className = classMap[item.type]
  let bottomIcon = deviceBottomIconMap[item.type]
  if (item.isAlarm) {
    bottomIcon = iconBottomAlarm
    className += ' alarm'
  } else if (item.status === 0) {
    bottomIcon = iconBottomOffline
    className += ' offline'
  }

  const deviceIcon = BM.divIcon({
    html: `
        <header class="header flex items-center w-58Px h-28Px rounded-6Px">
          <img src="${deviceIconMap2[item.type]}" class="w-16Px h-18Px object-fit ml-6Px mr-4Px"/>
          <span text="14Px white">${num}</span>
        </header>
        <footer class="flex justify-center mt-2Px">
          <div style="background: url(${bottomIcon}) center / 100% 100% no-repeat" class="footerImage w-16Px h-8Px"/>
        </footer>
        `,
    iconSize: [58, 38],
    iconAnchor: [29, 38],
    className: `deviceIcon ${className}`,
  })
  const { latitude, longitude } = item.coordinate
  const wgs84Coord = gcoord.transform([longitude, latitude], gcoord.WGS84, gcoord.BD09)
  const marker = BM.marker([wgs84Coord[1], wgs84Coord[0]], { icon: deviceIcon })

  map.flyTo([wgs84Coord[1], wgs84Coord[0]], 16)

  marker.addTo(map)
}

// type  设备类型 1 智慧杀虫灯 2 墒情气象一体机 3 虫情分析仪 4 苗情监控
function getDeviceInfo() {
  BaseInfoAPI.getDeviceInfo(deviceInfo.value.id, 2).then((res) => {
    if (res) {
      console.log(res)
      deviceDetailData.value = res
    }
  })
}

function getDeviceCustomer() {
  BaseInfoAPI.getCustomerInfo(deviceInfo.value.id).then((res) => {
    customerData.value = res || {}
  })
}

function getDeviceAfter() {
  BaseInfoAPI.getAfterSaleInfo(deviceInfo.value.id).then((res) => {
    afterSalesData.value = res || {}
  })
}

// 获取tabList数据
const paramsList = ref([])
const getTabList = async () => {
  const res = await SoilMoistureAPI.paramSoil(deviceInfo.value.id)
  // if (res && res.length > 0) {
  //   res.forEach((item) => {
  //     dataList.value.forEach((data) => {
  //       if (data.key === item.columnName) {
  //         data.channel = item.channel;
  //       }
  //     });
  //   });
  // }
  paramsList.value = []
  if (res && res.length > 0) {
    paramsList.value = res.filter((item) => item.enabled === true)
  }
}
// ...........................参数统计....................
function goStatistics() {
  const data = {
    staticConfig: baseData?.value?.misc?.staticConfig,
    staticMode: baseData?.value?.staticMode,
    deviceId: deviceInfo.value.id,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/soilMoisture/statistics??data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

const formData = ref({
  activeBy: '',
  imei: '',
  serialNum: '',
})

const rules = {
  activeBy: [{ required: true, message: '请输入激活人' }],
  imei: [{ required: true, message: '请输入设备编号' }],
}

const showScanner = ref(false)
const toActivation = (result: string) => {
  formData.value.imei = result
  showScanner.value = false
}

// .............................告警设置 start..........................

/**
 * 删除告警项行
 */
const alarmIndex = ref()

// 删除告警设置
function deleteAlarm(item) {
  alarmIndex.value = item.id
  visibleShowAlarm.value = true
}

const visibleShowAlarm = ref(false)

const onCancelAlarm = () => {
  visibleShowAlarm.value = false
}

const onOkAlarm = () => {
  // alarmItemsData.value.splice(alarmIndex.value, 1)
  const data = {
    deviceId: deviceInfo.value.id,
    ids: [alarmIndex.value],
  }
  BaseInfoAPI.deleteConfigItem(data).then((res) => {
    Taro.showToast({
      title: '删除成功',
      icon: 'success',
    })
    getDeviceAlarmItems()
  })
}

/**
 * 获取当前设备配置
 */
function getDeviceAlarmItems() {
  BaseInfoAPI.getDeviceAlarmItems(deviceInfo.value.id).then((res) => {
    alarmItemsData.value = res || []
    alarmItemsData.value.forEach((val) => {
      val.options = val.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      valueMap.value[val.itemId]?.forEach((item) => {
        if (val.value == item.value) {
          val.valueName = item.text
        }
      })
    })
  })
}
// 新增告警
function addAlarm(row?) {
  const data = {
    innerModel: baseData.value.innerModel,
    deviceId: deviceInfo.value.id,
    deviceType: 2,
    ...row,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/component/addAlarm?data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

// .............................告警设置 end............................

// 定义一个计算属性将分钟转换为天、小时和分钟
const runningTimeDisplay = computed(() => {
  const minutes = deviceDetailData?.value.runningTime
  if (!minutes || minutes == 0) return '0'

  const days = Math.floor(minutes / (24 * 60))
  const remainingMinutesAfterDays = minutes % (24 * 60)
  const hours = Math.floor(remainingMinutesAfterDays / 60)
  const finalMinutes = remainingMinutesAfterDays % 60

  let display = ''
  if (days > 0) {
    display += `${days}天`
  }

  if (hours > 0) {
    display += `${hours}小时`
  }

  if (finalMinutes > 0 || (!days && !hours)) {
    display += `${finalMinutes}分钟`
  }

  return display
})
</script>

<style scoped lang="scss">
.batch-item {
  width: 690px;
  padding: 30px 30px;
  box-sizing: border-box;
  margin: 30px auto 0;
  border-radius: 20px;
  background: #fff;
  display: flex;
  .batch-item-left {
    margin-right: 20px;
    img {
      width: 60px;
      height: 60px;
    }
  }
  .batch-item-right {
    flex: 1;
    font-size: 28px;
    color: #909090;
    .name {
      font-size: 32px;
      color: #000;
      font-weight: bold;
    }
    .batch {
      margin: 5px 0;
    }
    .count {
      display: flex;
      justify-content: space-between;
    }
  }
}
.label-text {
  font-size: 30px;
  padding-left: 30px;
}
.code {
  margin-top: 50px;
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item) {
  .nut-input-inner {
    height: 78px;
    padding: 0 30px;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 630px;
  margin: 0 auto 0;
  border-radius: 20px;
}
.bottom-button {
  position: fixed;
  left: 30px;
  bottom: 60px;
  display: flex;
  justify-content: space-between;
  width: 690px;
  margin: 0 auto;
  .nut-button {
    width: 330px;
  }
}

//
.monitor-title {
  font-weight: bold;
  font-size: 32px;
  color: #101010;
}
.monitor-line {
  width: 100%;
  // height: 0px;
  border-bottom: 1px solid #d4d4d4;
  margin: 30px 0;
}
// 文字左右展示
.monitor-box-bg {
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
  .flex-between {
    display: flex;
    justify-content: space-between;
  }
  .text-item {
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 28px;
    color: #5b5b5b;
    margin: 16px 0;
    .text-item-right {
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      width: 450px;
      text-align: right;
      word-break: break-all;
      overflow-wrap: break-word; /* 允许长单词或 URL 地址换行到下一行 */
      // display: flex;
      // justify-content: flex-end;
      // flex-wrap: wrap;
    }
    .img {
      width: 82px;
      height: 82px;
      border-radius: 10px 10px 10px 10px;
      margin-left: 10px;
      margin-bottom: 10px;
    }
    .text-item-left {
      width: 140px;
    }
    .text-item-center {
      width: 380px;
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      word-break: break-all;
      overflow-wrap: break-word;
    }
  }
  .statusGd {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0px;
    top: 40px;
  }
}
.text-1010 {
  font-weight: 400;
  font-size: 28px;
  color: #101010;
}
.text-22 {
  font-size: 22px;
}
.border-bottom-20 {
  border-radius: 0 0 20px 20px;
}
</style>
