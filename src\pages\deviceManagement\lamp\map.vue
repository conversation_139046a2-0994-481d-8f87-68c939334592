<script setup lang="ts">
import { onMounted, ref } from 'vue'
import gcoord from 'gcoord'
import loadBigemap from '../../../utils/bigemap.ts'
import { DEFAULT_CENTER } from '../../../config/map'
import Taro from '@tarojs/taro'

const emit = defineEmits(['change', 'close'])

const mapContainer = ref<HTMLElement>()
const show1 = ref(false)

let map: any
let marker: any
let BM: any

const dataLngLat=ref()

onMounted(() => {
   // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  const data= JSON.parse(decodeURIComponent(params.data))
  dataLngLat.value=data
  loadBigeMap()
})

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center:[
      dataLngLat.value?.latitude || DEFAULT_CENTER.latitude,
      dataLngLat.value?.longitude || DEFAULT_CENTER.longitude,
    ],
    zoom: 7,
    zoomControl: false,
  })
  // 添加标记
  marker = BM.marker([dataLngLat.value.latitude, dataLngLat.value.longitude]).addTo(map)
}

const showDetail = ref(true)
const unfold = ref(['name1'])

</script>
<template>
  <div class="container">
    <CustomNavTitle title="实时定位" />

    <div ref="mapContainer" class="map-container" />
  </div>
</template>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
.map-container {
  width: 100%;
  flex: 1;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}
</style>
