<template>
  <div class="filterBox">
    <div class="chooseContent">
     <div class="title">设备状态</div>
     <div class="content">
      <div v-for="(item,index) in statusList" :key="index" class="nameBtn" :class="statusIndex===item.id?'nameBtnActive':''" @click="statusClick(item.id,'statusIndex')">
        {{ item.name }}
      </div>
     </div>

     <div class="title">客户区域</div>
     <div class="content">
      <div v-for="(item,index) in areaList" :key="index" class="nameBtn" :class="areaIndex===item.value?'nameBtnActive':''" @click="statusClick(item.value,'areaIndex')">
        {{item.label}}
      </div>
     </div>

     <div class="title">客户名称</div>
     <div class="content">
      <div v-for="(item,index) in customerList" :key="index" class="nameBtn" :class="customerIndex===item.id?'nameBtnActive':''" @click="statusClick(item.id,'customerIndex')">
        {{item.name}}
      </div>
     </div>
    </div>

     <div class="btnFliter">
       <div class="resetBtn" @click="resetClick">重置</div>
       <div class="sureBtn" @click="sureClick">确定</div>
     </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineEmits } from 'vue'
import { defineProps } from 'vue';
import BaseInfoAPI from '@/api/device/baseInfo'

// // 父组件传递的提示语和背景图路径
// defineProps({
//   showRight: {
//     type: Boolean,
//     default: true, // 默认显示背景
//   },
//   filterData: {
//     type: Object,
//     default: () => ({}) // 默认值为一个空对象
//   }
// });



const emits = defineEmits<{
  (e: 'change', value: any): void
}>()

const statusList=ref([{id:'',name:'全部'},{id:'2',name:'离线'},{id:'1',name:'在线'}])

// 设备状态
const statusIndex=ref('')
const areaIndex=ref('')
const customerIndex=ref('')

// 在 <script setup> 中，defineProps 只需定义一次，并且可以直接使用 props
const props = defineProps<{
  filterData?: {
    online: any;
    areaId: any;
    customerId: any;
  };
}>();
console.log(props.filterData)
// 从 props 中获取 filterData 的值并赋值给对应的 ref
statusIndex.value = props.filterData?.online || '';
areaIndex.value = props.filterData?.areaId || '';
customerIndex.value = props.filterData?.customerId || '';

function statusClick(index,type){
  console.log(index,type)
  // statusIndex.value=index
  // 原代码 [type].value=index 有误，推测是想根据传入的 type 动态修改对应 ref 的值
  // 这里使用对象解构和动态属性名来正确修改对应 ref 的值
  if (type === 'statusIndex') {
    statusIndex.value = index;
  } else if (type === 'areaIndex') {
    areaIndex.value = index;
  } else if (type === 'customerIndex') {
    customerIndex.value = index;
  }
}

// 重置
function resetClick(){
  statusIndex.value=''
  areaIndex.value=''
  customerIndex.value=''
}

// 确定
function sureClick(){
  const data={
    online:statusIndex.value,
    areaId:areaIndex.value,
    customerId:customerIndex.value,
  }
  console.log(data)
  emits('change',data)
  // console.log(statusIndex.value)
  // console.log(areaIndex.value)
  // console.log(customerIndex.value)
}


const areaList=ref()
// 获取下来筛选列表
const getAreaList = async () => {
  areaList.value = await BaseInfoAPI.areaOptions()
  areaList.value.unshift({value:'',label:'全部'})
  // areaList.value.forEach(x => {
  //   x.show=false
  // });
  console.log(areaList)
}
getAreaList()

const customerList=ref()
// 获取下来筛选列表
const getCustomerList = async () => {
 customerList.value = await BaseInfoAPI.customerOptions()
 customerList.value.unshift({id:'',name:'全部'})
//  customerList.value.forEach(x => {
//   x.show=false
//  }); 
  console.log(areaList)
}
getCustomerList()

</script>

<style scoped lang="scss">
.filterBox{
  width: 100%;
  padding: 30px;
  box-sizing: border-box;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  .chooseContent{
    max-height: 88%;
    overflow-y: auto;
  }
  .title{
   font-family: PingFang SC, PingFang SC;
   font-weight: bold;
   font-size: 28px;
   color: #101010;
   margin-top: 30px;
   margin-bottom: 26px;
  }
  .content{
    display: flex;
    flex-wrap: wrap;
    .nameBtn{
      width: 190px;
      height: 58px;
      background: #F7F8FC;
      border-radius:6px;
      margin-right: 22px;
      padding-left:20px ;
      padding-right:20px ;
      box-sizing: border-box;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-weight: 500;
      font-size: 24px;
      color: #19191A;
      margin-bottom: 26px;
      text-align: center;
      line-height: 58px;
    }
    .nameBtn:nth-child(3n){
       margin-right: 0px;
    }
    .nameBtnActive{
      background: rgba(2,164,93,0.1);
      border: 1px solid #02A45D;
      font-weight: bold;
      font-size: 24px;
      color: #02A45D;
    }
  }
}

.btnFliter{
  // width: 100%;
  width: 614px;
  height: 74px;
  line-height: 74px;
  background: #02A45D;
  border-radius: 12px;
  display: flex;
  position: absolute;
  bottom: 50px;
  .resetBtn{
    // width: 50%;
    width: 308px;
    height: 74px;
    background: #FFFFFF;
    border-radius: 12px;
    border: 1px solid #02A45D;
    font-weight: bold;
    font-size: 26px;
    color: #02A45D;
    text-align: center;
  }
  .sureBtn{
    width: 50%;
    font-weight: bold;
    font-size: 26px;
    color: #FFFFFF;
    text-align: center;
  }
}

</style>