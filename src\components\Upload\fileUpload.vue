<!-- 多图上传组件 -->
<script setup lang="ts">
import { FileAPI, getFileUrl } from '../../api/file'
import { ref, watch } from 'vue'
import Taro from '@tarojs/taro'

const emit = defineEmits(['update:modelValue'])

const props = defineProps({
  text: {
    type: String,
    default: '上传文件',
  },
  showButton: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: Array<FileInfo>,
    default: () => [],
  },
  limit: {
    type: Number,
    default: 10,
  },
  tips: {
    type: String,
    default: '',
  },
  accept: {
    type: String,
    default: '',
  },
  dir: {
    required: false,
    type: String,
    default: 'file',
  },
  width: {
    required: false,
    type: String,
    default: '100%',
  },
})

const loading = ref(false)

const fileList = ref<any[]>([])
/**内部更新rawFiles时这里无需执行*/
let needRefresh = true

watch(
  () => props.modelValue,
  (newVal: string[]) => {
    if (!needRefresh) {
      needRefresh = true
      return
    }
    fileList.value = []
    for (const item of newVal) {
      getFileUrl(item.url).then((url) => {
        fileList.value.push({
          objectName: item.url,
          name: item.name,
          url,
          status: 'success',
          type: 'image',
        })
      })
    }
  },
  { immediate: true }
)

// 上传前校验
const beforeUpload = () => {
  const file = fileList.value[fileList.value.length - 1]
  const fileName = file.name
  const pos = fileName.lastIndexOf('.')
  const lastName = fileName.substring(pos, fileName.length)
  const acceptName = props.accept.split(',')

  if (props.accept && !acceptName.includes(lastName)) {
    Taro.showToast({
      title: '请选择正确的文件格式',
      icon: 'none',
    })
    return false
  }

  if (file.size > 200 * 1024 * 1024) {
    Taro.showToast({
      title: '上传文件不能大于200M',
      icon: 'none',
    })
    return false
  }

  onUpload(file)
  return false // 返回 false 阻止内置上传
}
const uploader = ref<any>(null)
// 上传操作
const onUpload = async (file: File) => {
  loading.value = true
  try {
    const data = await FileAPI.upload(file, props.dir)
    Taro.showLoading({
      title: '上传中',
    })
    uploader.value.clearUploadQueue(0)
    file.status = 'success'
    file.url = data.url
    file.objectName = data.objectName
    const list = fileList.value.map((file) => ({
      name: file.name,
      url: file.objectName,
    }))
    needRefresh = false
    emit('update:modelValue', list)
  } catch (error) {
    Taro.showToast({
      title: '上传失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
    Taro.hideLoading()
  }
}

// 删除文件
function onDelete() {
  needRefresh = false
  const list = fileList.value.map((file) => ({
    name: file.name,
    url: file.objectName,
  }))
  emit('update:modelValue', list)
}

// 超出数量限制
const onOversize = () => {
  Taro.showToast({
    title: `最多选择 ${props.limit} 个文件，超出了文件数量限制！`,
    icon: 'none',
  })
}
</script>

<template>
  <nut-overlay v-model:visible="loading" :close-on-click-overlay="false"></nut-overlay>
  <div :style="{ width }">
    <nut-uploader
      ref="uploader"
      v-model:file-list="fileList"
      :maximum="limit"
      :before-xhr-upload="beforeUpload"
      :disabled="loading"
      @oversize="onOversize"
      @delete="onDelete"
    >
      <template #upload>
        <nut-button v-if="showButton" :loading="loading" type="primary">
          {{ text }}
        </nut-button>
      </template>
      <template #tip>
        <div class="upload-tip" v-if="tips">{{ tips }}</div>
      </template>
    </nut-uploader>
  </div>
</template>

<style scoped lang="scss">
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
:deep(.nut-uploader__preview) {
  width: 110px;
  height: 110px;
  .nut-uploader__preview-img {
    width: 110px;
    height: 110px;
  }
}
:deep(.nut-uploader__upload) {
  width: 110px;
  height: 110px;
}
</style>
