<template>
  <div class="vertical">
  <div class="page-content" >
    <CustomNavTitle title="参数统计" :showBack="true"  background="#fff" />
    <div class="content">
      <div class="content-titlt">
        <div style="width: 100%">
          <CustomTabs v-model="status" :tab-list="tabList" :verticalScreen="true"/>
        </div>
        <div class="content-line"></div>
      </div>
      <div class="dateDiv">
        <div class="dateInput" @click="openTimePop(0)">
          <div>{{ date }}</div>
          <IconFont name="rect-down" size="12" color="#707070"></IconFont>
        </div>
        <TabCardDate
          v-model:tab-list="tabListDate"
          class="timeTab"
          @change="handleChangeDateTab($event)"
        />
      </div>
      <Chart v-if="dataY?.length > 0" ref="chartRef" :option="option" class="chartsDiv" />
      <div v-if="!dataY || dataY?.length < 1" class="charts-nodata">暂无数据</div>
    </div>
    <!-- <div class="content">
      <div class="content-titlt">
        <div style="width: 68%">
          <CustomTabs v-model="status1" :tab-list="tabList1" />
        </div>
        <TabCardDate
          v-model:tab-list="tabListDate"
          class="timeTab"
          @change="handleChangeDateTab($event, 1)"
        />
      </div>
      <div class="dateInput" @click="openTimePop(1)">
        <div>{{ date1 }}</div>
        <IconFont name="rect-down" size="12" color="#707070"></IconFont>
      </div>
      <Chart
        v-if="dataY?.length > 0 && tabList1[status1].type !== 'temperature-humidity'"
        ref="chartRef"
        :option="option"
        class="chartsDiv"
      />
      <Chart
        v-if="dataY && dataY.length > 0 && tabList1[status1].type === 'temperature-humidity'"
        ref="chartRef"
        :option="option1"
        class="chartsDiv"
      />
      <div v-if="!dataY || dataY?.length < 1" class="charts-nodata">暂无数据</div>
    </div> -->
    <nut-popup v-model:visible="showPop" position="bottom">
      <nut-picker
        v-model="dateYear"
        v-if="chooseDeteType == 'year'"
        :columns="columns"
        title=""
        @confirm="confirm"
        @cancel="cancel"
      />
      <nut-date-picker
        v-else
        v-model="detePop"
        :type="chooseDeteType"
        :min-date="min"
        :max-date="max"
        :three-dimensional="false"
        @confirm="confirm"
        @cancel="cancel"
      ></nut-date-picker>
    </nut-popup>
  </div>
</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, watch } from 'vue'
import Taro from '@tarojs/taro'
import dayjs from 'dayjs'
import type { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import { IconFont } from '@nutui/icons-vue-taro'
import SoilMoistureAPI from '@/api/device/soilMoisture'

const currentYear = new Date().getFullYear()
const columns = ref([
  ...Array.from({ length: 30 }, (_, index) => ({
    text: `${currentYear - index}`,
    value: `${currentYear - index}`,
  })),
])

const deviceInfo = ref<any>({})

onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  console.log(deviceInfo.value, 'deviceId.value')

  getTabList()
})

// 获取tabList数据
const getTabList = async () => {
  const res = await SoilMoistureAPI.paramSoil(deviceInfo.value.deviceId)
  const list = []
  if (res && res.length > 0) {
    res.forEach((item) => {
      if (item.enabled && item.chart) {
        list.push({
          ...item,
          title: item.paramName,
          paneKey: item.paramCode,
        })
      }
    })
  }

  tabList.value = list
  status.value = list[0].paneKey

  console.log(list, 'list')
  // emits('change', list[0].id)

  setTimeout(() => {
    getChat()
  }, 500)
  // console.log(res)
  // tabList.value=res
}

const loading = ref(false)

const status = ref(0)
const tabList = ref([
  {
    title: '充电电流',
    paneKey: 0,
    count: '',
    type: 'charging-current',
  },
  {
    title: '放电电流',
    paneKey: 1,
    count: '',
    type: 'discharge-current',
  },
  {
    title: '电压',
    paneKey: 2,
    count: '',
    type: 'voltage',
  },
])

const tabListDate = reactive([
  { id: 0, key: 'day', name: '日', type: 'daily' },
  { id: 1, key: 'month', name: '月', type: 'monthly' },
  { id: 2, key: 'year', name: '年', type: 'yearly' },
])

const tableIndexDate = ref(tabListDate[0].id)

watch(status, (newStatus) => {
  setTimeout(() => {
    getChat()
  }, 500)
})

// 年月日类型
const chooseDeteType = ref('date')

const dateYear = ref()

function handleChangeDateTab(item) {
  console.log(item)
  tableIndexDate.value = tabListDate[item].id
  if (tabListDate[item].key === 'day') {
    date.value = dayjs(new Date()).format('YYYY-MM-DD')
  } else if (tabListDate[item].key === 'month') {
    date.value = dayjs(new Date()).format('YYYY-MM')
  } else {
    date.value = dayjs(new Date()).format('YYYY')
  }
  getChat()
}

// const chooseDeteType=ref('date')

const showPop = ref(false)
const min = ref(new Date(2020, 0, 1))
const max = ref(new Date(2025, 11, 31))
const detePop = ref(new Date())
const confirm = (val: any) => {
  if (val && val.selectedValue) {
    date.value = val.selectedValue.join('-')
  }
  showPop.value = false
  getChat()
}

function cancel() {
  showPop.value = false
}

function openTimePop(type) {
  let key = ''
  key = tabListDate[tableIndexDate.value].key

  if (key === 'day') {
    chooseDeteType.value = 'date'
    detePop.value = new Date()
  } else if (key === 'month') {
    chooseDeteType.value = 'year-month'
    detePop.value = new Date()
  } else {
    chooseDeteType.value = 'year'
    dateYear.value = [dayjs(new Date()).format('YYYY')]
  }
  showPop.value = true
}

const dataX = ref()
const dataY = ref()
const title = ref()
const unitDate = ref()
const date = ref(dayjs(new Date()).format('YYYY-MM-DD'))

// 获取图标数据
const getChat = async () => {
  loading.value = true
  const res = await SoilMoistureAPI.chartSoil({
    deviceId: deviceInfo.value.deviceId,
    unit: tabListDate[tableIndexDate.value].key,
    paramCode: status.value, // 参数id
    query: date.value,
  })
  console.log(res, 'res')
  loading.value = false
  dataX.value = []
  dataY.value = []
  title.value = tabList.value.find((item) => item.paneKey === status.value).title
  unitDate.value = res.yunit
  if (res && res.values.length > 0) {
    res.values.forEach((item) => {
      // dataX.value.push(item.x)
      if (tableIndexDate.value === 1) {
        dataX.value.push(`${item.x}日`)
      } else if (tableIndexDate.value === 2) {
        dataX.value.push(`${item.x}月`)
      } else {
        // dataX.value.push(`${item.x}时`)
        dataX.value.push(`${item.x}`)
      }

      dataY.value.push(item.y)
    })
  }

  // dataX.value = ['2025-02-32', '2025-02-32', '2025-02-32', '2025-02-32', '2025-02-32']
  // dataY.value = [1, 2, 4, 5, 8]
}

// const dataX=ref([1,2,4,5,8])
// const dataY=ref([1,2,4,5,8])

const option = computed<EChartsOption>(() => {
  // 根据 dynamicLineData 长度动态设置 xAxis 的 axisLabel 间隔
  const axisLabelInterval = dataX?.value?.length < 5 ? 0 : 3
  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        return `${params[0].axisValue}<br/>${title.value}: ${value} ${unitDate.value}`;
      },
    },
    legend: {
      x: '5%',
      top: '0%',
      textStyle: {
        color: '#000',
        fontSize: 13 , // 设置图例项的字体大小
        fontWeight: 'normal', // 设置图例项的字体粗细
      },
      icon: 'circle', // 设置图例项的形状为圆形
      itemWidth: 10, // 设置图例项的宽度
      itemHeight: 10, // 设置图例项的高度
      data: [title.value],
      formatter(name) {
        return `${name} ${unitDate.value}` // 在名称后添加单位
      },
    },
    // legend: {
    //   x: '3%',
    //   top: '-1%',
    //   textStyle: {
    //     color: '#000',
    //     fontSize: 15, // 设置图例项的字体大小
    //     fontWeight: 'normal', // 设置图例项的字体粗细
    //   },
    //   icon: 'circle', // 设置图例项的形状为圆形
    //   itemWidth: 10, // 设置图例项的宽度
    //   itemHeight: 10, // 设置图例项的高度
    //   data: [title.value],
    // },
    // xAxis: [
    //   {
    //     type: 'category',
    //     data: dataX.value,
    //     boundaryGap: true, //x轴是否有空隙
    //     axisLine: {
    //       lineStyle: {
    //         color: '#CCCFD7',
    //       },
    //     },
    //     axisLabel: {
    //       // 根据前面计算的间隔动态设置
    //       interval: axisLabelInterval,
    //     },
    //   },
    // ],
    xAxis: [
      {
        type: 'category',
        data: dataX.value,
        boundaryGap: true, // 是否留白
        axisLine: {
          lineStyle: {
            color: '#CCCFD7',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        splitNumber: 4,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#CCCFD7',
          },
        },
        splitArea: {
          show: false,
        },
      },
    ],
    series: [
      {
      name: title.value,
      type: 'line',
      symbol: 'emptyCircle', // 使用空心圆
      symbolSize: 8,
      data: dataY.value,
      itemStyle: {
        normal: {
        color: '#00CC73',
        borderColor: '#00CC73', // 边框颜色
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          {
            offset: 0,
            color: 'rgba(255,255,255,1)',
          },
          {
            offset: 1,
            color: 'rgba(0,204,115, 0.1)',
          },
          ]),
        },
        },
      },
      smooth: true,
      },
    ],
    dataZoom: [
      {
        type: 'inside', // 内置缩放
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        zoomLock: false, // 允许缩放
      },
      {
        type: 'slider', // 滑动条单独显示
        show: dataX.value.length>4 ? true : false, // 是否显示滑动条
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        height: 6, // 滑动条组件高度
        bottom: 0, // 距离图表区域下边的距离
        borderRadius: 5,
        showDetail: true, // 拖拽时显示详情
        showDataShadow: false,
        fillerColor: '#019e59', // 平移条的填充颜色
        borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
        backgroundColor: '#CCCFD7', // 背景颜色
        handleStyle: {
          color: '#019e59', // 手柄颜色
        },
        textStyle: {
          fontSize: 12,
        },
      },
    ],
    // dataZoom: [
    //   {
    //     type: 'slider', // 滑动条单独显示
    //     show: axisLabelInterval !== 0 ? true : false, // 是否显示滑动条
    //     // start: 0, // 左边的滑块位置，表示从0开始显示
    //     // end: 20, // 右边的滑块位置，表示只显示3个点（33.33333333333333%表示总长度的30%）
    //     startValue: 0, // 展示区域内容的起始数值
    //     endValue: 24, // 展示区域内容的结束数值 当前展示x坐标下标为0-7
    //     height: 6, // 滑动条组件高度
    //     bottom: 0, // 距离图表区域下边的距离
    //     borderRadius: 5,
    //     showDetail: false, // 拖拽时是否显示详情
    //     showDataShadow: false,
    //     fillerColor: '#019e59', // 平移条的填充颜色
    //     borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
    //     backgroundColor: '#CCCFD7', // 背景颜色
    //     zoomLock: true, // 锁定视图
    //     brushSelect: false, // 不可缩放 滑动条默认是有手柄可以进行展示的内容区域缩放的，不太美观
    //     // 通过该属性可以只滑动，不显示缩放功能
    //     handleStyle: {
    //       opacity: 0,
    //     },
    //     lineStyle: {
    //       opacity: 0,
    //     },
    //     textStyle: {
    //       fontSize: 0,
    //     },
    //   },
    // ],
    grid: {
      top: '15%',
      left: '5%',
      right: '5%',
      bottom: '10%',
      containLabel: true, //‌防止标签溢出‌
    },
  }
})

// 返回上一页
const goBack = () => {
  Taro.navigateBack()
}
</script>

<style scoped lang="scss">
.vertical{
  width: 100vh;  /* 竖屏时宽度取视口高度 */
  height: 100vw; /* 竖屏时高度取视口宽度 */
  transform: rotate(90deg) translateY(-100%);
  transform-origin: left top;
  overflow-x: auto;
}
.page-content {
  background: #F5F6FA;
  height: 100vw;
  box-sizing: border-box;
  // overflow-x:hidden;
  // overflow-y: scroll;
}
.content {
  width: 100%;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  // margin: 30px auto;
  position: relative;
  padding-bottom: 30px;
  box-sizing: border-box;
  .content-titlt {
    width: 100vh;  // 竖屏时宽度取视口高度
    // height: 0px;
    // border-bottom: 1px solid #d4d4d4;
    // padding-left: 30px;
    box-sizing: border-box;
    position: relative;
    .content-line {
      width: 100%;
      height: 0px;
      border-bottom: 1px solid #d4d4d4;
      position: absolute;
      bottom: 6px;
      left: 0;
      z-index: 0;
   }
  }
}
.timeTab {
  // position: absolute;
  // top: 22px;
  // right: 30px;
  // width: 100px;
  // height: 40px;
  // background: #FFFFFF;
  // border-radius: 20px 20px 0px 0px;
  // z-index: 999;
}
.chartsDiv {
  width: 100%;
  height: 500px;
  // margin-top: 100px;
}
.charts-nodata {
  width: 100%;
  height: 500px;
  // margin-top:100px ;
  text-align: center;
  line-height: 500px;
  font-size: 32px;
  color: #000000;
}

// input 框
.dateInput {
  width: 180px;
  height: 58px;
  line-height: 58px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d4d4d4;
  font-weight: 400;
  font-size: 22px;
  color: #000000;
  padding: 0 15px;
  // position: absolute;
  // right: 30px;
  // top: 130px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dateDiv {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  box-sizing: border-box;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}


</style>
