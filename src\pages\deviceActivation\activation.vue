<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />

  <div v-else class="page-content">
    <CustomNavTitle title="设备激活" :showBack="true" background="#fff" />
    <scroll-view class="active-content" scroll-y>
      <div class="batch-item">
        <div class="batch-item-left">
          <img :src="IconDevice" alt="" />
        </div>
        <div class="batch-item-right">
          <div class="name">{{ deviceInfo.serialNum || '--' }}</div>
          <div class="batch">设备名称：{{ deviceNameMap[deviceInfo.type] || '--' }}</div>
          <div class="batch">设备型号：{{ deviceInfo.innerModelName || '--' }}</div>
          <div class="batch">激活状态：{{ isActive === true ? '已激活' : '未激活' }}</div>
        </div>
      </div>

      <nut-form class="batch-item" ref="formRef" :model-value="formData" :rules="rules">
        <p class="label-text">激活人</p>
        <nut-form-item :label-width="80" prop="activeBy ">
          <nut-input
            v-model="formData.activeBy"
            placeholder="请输入激活人"
            type="text"
            @blur="customBlurValidate('activeBy ')"
          >
          </nut-input>
        </nut-form-item>

        <p class="label-text code">机器码</p>
        <nut-form-item :label-width="80" prop="imei">
          <nut-input
            v-model="formData.imei"
            placeholder="输入或扫码获取"
            @blur="customBlurValidate('imei')"
          >
            <template #right>
              <Scan2 color="#AAADB2" @click="showScanner = true" size="20" />
            </template>
          </nut-input>
        </nut-form-item>
      </nut-form>

      <div v-if="thisTimeActiveList && thisTimeActiveList.length > 0" class="actived">
        <div class="actived-title">
          <span>本次激活设备</span>
          <span class="actived-num">{{ thisTimeActiveList.length }}</span>
        </div>
        <div v-for="(item, index) in thisTimeActiveList" :key="index" class="actived-item">
          {{ item }}
        </div>
      </div>
    </scroll-view>

    <div class="bottom-button">
      <nut-button type="default" @click="goBack">取消</nut-button>
      <nut-button type="success" :loading="submitLoading" @click="submit">激活</nut-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Taro from '@tarojs/taro'
import IconDevice from '../../assets/icon_shachongdeng.png'
import AuthAPI from '../../api/auth'
import { Scan2 } from '@nutui/icons-vue-taro'
import DeviceActivationAPI from '../../api/deviceActivation'
import Scanner from '../../components/Scanner/Scanner.vue'

const deviceInfo = ref<any>({})
const isActive = ref<boolean>(false)
const deviceNameMap = {
  '1': '智慧杀虫灯',
  '2': '墒情气象一体机',
  '3': '虫情分析仪',
}
onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  getStatus()
  getUserInfo()
})

const goBack = () => {
  Taro.navigateBack()
}

const formData = ref({
  activeBy: '',
  imei: '',
  serialNum: '',
})

const getUserInfo = async () => {
  const res = await AuthAPI.me()
  formData.value.activeBy = res.nickname
}

const rules = {
  activeBy: [{ required: true, message: '请输入激活人' }],
  imei: [{ required: true, message: '请输入设备编号' }],
}

const formRef = ref()
// 失去焦点校验
const customBlurValidate = (prop) => {
  formRef.value?.validate(prop).then(({ valid, errors }) => {
    if (valid) {
      console.log('success:', formData.value)
    } else {
      console.warn('error:', errors)
    }
  })
}

const getStatus = async () => {
  const res = await DeviceActivationAPI.activated(deviceInfo.value.serialNum)
  isActive.value = res.activated
  if (isActive.value) {
    Taro.showModal({
      title: '提示',
      content: `设备${deviceInfo.value.serialNum}已激活`,
      cancelText: '重新绑定',
      confirmText: '下一个',
      success: (res) => {
        if (res.confirm) {
          getNextDevice(false)
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      },
    })
  }
}

const showScanner = ref(false)
const getImeiFromString = (str: string): string => {
  const match = str.match(/IMEI:(\d+)/)
  return match ? match[1] : ''
}
const toActivation = (result: string) => {
  formData.value.imei = getImeiFromString(result) || result
  showScanner.value = false
}

const submitLoading = ref(false)
const thisTimeActiveList = ref<string[]>([])
const submit = async () => {
  submitLoading.value = true
  const { activeBy, imei } = formData.value
  const params = {
    activeBy,
    imei,
    serialNum: deviceInfo.value.serialNum,
  }
  // 已绑定设备重新绑定需要先调用解绑接口
  if (deviceInfo.value.id) {
    Taro.showLoading({ title: '原有设备解绑中' })
    try {
      await DeviceActivationAPI.unbind(deviceInfo.value.id)
    } finally {
      submitLoading.value = false
      Taro.hideLoading()
    }
  }
  try {
    await DeviceActivationAPI.activate(params)
    Taro.showToast({
      title: '激活成功',
      icon: 'success',
    })

    getNextDevice()
  } finally {
    submitLoading.value = false
  }
}
const getNextDevice = (pushPre = true) => {
  // 激活成功后将deviceInfo.value.id置空以免再次激活下一个设备时解绑该设备
  deviceInfo.value.id = undefined

  if (pushPre) {
    thisTimeActiveList.value.push(deviceInfo.value.serialNum)
  }

  // 从 serialNum 中提取字母前缀和数字部分
  const prefix = deviceInfo.value.serialNum.match(/^[A-Za-z]+/)[0]
  const number = parseInt(deviceInfo.value.serialNum.match(/\d+/)[0])
  // 数字部分加1
  const newNumber = number + 1
  // 将新数字补齐到原来的位数
  const digits = deviceInfo.value.serialNum.match(/\d+/)[0].length
  const paddedNumber = newNumber.toString().padStart(digits, '0')
  // 拼接新的 serialNum
  const newSerialNum = `${prefix}${paddedNumber}`
  deviceInfo.value.serialNum = newSerialNum

  formData.value.imei = ''
  getStatus()
}
</script>

<style scoped lang="scss">
.batch-item {
  width: 690px;
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
  display: flex;
  .batch-item-left {
    margin-right: 20px;
    img {
      width: 60px;
      height: 60px;
    }
  }
  .batch-item-right {
    flex: 1;
    font-size: 28px;
    color: #909090;
    .name {
      font-size: 32px;
      color: #000;
      font-weight: bold;
    }
    .batch {
      margin: 10px 0;
    }
    .count {
      display: flex;
      justify-content: space-between;
    }
  }
}
.label-text {
  font-size: 30px;
  padding-left: 30px;
}
.code {
  margin-top: 20px;
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item) {
  .nut-input-inner {
    height: 78px;
    padding: 0 30px;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 630px;
  margin: 0 auto 0;
  border-radius: 20px;
}
.bottom-button {
  position: fixed;
  left: 0;
  bottom: 60px;
  padding: 0 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 0 auto;
  .nut-button {
    width: 330px;
  }
}
.actived-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #5b5b5b;
  font-size: 28px;
  .actived-num {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 38px;
    height: 38px;
    background: #e5f5ee;
    color: #02a25c;
    border-radius: 50%;
    font-size: 24px;
  }
}
.actived {
  width: 690px;
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
}
.active-content {
  height: calc(100vh - 200px);
}
.actived-item {
  color: #101010;
  font-size: 28px;
  border-top: 1px solid #e7e9ee;
  margin: 28px 0;
  padding-top: 28px;
}
</style>
