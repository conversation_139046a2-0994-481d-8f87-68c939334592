<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import gcoord from 'gcoord'
import { Search2 } from '@nutui/icons-vue-taro'
import loadBigemap from '../../utils/bigemap'
import Taro from '@tarojs/taro'
import { MAP_CONFIG, DEFAULT_CENTER, DEFAULT_ZOOM } from '@/config/map'

interface Position {
  longitude: number
  latitude: number
}

interface SearchResult {
  name: string
  address: string
  lonlat?: string
  location: {
    lon: number
    lat: number
  }
}

const props = withDefaults(
  defineProps<{
    modelValue?: Position
    address?: string
    showSearch?: boolean
  }>(),
  {
    showSearch: true,
  }
)

const emit = defineEmits(['change', 'close'])

const mapContainer = ref<HTMLElement>()
const searchInput = ref('')
const searchResults = ref<SearchResult[]>([])
const selectedPosition = ref<Position>(props.modelValue || DEFAULT_CENTER)
const showSearchResults = ref(false)
const loading = ref(false)

let map: any
let marker: any
let BM: any

onMounted(() => {
  loadBigeMap()
})

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [
      selectedPosition.value?.latitude || DEFAULT_CENTER.latitude,
      selectedPosition.value?.longitude || DEFAULT_CENTER.longitude,
    ],
    zoom: DEFAULT_ZOOM,
    zoomControl: false,
  })

  // 添加标记
  marker = BM.marker([selectedPosition.value.latitude, selectedPosition.value.longitude]).addTo(map)

  // 点击地图更新位置
  map.on('click', async (e: any) => {
    if (!props.showSearch) return
    const { lat, lng } = e.latlng
    if (marker) {
      marker.setLatLng([lat, lng])
    } else {
      marker = BM.marker([lat, lng]).addTo(map)
    }

    // 百度坐标转WGS84
    const wgs84Coord = gcoord.transform([lng, lat], gcoord.BD09, gcoord.WGS84)
    getAddrees(wgs84Coord[0], wgs84Coord[1])

    // 更新位置和地图视图
    selectedPosition.value = { longitude: lng, latitude: lat }
    map.flyTo([lat, lng], 17)
  })
}

let positionData: any = {}

const getAddrees = async (lng, lat) => {
  // 使用天地图API进行逆地理编码
  try {
    const response = await fetch(
      `https://api.tianditu.gov.cn/geocoder?postStr={"lon":${lng},"lat":${lat},"ver":1}&type=geocode&tk=${MAP_CONFIG.TIANDITU_KEY}`
    )
    const data = await response.json()

    if (data.status === '0' && data.result) {
      const address = data.result.formatted_address
      searchInput.value = address
    }

    const { formatted_address, location } = data.result
    const { province_code, county_code, city_code } = data.result.addressComponent
    positionData = {
      address: formatted_address,
      coordinate: { longitude: location.lon, latitude: location.lat },
      provinceCode: province_code,
      cityCode: city_code,
      countyCode: county_code,
    }
  } catch (error) {
    console.error('获取地址失败:', error)
    Taro.showToast({
      title: '获取地址信息失败',
      icon: 'none',
    })
  }
}

const searchLocation = async () => {
  if (!searchInput.value) {
    Taro.showToast({
      title: '请输入搜索关键词',
      icon: 'none',
    })
    return
  }

  loading.value = true
  showSearchResults.value = true

  try {
    const response = await fetch(
      `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${searchInput.value}","queryType":12,"start":0,"count":10,"specify":"156000000"}&type=query&tk=${MAP_CONFIG.TIANDITU_KEY}`
    )
    const data = await response.json()

    if (data.pois && data.pois.length > 0) {
      searchResults.value = data.pois.map((poi: any) => ({
        name: poi.name,
        address: poi.address,
        lonlat: poi.lonlat,
        location: {
          lon: Number(poi.lonlat.split(',')[0]),
          lat: Number(poi.lonlat.split(',')[1]),
        },
      }))
    } else if (data.area && Object.keys(data.area).length > 0) {
      const area = data.area
      searchResults.value = [
        {
          name: area.name,
          address: area.address || area.name,
          lonlat: area.lonlat,
          location: {
            lon: Number(area.lonlat.split(',')[0]),
            lat: Number(area.lonlat.split(',')[1]),
          },
        },
      ]
      // 如果只有一个区域结果，直接定位到该位置
      selectSearchResult(searchResults.value[0])
    } else {
      Taro.showToast({
        title: '未找到相关地址',
        icon: 'none',
      })
      searchResults.value = []
    }
  } catch (error) {
    console.error('搜索位置失败:', error)
    Taro.showToast({
      title: '搜索失败，请重试',
      icon: 'none',
    })
    searchResults.value = []
  } finally {
    loading.value = false
  }
}

const selectSearchResult = (result: SearchResult) => {
  const { lon, lat } = result.location
  getAddrees(lon, lat)
  updatePosition(lon, lat)
  showSearchResults.value = false
  searchInput.value = result.name
}

const updatePosition = (longitude: number, latitude: number) => {
  const result = gcoord.transform(
    [longitude, latitude], // 经纬度坐标
    gcoord.WGS84, // 当前坐标系
    gcoord.BD09 // 目标坐标系
  )
  selectedPosition.value = { longitude: result[0], latitude: result[1] }
  if (marker) {
    marker.setLatLng([result[1], result[0]])
  } else {
    marker = BM.marker([result[1], result[0]]).addTo(map)
  }

  map.flyTo([result[1], result[0]], 17)
}

// 点击外部关闭搜索结果
const handleClickOutside = (event: MouseEvent) => {
  const searchBox = document.querySelector('.search-box')
  if (searchBox && !searchBox.contains(event.target as Node)) {
    showSearchResults.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
const handleComfirm = () => {
  emit('change', positionData)
  emit('close')
}
const handleClose = () => {
  emit('close')
  searchInput.value = ''
  // emit('change', {})
}

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal?.latitude && newVal?.longitude) {
      // 将 WGS84 坐标转换为 BD09 坐标
      const bdCoord = gcoord.transform(
        [newVal.longitude, newVal.latitude],
        gcoord.WGS84,
        gcoord.BD09
      )

      selectedPosition.value = {
        longitude: bdCoord[0],
        latitude: bdCoord[1],
      }
      console.log('selectedPosition', selectedPosition.value)
      // 初始化地图时更新标记位置
      if (map && !marker) {
        marker = BM.marker([bdCoord[1], bdCoord[0]]).addTo(map)
        map.flyTo([bdCoord[1], bdCoord[0]], DEFAULT_ZOOM)
      }

      if (map && marker) {
        marker.setLatLng([bdCoord[1], bdCoord[0]])
        map.flyTo([bdCoord[1], bdCoord[0]], DEFAULT_ZOOM)
      }
    } else {
      marker?.remove()
      marker = null
    }
  },
  { immediate: true }
)

watch(
  () => props.address,
  (newVal) => {
    if (newVal) {
      searchInput.value = newVal
    } else {
      searchInput.value = ''
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="position-select">
    <CustomNavTitle :title="props.showSearch ? '地址选择' : '地址查看'" :showBack="false" />

    <div v-if="props.showSearch" class="search-box">
      <!-- <nut-input
        v-model="searchInput"
        placeholder="请输入地址关键词"
        class="search-input"
        :loading="loading"
        clearable
        @keyup.enter="searchLocation"
      >
        <template #right>
          <nut-button :loading="loading" @click="searchLocation">
            <Search2 />
          </nut-button>
        </template>
      </nut-input> -->
      <nut-searchbar v-model="searchInput" placeholder="输入地址搜索">
        <template #rightin>
          <Search2 @click="searchLocation" />
        </template>
      </nut-searchbar>

      <div v-if="showSearchResults && searchResults.length" class="search-results">
        <div
          v-for="result in searchResults"
          :key="result.lonlat"
          class="search-result-item"
          @click="selectSearchResult(result)"
        >
          <div class="name">{{ result.name }}</div>
          <div class="address">{{ result.address }}</div>
        </div>
      </div>
    </div>

    <div ref="mapContainer" class="map-container" />
  </div>
  <div class="bottom-buttons">
    <nut-button v-if="props.showSearch" type="default" @click="handleClose">取消</nut-button>
    <nut-button v-if="props.showSearch" type="primary" @click="handleComfirm">确定</nut-button>
    <nut-button style="width: 100% !important" v-else type="primary" @click="handleClose"
      >确定</nut-button
    >
  </div>
</template>

<style lang="scss" scoped>
.bottom-buttons {
  width: 690px;
  position: absolute;
  left: 50%;
  bottom: 50px;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  .nut-button {
    width: 300px !important;
  }
}
.position-select {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.search-box {
  position: absolute;
  width: 100%;
  top: 100px;
  padding-bottom: 10px;
  z-index: 1000;
  background: #fff;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-height: 600px;
  overflow-y: auto;
}

.search-result-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &:last-child {
    border-bottom: none;
  }

  .name {
    font-size: 28px;
    color: #303133;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .address {
    font-size: 24px;
    color: #909399;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.map-container {
  width: 100vw;
  height: calc(100vh - 190px);
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

// 搜索结果动画
.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.el-zoom-in-top-enter-from,
.el-zoom-in-top-leave-to {
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
}
</style>
