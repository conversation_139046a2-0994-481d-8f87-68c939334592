import request from '../../utils/request'

export default class MonitorAPI {
  /** 列表 接口*/
  /** 列表 接口*/
  static pageList(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/device/camera/page`,
      method: 'get',
      params: data,
    })
  }
  // // 搜索设备
  // static searchDevice(serialNum: string): Promise<Device[]> {
  //   return request({
  //     url: "/api/v1/device/common/options",
  //     method: "get",
  //     params: { deviceType: 3, serialNum },
  //   });
  // }

  // 获取监控列表
  static monitorList(params: MonitorListParams): Promise<BatchListResponse> {
    return request({
      url: '/api/v1/manage/h5/device/page',
      method: 'get',
      params,
    })
  }

  /** 查看录像列表*/
  static checkVideoList(deviceId, channelId, data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/gb_record/query/${deviceId}/${channelId}`,
      method: 'get',
      params: data,
    })
  }
  // 设备录像查询
  static checkVideoUrl(deviceId, channelId, data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/playback/start/${deviceId}/${channelId}`,
      method: 'get',
      params: data,
    })
  }

  // 新增监控设备
  static addMonitor(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/device/camera`,
      method: 'post',
      data,
    })
  }

  // ....................... 预览...................

  // 国标设备 - 语音广播命令
  static gbBroadcast(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/play/broadcast/${data.deviceId}/${data.channelId}?timeout=${data.timeout}&broadcastMode=${data.broadcastMode}`,
      method: 'get',
    })
  }

  //   获取推流鉴权Key(系统 - 获取用户信息)
  static gbUserInfo() {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/user/userInfo`,
      method: 'get',
    })
  }

  // 云台控制
  static gbPtzControl(deviceId, channelId, data: PtzControlData) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/front-end/ptz/${deviceId}/${channelId}`,
      method: 'get',
      params: data,
    })
  }

  // 国标设备 - 开始点播
  static gbPlaybackStart(deviceId, channelId) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/play/start/${deviceId}/${channelId}`,
      method: 'get',
    })
  }

  //推流管理 - 获取流信息(编码信息)
  static gbStreamInfo(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/camera/control/api/server/media_server/media_info`,
      method: 'get',
      params: data,
    })
  }
}

interface ActivateRequest {
  serialNum: string
  activeBy: string
  imei: string
}

interface Device {
  serialNum: string
  deviceName: string
  [key: string]: any
}

interface MonitorListParams {
  type: number
  pageNum: number
  pageSize: number
  keywords?: string
  sold?: boolean
}

interface BatchListResponse {
  records: {
    id: number | string
    batchNum: string
    model: string
    deviceTotal: number
    activatedCount: number
  }[]
  total: number
}

export interface ListRequest {
  /**
   * 批次号
   */
  // batchNum?: string;
  /**
   * 关键字
   */
  // keywords?: string;
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  /**
   * 设备类型
   */
  type: number
  [property: string]: any
}

/** 云台控制请求参数 */
export interface PtzControlData {
  // deviceId: string // 设备国标编号
  // channelId: string // 通道国标编号
  command: string // 控制指令,允许值: left, right, up, down, upleft, upright, downleft, downright, zoomin, zoomout, stop
  horizonSpeed?: number // 水平速度(0-255)
  verticalSpeed?: number // 垂直速度(0-255)
  zoomSpeed?: number
}
