<script setup lang="ts">
/**
 * 组件外部传进来的设备id
 * */
// 在 Vue 3.3 之前的版本中，没有 `defineModel`，可以使用 `defineProps` 和 `defineEmits` 来实现类似的功能
import { defineProps, defineEmits,ref } from 'vue';

const props = defineProps({
  tabList: {
    type: Array as () => any[],
    required: true,
  },
})

// const emits = defineEmits<{
//   (e: 'update:tabList', value: any[]): void;
// }>();
// const tabList = computed({
//   get() {
//     return props.tabList;
//   },
//   set(value) {
//     emits('update:tabList', value);
//   }
// });
// watch(tabList, (val) => {
//   if (val) {
//     nextTick(() => {
//       console.log(val, '.................', tabList)
//     })
//   }
// })

// props.tabList 本身就是数组，不需要 .value
const tableIndex = ref(props.tabList[0].id)

// 使用泛型定义 emits 类型，明确指定事件名称和参数类型
const emits = defineEmits<{
  (e: 'change', id: number): void;
}>();

function handleChange(item) {
  tableIndex.value = item.id
  emits('change', item.id)
}
</script>

<template>
  <div class="tabDiv">
    <div v-for="(item, index) in props.tabList" :key="index" class="unCheck" :class="tableIndex === item.id ? 'check' : ''" @click="handleChange(item)">
      {{ item.name }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
// .tab切换样式
.tabDiv {
  display: flex;
  .unCheck {
    font-weight: 400;
    font-size: 28px;
    color: #ABABAB;
    margin-right: 38px;
    // text-align: center;
  }
  > div:nth-last-child(1) {
    margin-right: 0px;
  }
  .check {
    font-weight: blob;
    font-size: 28px;
    color: #02A15B;
    text-decoration: underline;
  }
}
</style>
