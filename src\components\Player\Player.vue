<script setup lang="ts">
import { onMounted, onUnmounted, useTemplateRef, watch } from 'vue'
import { IconFont } from '@nutui/icons-vue-taro'

/**
 * 官方文档
 * @see https://jessibuca.com/api.html
 * **/
const { videoUrl, loading = false } = defineProps<{
  loading?: boolean
  // 视频地址
  videoUrl: string
}>()
const emit = defineEmits<{ (e: 'timeUpdate', delta: number) }>()

let player: Jessibuca = null

let preTimestamp = 0
function timeUpdate(e: number) {
  const delta = e - preTimestamp
  if (delta < 500) {
    emit('timeUpdate', e - preTimestamp)
  }

  preTimestamp = e
}

let offWatch = () => {}

const videoContainerRef = useTemplateRef('videoContainerRef')
onMounted(() => {
  player = new Jessibuca({
    container: videoContainerRef.value,
    videoBuffer: 0.1, // 缓存时长
    isResize: false,
    supportDblclickFullscreen: true,
    keepScreenOn: true, // 开启屏幕常亮，在手机浏览器上, canvas标签渲染视频并不会像video标签那样保持屏幕常亮
    hotKey: true, // 否开启键盘快捷键
    controlAutoHide: true, // 底部控制台是否自动隐藏
    useWebFullScreen: true, //是否使用web全屏(旋转90度)（只会在移动端生效）
    loadingText: '加载中...',
    decoder: '/jessibuca/decoder.js',
    useMSE: true,
    debug: false,
    showBandwidth: false, // 显示网速
    operateBtns: {
      fullscreen: true,
      screenshot: true,
      play: true,
      audio: true,
    },
    isNotMute: false,
    useWCS: true,
    autoWasm: true,
  })

  player.on('timeUpdate', timeUpdate)

  offWatch = watch(
    () => videoUrl,
    (url) => {
      if (url) {
        player.play(videoUrl)
      }
    },
    { immediate: true }
  )
})

onUnmounted(() => {
  offWatch()
  if (player) {
    player.destroy().finally(() => {
      player = null
    })
  }
})
</script>

<template>
  <div class="liveVideo relative">
    <div ref="videoContainerRef" class="bg-black" />
    <div v-if="loading" class="absolute inset-0 h-full grid place-items-center">
      <IconFont name="loading"></IconFont>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
