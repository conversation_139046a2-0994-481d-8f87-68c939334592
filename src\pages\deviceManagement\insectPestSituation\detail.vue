<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />

  <div v-else class="page-content">
    <CustomNavTitle title="虫情分析仪" :showBack="true" background="#fff" />

    <div v-if="baseData.coordinate?.latitude" ref="mapContainer" class="h-30" />

    <div class="batch-item info-box-title">
      <div class="batch-item-left">
        <img :src="baseData?.online ? IconDevice : UnIconDevice" alt="" />
      </div>
      <div class="batch-item-right">
        <div class="name">{{ baseData.serialNum || '--' }}</div>
        <div class="batch">设备型号：{{ baseData.innerModelName || '--' }}</div>
        <div class="batch">安装状态：{{ baseData.installed ? '已安装' : '未安装' }}</div>

        <div v-if="baseData.coordinate?.latitude">
          <span class="vertical-mid">{{ baseData.address }}</span>
          <span
            @click.stop="openGMap"
            class="ml-1 inline-flex items-center vertical-mid gap-x-0.5"
            text="#1688d1 2.4"
          >
            <image src="@/assets/deviceMap/locate.png" alt="" class="w-2.4 h-2.5" />
            导航
          </span>
        </div>
      </div>
    </div>

    <div v-if="tabList?.length > 0" class="p-3">
      <div class="tab-top-border">
        <CustomTabs
          style="border-top-left-radius: 4px; border-top-right-radius: 4px"
          v-model="status"
          :tab-list="tabList"
        />
      </div>
      <div
        v-show="status === 0"
        class="monitor-box-bg"
        style="margin-bottom: 0 !important; border-radius: 0 !important"
      >
        <div class="flex flex-col">
          <div class="flex flex-row justify-between">
            <div class="flex flex-row">
              <div class="text-2.7 text-#707070">设备状态：</div>
              <div class="text-2.7 text-#101010">
                {{ deviceDetailData.online ? '在线' : '离线' }}
              </div>
            </div>
            <div class="flex flex-row">
              <div class="text-2.7 text-#707070">电量：</div>
              <div class="text-2.7 text-#101010">
                {{
                  deviceDetailData.batteryLevel != null ? `${deviceDetailData.batteryLevel}%` : '--'
                }}
              </div>
            </div>
          </div>
          <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div>
          <div class="flex flex-row justify-between mt-2.5">
            <div class="text-2.7 text-#707070">存储空间</div>
            <div class="text-2.7 text-#101010">
              {{ usedSpaceInGB }} /
              {{ totalSpaceInGB }}
              GB
            </div>
          </div>
          <div class="flex flex-row justify-between mt-2.5">
            <div class="text-2.7 text-#707070">本次运行时间</div>
            <div class="text-2.7 text-#101010">
              {{ deviceDetailData.uptime ? convertMinutes(deviceDetailData.uptime) : '--' }}
            </div>
          </div>
          <div class="flex flex-row justify-between mt-2.5">
            <div class="text-2.7 text-#707070">累计拍照次数</div>
            <div class="text-2.7 text-#101010">{{ deviceDetailData?.snapCount || 0 }}</div>
          </div>
          <div class="flex flex-row justify-between mt-2.5">
            <div class="text-2.7 text-#707070">最近拍照时间</div>
            <div class="text-2.7 text-#101010">
              {{ deviceDetailData?.lastSnapTime || '--' }}
            </div>
          </div>
          <div class="flex flex-row justify-between mt-2.5">
            <div class="text-2.7 text-#707070">数据更新时间</div>
            <div class="text-2.7 text-#101010">{{ deviceDetailData?.time || '--' }}</div>
          </div>
          <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div>
          <div class="flex justify-end flex-wrap mt-2.5 gap-2.5">
            <div v-if="deviceDetailData.online" class="completeGreenDetail" @click="snapshot">
              触发执行
            </div>
            <div v-if="deviceDetailData.online" class="completeGreenDetail" @click="clearInset">
              手动清虫
            </div>
            <div class="completeGreenDetail" @click="insectecord">虫情记录</div>
            <div class="completeGreenDetail" @click="analys">虫情分析</div>
          </div>
        </div>
      </div>
      <div
        v-show="status === 0"
        class="monitor-box-bg !mb-25"
        style="border-top-left-radius: 0 !important; border-top-right-radius: 0 !important"
      >
        <div class="flex flex-row justify-between">
          <div class="text-2.8 text-#101010 font-550">设备控制</div>
          <div class="completeGreenDetail" @click="saveControlMode">保存</div>
        </div>
        <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div>
        <!-- <div class="text-2.8 text-#101010 font-550 mt-2.5">拍照是否开启闪光灯</div> -->
        <div class="flex flex-row justify-between mt-2.5">
          <div class="text-2.8 text-#101010 font-550">拍照是否开启闪光灯</div>
          <nut-switch v-model="deviceDetailData.controlConfig.flashlightEnabled" />
        </div>
        <div class="text-2.8 text-#101010 font-550 mt-2.5">正常模式</div>
        <nut-form ref="normalRef" :model-value="deviceDetailData.controlConfig.normalMode">
          <nut-form-item
            prop="startTime"
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请选择起始时间' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070">起始时间</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5 datePd"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  handleTimePopup(
                    deviceDetailData?.controlConfig?.normalMode?.startTime,
                    'startTime',
                    'normalMode'
                  )
                "
              >
                <div
                  :style="{
                    color: deviceDetailData?.controlConfig?.normalMode?.startTime ? '#101010' : '',
                  }"
                >
                  {{ deviceDetailData?.controlConfig?.normalMode?.startTime || '请选择时间' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="clock"></IconFont>
              </div>
            </div>
          </nut-form-item>
          <nut-form-item
            prop="endTime"
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请选择结束时间' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070">结束时间</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5 datePd"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  handleTimePopup(
                    deviceDetailData?.controlConfig?.normalMode?.endTime,
                    'endTime',
                    'normalMode'
                  )
                "
              >
                <div
                  :style="{
                    color: deviceDetailData?.controlConfig?.normalMode?.endTime ? '#101010' : '',
                  }"
                >
                  {{ deviceDetailData?.controlConfig?.normalMode?.endTime || '请选择时间' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="clock"></IconFont>
              </div>
            </div>
          </nut-form-item>
          <nut-form-item
            prop="interval"
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请输入拍照间隔' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070 mb-2.5">拍照间隔</div>
              <nut-input
                v-model="deviceDetailData.controlConfig.normalMode.interval"
                clearable
                type="number"
              >
                <template #right>
                  <div class="bg-#DFDFDF h-4 w-0.23 mr-2.5"></div>
                  <div class="text-#101010">秒</div> </template
                >nut-input-inner
              </nut-input>
            </div>
          </nut-form-item>
        </nut-form>
        <div class="w-100% bg-#DFDFDF h-0.23 mt-3"></div>
        <div class="flex flex-row justify-between mt-2.5">
          <div class="text-2.8 text-#101010 font-550">休眠模式</div>
          <nut-switch v-model="deviceDetailData.controlConfig.sleepMode.enabled" />
        </div>
        <nut-form
          v-show="deviceDetailData?.controlConfig?.sleepMode?.enabled"
          ref="sleepRef"
          :model-value="deviceDetailData.controlConfig.sleepMode"
        >
          <nut-form-item
            prop="startDate "
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请选择起始日期' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070">起始日期</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5"
                p="x-2 y-1"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  handleDatePopup(deviceDetailData.controlConfig.sleepMode.startDate, 'startDate')
                "
              >
                <div
                  :style="{
                    color: deviceDetailData.controlConfig.sleepMode.startDate ? '#101010' : '',
                  }"
                >
                  {{ deviceDetailData.controlConfig.sleepMode.startDate || '请选择日期' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="clock"></IconFont>
              </div>
            </div>
          </nut-form-item>
          <nut-form-item
            prop="endDate"
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请选择结束日期' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070">结束日期</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5"
                p="x-2 y-1"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  handleDatePopup(deviceDetailData.controlConfig.sleepMode.endDate, 'endDate')
                "
              >
                <div
                  :style="{
                    color: deviceDetailData.controlConfig.sleepMode.endDate ? '#101010' : '',
                  }"
                >
                  {{ deviceDetailData.controlConfig.sleepMode.endDate || '请选择日期' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="clock"></IconFont>
              </div>
            </div>
          </nut-form-item>
          <nut-form-item
            prop="photoTime"
            required
            :style="{ padding: '0' }"
            :rules="[{ required: true, message: '请选择拍照时间' }]"
          >
            <div class="flex flex-col mt-2.5">
              <div class="text-2.7 text-#707070">拍照时间</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5"
                p="x-2 y-1"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  handleTimePopup(
                    deviceDetailData.controlConfig.sleepMode.photoTime,
                    'photoTime',
                    'sleepMode'
                  )
                "
              >
                <div
                  :style="{
                    color: deviceDetailData.controlConfig.sleepMode.photoTime ? '#101010' : '',
                  }"
                >
                  {{ deviceDetailData.controlConfig.sleepMode.photoTime || '请选择时间' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="clock"></IconFont>
              </div>
            </div>
          </nut-form-item>
        </nut-form>
      </div>
      <div v-if="status === 1" class="monitor-box-bg">
        <div class="text-item flex-between">
          <div>客户</div>
          <div class="text-item-right">{{ customerData.name || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>客户区域</div>
          <div class="text-item-right">{{ customerData.areaName || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人</div>
          <div class="text-item-right">{{ customerData.respPerson || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人电话</div>
          <div class="text-item-right">{{ customerData.phone || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>通讯地址</div>
          <div class="text-item-right">{{ customerData.address || '--' }}</div>
        </div>
      </div>
      <div v-if="status === 2">
        <div class="monitor-box-bg">
          <div class="monitor-title">安装信息</div>
          <div class="monitor-line"></div>
          <div class="text-item flex-between">
            <div>工单</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.workOrderNo || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装人员</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installerName || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>手机号码</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.phoneNumber || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装时间</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installTime || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装经纬度</div>
            <div class="text-item-right">
              <span v-if="afterSalesData?.installInfo?.coordinate">
                {{ afterSalesData?.installInfo?.coordinate?.longitude }},
                {{ afterSalesData?.installInfo?.coordinate?.latitude }}
              </span>
              <span v-else>--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div class="text-item-left">安装照片</div>
            <div class="text-item-right">
              <VImage
                v-for="item in afterSalesData?.installInfo?.images"
                :key="item"
                class="img"
                :src="item.url"
                :preview-src-list="afterSalesData?.installInfo?.images.map((item) => item.url)"
              />
              <span
                v-if="
                  !afterSalesData?.installInfo?.images ||
                  afterSalesData?.installInfo?.images?.length < 1
                "
                >暂无图片</span
              >
            </div>
          </div>
        </div>
        <div
          class="monitor-box-bg"
          v-if="afterSalesData?.workOrders && afterSalesData?.workOrders?.length > 0"
        >
          <div class="monitor-title">工单记录</div>
          <div
            v-for="(item, index) in afterSalesData?.workOrders"
            :key="index"
            style="position: relative"
          >
            <div class="monitor-line"></div>
            <div class="text-item">
              <div class="text-item-left">工单</div>
              <div class="text-item-center">{{ item.workOrderNo || '--' }}</div>
            </div>
            <div class="text-item">
              <div class="text-item-left">工单类型</div>
              <div class="text-item-center">
                {{ item.type === 1 ? '安装工单' : item.type === 2 ? '售后工单' : '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">故障原因</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.issueTypeName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交人</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.createByName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交时间</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.createTime || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="status === 3" class="mb-20">
        <div v-for="(item, index) in alarmItemsData" :key="index" class="monitor-box-bg">
          <div class="text-item flex-between">
            <div>告警项</div>
            <div class="text-item-right">{{ item.itemName || '--' }}</div>
          </div>
          <div class="text-item flex-between">
            <div>告警阈值</div>
            <div class="text-item-right">
              <span v-if="item.valueList && item.valueList.length > 0">
                {{
                  item.valueList.some((x) => x.value === item.value)
                    ? item.valueList.filter((x) => x.value === item.value)[0].label
                    : item.value
                }}
              </span>
              <span v-else
                >{{ item.value || '--' }}
                <span>{{ item.unit || '' }}</span>
              </span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>条件</div>
            <div class="text-item-right">
              {{ item.operator ? String(options[item.operator - 1]) : '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>平台推送方式</div>
            <div class="text-item-right">
              <span v-if="item.managePushMethod && item.managePushMethod.includes(1)">后台</span>
              <span
                v-if="
                  item.managePushMethod &&
                  item.managePushMethod.includes(1) &&
                  item.managePushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.managePushMethod && item.managePushMethod.includes(2)">短信</span>
              <span v-if="!item.managePushMethod || item.managePushMethod.length === 0">--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>客户推送方式</div>
            <div class="text-item-right">
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(1)"
                >后台</span
              >
              <span
                v-if="
                  item.customerPushMethod &&
                  item.customerPushMethod.includes(1) &&
                  item.customerPushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(2)"
                >短信</span
              >
              <span v-if="!item.customerPushMethod || item.customerPushMethod.length === 0"
                >--</span
              >
            </div>
          </div>
          <div class="monitor-line"></div>
          <div class="flex justify-end items-center">
            <div class="completeGreen" @click="addAlarm(item)">编辑</div>
            <div class="deleteRed left-15" @click="deleteAlarm(item)">删除</div>
          </div>
        </div>
        <emput v-if="!alarmItemsData || alarmItemsData?.length < 1" message="暂无数据" />
      </div>
    </div>

    <div v-show="status === 3" class="position-absolute w-100% bottom-0 flex justify-center">
      <div class="mt-2.5 position-relative w-80vw mb-4">
        <nut-button type="primary" size="large" @click="addAlarm"> 新增 </nut-button>
      </div>
    </div>

    <nut-dialog
      title="确定删除该告警设置？"
      v-model:visible="visibleShowAlarm"
      @cancel="onCancelAlarm"
      @ok="onOkAlarm"
    />

    <nut-popup v-model:visible="showPopup" position="bottom">
      <nut-picker
        v-model="choose"
        :columns="optionList"
        :title="option === 'itemId' ? '告警项' : option === 'value' ? '阈值' : '条件'"
        @confirm="chooseOption"
        @cancel=";(showPopup = false), (choose = []), (optionList = [])"
      />
    </nut-popup>

    <nut-popup v-model:visible="showTimePopup" position="bottom">
      <nut-date-picker
        v-model="chooseTime"
        type="hour-minute"
        :three-dimensional="false"
        :min-date="minTime"
        :max-date="maxTime"
        @confirm="confirmTime"
        @cancel=";(showTimePopup = false), (chooseTime = '')"
      ></nut-date-picker>
    </nut-popup>

    <nut-popup v-model:visible="showDatePopup" position="bottom">
      <nut-date-picker
        v-model="chooseDate"
        type="month-day"
        :min-date="minDate"
        :max-date="maxDate"
        :three-dimensional="false"
        @confirm="confirmDate"
        @cancel=";(showDatePopup = false), (chooseDate = '')"
      ></nut-date-picker>
    </nut-popup>
  </div>
</template>

<script setup lang="ts">
import { IconFont } from '@nutui/icons-vue-taro'
import { computed, onMounted, ref } from 'vue'
import Taro, { eventCenter, getCurrentInstance } from '@tarojs/taro'
import IconDevice from '@/assets/device/<EMAIL>'
import UnIconDevice from '@/assets/device/<EMAIL>'
import DeviceActivationAPI from '@/api/deviceActivation'
import BaseInfoAPI from '@/api/device/baseInfo'
import InsectPestAPI from '@/api/device/insectPest'
import Scanner from '@/components/Scanner/Scanner.vue'
import loadBigemap from '@/utils/bigemap'
import { classMap, DEFAULT_CENTER, deviceBottomIconMap, deviceIconMap2 } from '@/config/map'
import iconBottomAlarm from '@/assets/mapIcons/bottom/alarm.webp'
import iconBottomOffline from '@/assets/mapIcons/bottom/offline.webp'

import { markerAlarmMap, markerMap } from '@/config/map'

import gcoord from 'gcoord'

const mapContainer = ref<HTMLElement>()

let map: any
let BM: any

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = async () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [DEFAULT_CENTER.latitude, DEFAULT_CENTER.longitude],
    zoom: 16,
    minZoom: 5,
    zoomControl: false,
    maxZoom: 18,
  })

  drawMarker(baseData.value)
}

const deviceInfo = ref<any>({})

const id = ref()
onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  // id.value = JSON.parse(decodeURIComponent(params.id));
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  console.log(deviceInfo.value, 11111, Taro.getCurrentInstance().router)

  getDetailBase()
  // getUserInfo();

  // 监听从back页面返回的事件
  const router = getCurrentInstance()?.router
  if (router?.onShow) {
    eventCenter.on(router.onShow, () => {
      getDeviceAlarmItems()
    })
  }
})

const status = ref(0)
const tabList = ref([
  {
    title: '设备信息',
    paneKey: 0,
    count: '',
  },
  {
    title: '客户信息',
    paneKey: 1,
    count: '',
  },
  {
    title: '售后信息',
    paneKey: 2,
    count: '',
  },
  {
    title: '告警设置',
    paneKey: 3,
    count: '',
  },
])

const baseData = ref({
  version: '',
  iccd: '',
  coordinate: {},
}) // 基础信息

const deviceDetailData = ref({
  online: false, // 是否在线
  batteryLevel: '', // 电量
  totalSpace: '', // 总空间
  usedSpace: '', // 使用空间
  uptime: '', // 运行时间
  snapCount: '', // 拍照次数
  lastSnapTime: '', // 最近拍照时间
  controlConfig: {
    normalMode: {
      startTime: '', // 起始时间
      endTime: '', // 截止时间
      interval: 0, // 拍照间隔
    },
    sleepMode: {
      enabled: false, // 开关
      startDate: '', // 开始日期
      endDate: '', // 截止日期
      photoTime: '', // 拍照时间
    },
    flashlightEnabled:false, // 闪光灯
  },
}) // 设备信息
const customerData = ref() // 客户信息
const afterSalesData = ref() // 售后信息
const alarmItemsData = ref() // 告警设置信息
const showPopup = ref(false) // 条件下拉
const options = ref(['等于', '不等于', '大于', '小于', '大于等于', '小于等于', '之间']) // 条件列表
const choose = ref([]) // 通用Picker选项
const optionList = ref([]) // 通用选项列表
const option = ref('') // 通用选项item
const valueMap = ref<Record<number, any>>({}) // 告警项 valueList
const showTimePopup = ref(false) // 时间下拉
const chooseTime = ref() // 选择中的时间
const chooseTimeOption = ref() // 操作的时间对象
const chooseTimeParentOption = ref() // 表单对象
const showDatePopup = ref(false) // 日期下拉
const chooseDate = ref() /// 选中的日期
const chooseDateOption = ref() // 操作日期对象
const minTime = ref() // 最小时间
const maxTime = ref() // 最大时间
const minDate = ref() // 最小日期
const maxDate = ref() // 最大日期

/**
 * 虫情分析
 */
function analys() {
  Taro.navigateTo({
    url: `/pages/deviceManagement/insectPestSituation/analys?data=${encodeURIComponent(
      JSON.stringify(deviceInfo.value.id)
    )}`,
  })
}

/**
 * 去虫情记录
 */
function insectecord() {
  Taro.navigateTo({
    url: `/pages/deviceManagement/insectPestSituation/recordIndex?data=${encodeURIComponent(
      JSON.stringify(baseData?.value.serialNum)
    )}`,
  })
}
/**
 * 拍照
 */
function snapshot() {
  InsectPestAPI.snapshot(deviceInfo.value.id).then((res) => {
    Taro.showToast({
      title: '操作成功',
      icon: 'success',
    })
  })
}

/**
 * 清虫
 * */
function clearInset() {
  InsectPestAPI.clearInset(deviceInfo.value.id).then((res) => {
    Taro.showToast({
      title: '操作成功',
      icon: 'success',
    })
  })
}

/**
 * 保存设备控制
 */
function saveControlMode() {
  const data = {
    id: deviceInfo.value.id,
    normalMode: deviceDetailData.value.controlConfig.normalMode,
    sleepMode: deviceDetailData.value.controlConfig.sleepMode,
    flashlightEnabled: deviceDetailData.value.controlConfig.flashlightEnabled || false,
  }
  BaseInfoAPI.updateDeviceControl(data).then((res) => {
    Taro.showToast({
      title: '操作成功',
      icon: 'success',
    })
  })
}

/**
 * 日期选中
 */
function confirmDate({ selectedValue }) {
  let startDate = new Date()
  let endDate = new Date()
  switch (chooseDateOption.value) {
    case 'startDate':
      startDate.setMonth(selectedValue[0] - 1, selectedValue[1])
      minDate.value = startDate
      endDate.setMonth(11, 30)
      maxDate.value = endDate
      deviceDetailData.value.controlConfig.sleepMode.endDate = ''
      break
    case 'endDate':
      endDate.setMonth(selectedValue[0] - 1, selectedValue[1])
      maxDate.value = endDate
      startDate.setMonth(0, 1)
      minDate.value = startDate
      break
    default:
      break
  }
  deviceDetailData.value.controlConfig.sleepMode[
    `${chooseDateOption.value}`
  ] = `${selectedValue[0]}-${selectedValue[1]}`
  chooseDate.value = ''
  showDatePopup.value = false
}

/**
 * 打开日期下拉
 */
function handleDatePopup(value, item) {
  if (value) {
    let date = new Date()
    date.setMonth(Number(value.split('-')[0]) - 1, value.split('-')[1])
    chooseDate.value = date
  } else {
    chooseDate.value = ''
  }
  chooseDateOption.value = item
  showDatePopup.value = true
}

/**
 * 打开时间下拉
 */
function handleTimePopup(value, item, parentItem) {
  let date = new Date()
  if (value) {
    date.setHours(value.split(':')[0], value.split(':')[1])
  }
  chooseTime.value = value ? date : ''
  chooseTimeOption.value = item
  chooseTimeParentOption.value = parentItem
  showTimePopup.value = true
}

/**
 * 时间选中
 */
function confirmTime({ selectedValue }) {
  let startDate = new Date()
  let endDate = new Date()
  switch (chooseTimeOption.value) {
    case 'startTime':
      startDate.setHours(selectedValue[0], selectedValue[1])
      minTime.value = startDate
      endDate.setHours(23, 59)
      maxTime.value = endDate
      deviceDetailData.value.controlConfig.normalMode.endTime = ''
      break
    case 'endTime':
      endDate.setHours(selectedValue[0], selectedValue[1])
      maxTime.value = endDate
      startDate.setHours(0, 0)
      minTime.value = startDate
      break
    default:
      break
  }
  deviceDetailData.value.controlConfig[`${chooseTimeParentOption.value}`][
    `${chooseTimeOption.value}`
  ] = `${selectedValue[0]}:${selectedValue[1]}`
  chooseTime.value = ''
  showTimePopup.value = false
}

/**
 * 处理分钟数
 * @param minutes
 */
function convertMinutes(minutes) {
  const minutesInAYear = 525600 // 1 year = 365 days = 525600 minutes
  const minutesInADay = 1440 // 1 day = 1440 minutes
  const minutesInAnHour = 60 // 1 hour = 60 minutes
  let result = ''
  const years = Math.floor(minutes / minutesInAYear)
  minutes %= minutesInAYear
  if (years) {
    result += `${years}年`
  }
  const days = Math.floor(minutes / minutesInADay)
  minutes %= minutesInADay
  if (days) {
    result += `${days}天`
  }
  const hours = Math.floor(minutes / minutesInAnHour)
  minutes %= minutesInAnHour
  if (hours) {
    result += `${hours}小时`
  }
  if (minutes) {
    result += ` ${minutes}分钟`
  }

  return result
}

// 获取设备基础信息
async function getDetailBase() {
  const res = await BaseInfoAPI.getBaseInfoId(deviceInfo.value.id)
  baseData.value = Object.assign({}, res)
  tabList.value = []
  // 如果设备有定位，标绘该设备
  if (res.coordinate) {
    loadBigeMap()
  }

  if (res) {
    if (res?.internetEnable) {
      tabList.value.push(
        {
          title: '设备信息',
          paneKey: 0,
          count: '',
        },
        {
          title: '告警设置',
          paneKey: 3,
          count: '',
        }
      )
      getDeviceInfo()
      // getDeviceAlarmSetting()
      getDeviceAlarmItems()
    }
    if (res?.customerId) {
      tabList.value.push({
        title: '客户信息',
        paneKey: 1,
        count: '',
      })
      getDeviceCustomer()
    }
    if (res?.installed) {
      tabList.value.push({
        title: '售后信息',
        paneKey: 2,
        count: '',
      })
      getDeviceAfter()
    }
    tabList.value.sort((a, b) => a.paneKey - b.paneKey)
    status.value = tabList.value[0]?.paneKey ?? 0
  }
}

/**
 * 跳转高德地图
 * */
function openGMap() {
  const { longitude: lonEnd, latitude: latEnd } = baseData.value.coordinate
  const destination = baseData.value.address
  window.location.href = `https://uri.amap.com/marker?position=${lonEnd},${latEnd}&name=${destination}&src=mypage&coordinate=wgs84&callnative=1`
}

function drawMarker(item) {
  // 截取name最后三位
  const num = item.serialNum.slice(-3)

  let className = classMap[item.type]
  let bottomIcon = deviceBottomIconMap[item.type]
  if (item.isAlarm) {
    bottomIcon = iconBottomAlarm
    className += ' alarm'
  } else if (item.status === 0) {
    bottomIcon = iconBottomOffline
    className += ' offline'
  }

  const deviceIcon = BM.divIcon({
    html: `
        <header class="header flex items-center w-58Px h-28Px rounded-6Px">
          <img src="${deviceIconMap2[item.type]}" class="w-16Px h-18Px object-fit ml-6Px mr-4Px"/>
          <span text="14Px white">${num}</span>
        </header>
        <footer class="flex justify-center mt-2Px">
          <div style="background: url(${bottomIcon}) center / 100% 100% no-repeat" class="footerImage w-16Px h-8Px"/>
        </footer>
        `,
    iconSize: [58, 38],
    iconAnchor: [29, 38],
    className: `deviceIcon ${className}`,
  })
  const { latitude, longitude } = item.coordinate
  const wgs84Coord = gcoord.transform([longitude, latitude], gcoord.WGS84, gcoord.BD09)
  const marker = BM.marker([wgs84Coord[1], wgs84Coord[0]], { icon: deviceIcon })

  map.flyTo([wgs84Coord[1], wgs84Coord[0]], 16)

  marker.addTo(map)

  // 为每个marker添加点击事件
  // marker.on('click', (e: any) => {
  //   e.originalEvent.stopPropagation() // 阻止事件冒泡
  //
  //   markerClick(marker)
  // })
}

// type  设备类型 1 智慧杀虫灯 2 墒情气象一体机 3 虫情分析仪 4 苗情监控
function getDeviceInfo() {
  BaseInfoAPI.getDeviceInfo(deviceInfo.value.id, 3).then((res) => {
    deviceDetailData.value = Object.assign({}, res)
    if (res?.controlConfig?.sleepMode?.enabled === null || undefined) {
      deviceDetailData.value.controlConfig.sleepMode.enabled = false
    }
    console.log(deviceDetailData.value, '设备信息')
  })
}

// .............................告警设置 start..........................

/**
 * 删除告警项行
 */
const alarmIndex = ref()

// 删除告警设置
function deleteAlarm(item) {
  alarmIndex.value = item.id
  visibleShowAlarm.value = true
}

const visibleShowAlarm = ref(false)

const onCancelAlarm = () => {
  visibleShowAlarm.value = false
}

const onOkAlarm = () => {
  // alarmItemsData.value.splice(alarmIndex.value, 1)
  const data = {
    deviceId: deviceInfo.value.id,
    ids: [alarmIndex.value],
  }
  BaseInfoAPI.deleteConfigItem(data).then((res) => {
    Taro.showToast({
      title: '删除成功',
      icon: 'success',
    })
    getDeviceAlarmItems()
  })
}

/**
 * 获取当前设备配置
 */
function getDeviceAlarmItems() {
  BaseInfoAPI.getDeviceAlarmItems(deviceInfo.value.id).then((res) => {
    alarmItemsData.value = res || []
    alarmItemsData.value.forEach((val) => {
      val.options = val.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      valueMap.value[val.itemId]?.forEach((item) => {
        if (val.value == item.value) {
          val.valueName = item.text
        }
      })
    })
  })
}
// 新增告警
function addAlarm(row?) {
  const data = {
    innerModel: baseData.value.innerModel,
    deviceType: 3,
    deviceId: deviceInfo.value.id,
    ...row,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/component/addAlarm?data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

// .............................告警设置 end............................

/**
 * 获取客户信息
 */
function getDeviceCustomer() {
  BaseInfoAPI.getCustomerInfo(deviceInfo.value.id).then((res) => {
    customerData.value = res || {}
  })
}

/**
 * 获取售后信息
 */
function getDeviceAfter() {
  BaseInfoAPI.getAfterSaleInfo(deviceInfo.value.id).then((res) => {
    afterSalesData.value = res || {}
  })
}

const goBack = () => {
  Taro.navigateBack()
}

const formData = ref({
  activeBy: '',
  imei: '',
  serialNum: '',
})

const formRef = ref()
// 失去焦点校验
const customBlurValidate = (prop) => {
  formRef.value?.validate(prop).then(({ valid, errors }) => {
    if (valid) {
      console.log('success:', formData.value)
    } else {
      console.warn('error:', errors)
    }
  })
}

const showScanner = ref(false)
const toActivation = (result: string) => {
  formData.value.imei = result
  showScanner.value = false
}

const submitLoading = ref(false)
const submit = async () => {
  submitLoading.value = true
  const { activeBy, imei } = formData.value
  const params = {
    activeBy,
    imei,
    serialNum: deviceInfo.value.serialNum,
  }
  try {
    await DeviceActivationAPI.activate(params)
    Taro.showToast({
      title: '激活成功',
      icon: 'success',
    })
    goBack()
  } finally {
    submitLoading.value = false
  }
}

// 定义计算属性，将 usedSpace 转换为 GB
const usedSpaceInGB = computed(() => {
  const usedSpace = deviceDetailData.value?.usedSpace || 0
  return (usedSpace / 1024 / 1024 / 1024).toFixed(0) // 保留两位小数
})
// 定义计算属性，将 totalSpace 转换为 GB
const totalSpaceInGB = computed(() => {
  // totalSpaceInGB
  const totalSpace = deviceDetailData.value?.totalSpace || 0
  return (totalSpace / 1024 / 1024 / 1024).toFixed(0) // 保留两位小数
})
</script>

<style scoped lang="scss">
.batch-item {
  width: 690px;
  padding: 30px 30px;
  box-sizing: border-box;
  margin: 30px auto 0;
  border-radius: 20px;
  background: #fff;
  display: flex;
  .batch-item-left {
    margin-right: 20px;
    img {
      width: 60px;
      height: 60px;
    }
  }
  .batch-item-right {
    flex: 1;
    font-size: 28px;
    color: #909090;
    .name {
      font-size: 32px;
      color: #000;
      font-weight: bold;
    }
    .batch {
      margin: 5px 0;
    }
    .count {
      display: flex;
      justify-content: space-between;
    }
  }
}
.label-text {
  font-size: 30px;
  padding-left: 30px;
}
.code {
  margin-top: 50px;
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item) {
  .nut-input-inner {
    height: 78px;
    padding: 0 30px;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 630px;
  margin: 0 auto 0;
  border-radius: 20px;
}
.bottom-button {
  position: fixed;
  left: 30px;
  bottom: 60px;
  display: flex;
  justify-content: space-between;
  width: 690px;
  margin: 0 auto;
  .nut-button {
    width: 330px;
  }
}

//
.monitor-title {
  font-weight: bold;
  font-size: 32px;
  color: #101010;
}
.monitor-line {
  width: 100%;
  // height: 0px;
  border-bottom: 1px solid #d4d4d4;
  margin: 30px 0;
}

// 文字左右展示
.monitor-box-bg {
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 20px auto;
  border-radius: 20px;
  background: #fff;
  .flex-between {
    display: flex;
    justify-content: space-between;
  }

  .text-item {
    display: flex;
    font-weight: 400;
    font-size: 28px;
    color: #5b5b5b;
    margin: 20px 0;
    .text-item-right {
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      width: 450px;
      text-align: right;
      word-break: break-all;
      overflow-wrap: break-word; /* 允许长单词或 URL 地址换行到下一行 */
      // display: flex;
      // justify-content: flex-end;
      // flex-wrap: wrap;
    }
    .img {
      width: 82px;
      height: 82px;
      border-radius: 10px 10px 10px 10px;
      margin-left: 10px;
      margin-bottom: 10px;
    }
    .text-item-left {
      width: 140px;
    }
    .text-item-center {
      width: 380px;
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      word-break: break-all;
      overflow-wrap: break-word;
    }
  }
  .statusGd {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0px;
    top: 40px;
  }
}

::v-deep .nut-cell-group .nut-cell::after {
  border: none;
}

.datePd {
  padding: 12px 32px;
}

:deep(.nut-input-inner) {
  font-size: 20px !important;
  color: #101010 !important;
}
// 拍照间隔样式
:deep(.nut-form-item__body .nut-input-box .input-text) {
  font-size: 28px !important;
  color: #101010 !important;
}
</style>
