import request from '../../utils/request'

export default class BaseInfoAPI {
  // 根据id获取设备基础信息
  static getBaseInfoId(deviceId: any) {
    return request({
      url: `/api/v1/manage/h5/device/${deviceId}/base`,
      method: 'get',
    })
  }
  // 获取设备信息
  static getDeviceInfo(deviceId: any, deviceType: any) {
    return request({
      url: `/api/v1/manage/h5/device/${deviceId}/detail?deviceType=${deviceType}`,
      method: 'get',
    })
  }
  // 获取客户信息
  static getCustomerInfo(deviceId: any) {
    return request({
      url: `/api/v1/manage/h5/device/${deviceId}/customer`,
      method: 'get',
    })
  }
  // 获取售后信息
  static getAfterSaleInfo(deviceId: any) {
    return request({
      url: `/api/v1/manage/h5/device/${deviceId}/after-sales`,
      method: 'get',
    })
  }

  // 获取各设备列表
  static deviceList(params: any) {
    return request<any>({
      url: '/api/v1/manage/h5/device/page',
      method: 'get',
      params,
    })
  }

  // 获取设备当前配置项
  static getDeviceAlarmItems(deviceId: any) {
    return request({
      url: `/api/v1/manage/h5/device/${deviceId}/alarm-setting`,
      method: 'get',
    })
  }

  // 根据设备类型获取可配置告警项
  static getDeviceAlarmItemSetting(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/model/${data.modelId}/alarm-item`,
      method: 'get',
      ...(data.deviceId ? { params: { deviceId: data.deviceId } } : {}),
    })
  }
  // 批量设置告警项配置
  static batchSetAlarmItem(params: Object) {
    return request({
      url: `/api/v1/manage/h5/device/batch-alarm-setting`,
      method: 'post',
      data: params,
    })
  }

  /** 添加设备告警项 */
  static setConfigItem(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/${data.deviceId}/alarm-setting`,
      method: 'post',
      data: data.data,
    })
  }

  /** 编辑设备告警项 */
  static updateConfigItem(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/${data.deviceId}/alarm-setting`,
      method: 'put',
      data: data.data,
    })
  }
  /** 删除设备告警项 */
  static deleteConfigItem(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/${data.deviceId}/alarm-setting?ids=${data.ids}`,
      method: 'delete',
    })
  }

  // 修改设备控制
  static updateDeviceControl(data: Object) {
    return request({
      url: '/api/v1/manage/h5/device/isa/control',
      method: 'put',
      data,
    })
  }

  // ...............BaseInfoAPI...BaseInfoAPI...BaseInfoAPI...BaseInfoAPI.........BaseInfoAPI..
  // 根据设备编码获取设备基础信息
  static getDeviceIdFrom(serialNum: any) {
    return request({
      url: `/api/v1/manage/h5/device/base?serialNum=${serialNum}`,
      method: 'get',
    })
  }

  // ................................... 新增筛选 区域，客户筛选下拉.....................
  static areaOptions() {
    return request({
      url: `/api/v1/manage/h5/area/listOptions`,
      method: 'get',
    })
  }
  static customerOptions() {
    return request({
      url: `/api/v1/manage/h5/customer/listCustomer`,
      method: 'get',
    })
  }
}
