:root,
page {
  --nut-navbar-background: transparent;
  --nut-navbar-title-font-weight: 600;
  --nut-navbar-title-font-color: #000;
  --nut-navbar-title-font: 36px;
  --nut-primary-color: #01a15b;
  --nut-searchbar-input-background: #f5f6fa;
}

.page-content {
  background: #f5f6fa;
  height: 100vh;
  overflow-y: scroll;
}

.limit-height {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow-y: scroll;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

// image,
// img {
//   pointer-events: none; // 禁止长按复制(保存)
// }

.taro-modal__content {
  background: linear-gradient(180deg, #ddf9eb 0%, #fff 50%, rgb(255, 255, 255) 100%) !important;
  border-radius: 20px 20px 20px 20px !important;
}
.taro-modal__text {
  margin-top: 60px !important;
  margin-bottom: 10px !important;
  color: #101010 !important;
}
.taro-modal__title {
  font-weight: bold !important;
}
.taro-model__confirm {
  // background: linear-gradient( 270deg, #06BB6C 0%, #019E59 100%)!important;
  color: #019e59 !important;
}
.nut-button--success {
  background: linear-gradient(270deg, #06bb6c 0%, #019e59 100%) !important;
}

// 按钮红色56
.deleteRed {
  font-size: 28px;
  color: #ff4d4f;
  height: 56px;
  background: #fff1f0;
  border-radius: 8px;
  border: 1px solid #ff4d4f;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32px;
}
// 按钮绿色56
.completeGreen {
  font-size: 28px;
  color: #019e59;
  height: 56px;
  background: #ecfcf1;
  border-radius: 8px;
  border: 1px solid #019e59;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32px;
}
// 按钮绿色46
.completeGreenDetail {
  font-size: 28px;
  color: #019e59;
  // width: 140px;
  height: 46px;
  background: #ecfcf1;
  border-radius: 8px;
  border: 1px solid #019e59;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}
.left-15 {
  margin-left: 15px;
}

.bigemap-left,
.bigemap-right {
  display: none;
}

.tab-top-border {
  border-radius: 20px 20px 0px 0px;
  overflow: hidden;
}

.shaixuanIcon {
  width: 40px;
  height: 37px;
}
