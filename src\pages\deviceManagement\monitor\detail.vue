<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />

  <div v-else class="page-content" select-none>
    <CustomNavTitle title="苗情监控" :showBack="true" background="#fff" />

    <div v-if="baseData.coordinate?.latitude" ref="mapContainer" class="h-30" />
    <div v-if="tabList && tabList.length > 0" class="content">
      <div v-if="baseData.internetEnable" class="monitor-box-bg">
        <header class="font-bold pb-3 mb-3" b="b-0.1 b-solid #E7E9EE" text="3.2 #101010">
          {{ baseData.serialNum || '--' }}
        </header>
        <div style="position: relative" class="w-63 h-35 rounded-0.8" b="0.1 solid #E7E9EE">
          <Player
            class="LivePlayerDiv w-full h-full"
            :video-url="videoObjUrl"
            :loading="monitorLoading"
          />
        </div>
        <div class="page-title">云台控制</div>
        <div class="frameLine">
          <div class="yuntaiBox pointer-events-none">
            <div
              class="yuntaiTop pointer-events-auto"
              @mousedown="ptzCamera('up')"
              @touchend="ptzCamera('stop')"
            >
              <image :src="topIcon" mode="scaleToFill" class="absolute inset-0 z-0" />
            </div>
            <div
              class="yuntaiLeft pointer-events-auto"
              @touchstart="ptzCamera('left')"
              @touchend="ptzCamera('stop')"
            >
              <image
                :src="leftIcon"
                mode="scaleToFill"
                class="absolute inset-0 pointer-events-none"
              />
            </div>
            <div
              class="yuntaiRight pointer-events-auto"
              @touchstart="ptzCamera('right')"
              @touchend="ptzCamera('stop')"
            >
              <image
                :src="rightIcon"
                mode="scaleToFill"
                class="absolute pointer-events-none inset-0"
              />
            </div>
            <div
              class="yuntaiBottom pointer-events-auto"
              @touchstart="ptzCamera('down')"
              @touchend="ptzCamera('stop')"
            >
              <image
                :src="bottomIcon"
                mode="scaleToFill"
                class="absolute inset-0 pointer-events-none"
              />
            </div>
          </div>
          <!--          播放按钮-->
          <div
            class="absolute top-1/2 left-1/2 -mt-1 -translate-1/2 w-10 h-10 rounded-1/2 grid place-items-center pointer-events-auto"
            @click="broadcastStatusClick"
          >
            <image mode="scaleToFill" src="@/assets/device/record.png" class="w-3 h-4" />
          </div>
          <div class="absolute top-6 right-6 flex flex-col gap-y-1">
            <div
              class="pointer-events-auto"
              @touchstart="ptzCamera('zoomin')"
              @touchend="ptzCamera('stop')"
            >
              <image :src="iconZoomIn" class="pointer-events-none" />
            </div>
            <div
              class="pointer-events-auto"
              @touchstart="ptzCamera('zoomout')"
              @touchend="ptzCamera('stop')"
            >
              <image :src="iconZoomOut" class="pointer-events-none" />
            </div>
          </div>

          <!--          <p-->
          <!--            v-if="broadcastStatus === 1"-->
          <!--            class="absolute left-0 right-0 bottom-2 text-center"-->
          <!--            text="2.6"-->
          <!--          >-->
          <!--            <span v-if="broadcastStatus === -2">正在释放资源</span>-->
          <!--            <span v-if="broadcastStatus === -1">点击开始对讲</span>-->
          <!--            <span v-if="broadcastStatus === 0">等待接通中...</span>-->
          <!--            <span v-if="broadcastStatus === 1">请说话</span>-->
          <!--          </p>-->
        </div>
      </div>

      <div v-if="baseData.internetEnable" class="monitor-box-bg">
        <header class="font-bold pb-3 mb-3" b="b-0.1 b-solid #E7E9EE" text="3.2 #101010">
          设备信息
        </header>
        <div class="text-item flex-between">
          <div>设备编号</div>
          <div class="text-item-right">{{ baseData.serialNum || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>客户信息</div>
          <div class="text-item-right">{{ baseData.customerName || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>通道号</div>
          <div class="text-item-right">{{ baseData.misc.channelId || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>通道名称</div>
          <div class="text-item-right">{{ baseData.misc.channelName || '--' }}</div>
        </div>
      </div>
    </div>

    <div class="monitor-box-bg">
      <header class="font-bold pb-3 mb-3" b="b-0.1 b-solid #E7E9EE" text="3.2 #101010">
        编码信息
      </header>

      <div class="text-item flex-between">
        <div>格式</div>
        <div class="text-item-right">{{ idStreamInfo?.videoCodec || '--' }}</div>
      </div>
      <div class="text-item flex-between">
        <div>类型</div>
        <div class="text-item-right">视频</div>
      </div>
      <div class="text-item flex-between">
        <div>分辨率</div>
        <div class="text-item-right">{{ idStreamInfo?.width }}X{{ idStreamInfo?.height }}</div>
      </div>

      <div v-if="false" class="mb-20">
        <div
          class="monitor-box-bg"
          style="
            margin-bottom: 0 !important;
            border-radius: 0 !important;
            padding-bottom: 10px !important;
          "
        >
          <div class="flex flex-col">
            <div class="flex justify-between flex-row">
              <div class="text-2.7 text-#101010 font-600">告警设置</div>
              <img :src="addIcon" alt="" class="h-5 w-5" @click="alarmItemAdd" />
            </div>
          </div>
          <!-- <div v-show="alarmItemsData.length > 0" class="w-100% bg-#707070 h-0.25 mt-2.5"></div> -->
          <div
            v-show="alarmItemsData.length > 0"
            class="monitor-line"
            style="margin-bottom: 0 !important"
          ></div>
        </div>

        <div
          v-for="(item, index) in alarmItemsData"
          :key="index"
          class="flex flex-col mb-10 monitor-box-bg"
          style="margin-top: 0 !important; border-radius: 0 !important; padding-top: 0 !important"
        >
          <nut-form
            :ref="
              (el) => {
                if (el) formRefs[index] = el
              }
            "
            :model-value="item"
          >
            <nut-form-item
              prop="itemId"
              required
              :style="{ padding: '0' }"
              :rules="[{ required: true, message: '请选择告警项' }]"
            >
              <div class="flex justify-between flex-col">
                <div class="text-2.7 text-#707070">告警项</div>
                <div
                  class="flex flex-row items-center h-5 mt-2.5"
                  p="x-2 y-1"
                  :style="{
                    borderWidth: '1px',
                    borderRadius: '4px',
                    borderStyle: 'solid',
                    borderColor: '#D4D4D4',
                  }"
                  @click="handlerPopup(alarmSettingsData, 'itemId', item.itemId, index)"
                >
                  <div :style="{ color: item.itemId ? '#101010' : '' }">
                    {{ item.itemId ? item.itemName : '请选择告警项' }}
                  </div>
                  <IconFont :style="{ marginLeft: 'auto' }" size="12" name="arrow-down"></IconFont>
                </div>
              </div>
            </nut-form-item>

            <nut-form-item
              prop="value"
              required
              :style="{ padding: '0' }"
              :rules="[{ required: true, message: '请输入告警阈值' }]"
            >
              <div class="flex flex-col mt-2.5">
                <div class="mt-2.5 text-2.7 text-#707070">告警阈值</div>
                <!-- {{  item }} -->
                <div v-if="!valueMap[`${item.itemId}`]" class="mt-2.5">
                  <nut-input v-model="item.value" clearable>
                    <template #right>
                      <div>{{ item.unit }}</div>
                    </template>
                  </nut-input>
                </div>
                <div
                  v-else
                  class="flex flex-row items-center h-5 mt-2.5"
                  p="x-2 y-1"
                  :style="{
                    borderWidth: '1px',
                    borderRadius: '4px',
                    borderStyle: 'solid',
                    borderColor: '#D4D4D4',
                  }"
                  @click="handlerPopup(valueMap[`${item.itemId}`], 'value', item.value, index)"
                >
                  <div :style="{ color: item.value ? '#101010' : '' }">
                    {{ item.value ? item.valueName : '请选择阈值' }}
                  </div>
                  <IconFont :style="{ marginLeft: 'auto' }" size="12" name="arrow-down"></IconFont>
                </div>
              </div>
            </nut-form-item>

            <nut-form-item
              prop="operator"
              required
              :style="{ padding: '0' }"
              :rules="[{ required: true, message: '请选择条件' }]"
            >
              <div class="mt-2.5 text-2.7 text-#707070">条件</div>
              <div
                class="flex flex-row items-center h-5 mt-2.5"
                p="x-2 y-1"
                :style="{
                  borderWidth: '1px',
                  borderRadius: '4px',
                  borderStyle: 'solid',
                  borderColor: '#D4D4D4',
                }"
                @click="
                  item.itemId ? handlerPopup(item.options, 'operator', item.operator, index) : ''
                "
              >
                <div :style="{ color: item.operator ? '#101010' : '' }">
                  {{ item.operator ? String(options[item.operator - 1]) : '请选择条件' }}
                </div>
                <IconFont :style="{ marginLeft: 'auto' }" size="12" name="arrow-down"></IconFont>
              </div>
            </nut-form-item>

            <nut-form-item
              prop="pushChannel"
              required
              :style="{ padding: '0' }"
              :rules="[
                { required: true, message: '请选择至少一种推送通道' },
                { validator: validateList, message: '请选择至少一种推送通道' },
              ]"
            >
              <div class="flex flex-row justify-between items-center mt-3.5">
                <div class="text-2.7 text-#707070">推送通道</div>
                <div class="flex flex-row">
                  <nut-checkbox-group v-model="item.pushChannel">
                    <nut-checkbox :label="1" icon-size="15"> 平台 </nut-checkbox>
                    <nut-checkbox :label="2" icon-size="15"> 客户 </nut-checkbox>
                  </nut-checkbox-group>
                </div>
              </div>
            </nut-form-item>

            <nut-form-item
              prop="pushMethod"
              required
              :style="{ padding: '0' }"
              :rules="[
                { required: true, message: '请选择至少一种推送方式' },
                { validator: validateList, message: '请选择至少一种推送方式' },
              ]"
            >
              <div class="flex flex-row justify-between items-center mt-2.5">
                <div class="text-2.7 text-#707070">推送方式</div>
                <div class="flex flex-row">
                  <nut-checkbox-group v-model="item.pushMethod">
                    <nut-checkbox :label="1" icon-size="15"> 后台 </nut-checkbox>
                    <nut-checkbox :label="2" icon-size="15"> 短信 </nut-checkbox>
                  </nut-checkbox-group>
                </div>
              </div>
            </nut-form-item>
          </nut-form>
          <!-- <div class="w-100% bg-#707070 h-0.25 mt-2.5 mb-2.5"></div> -->
          <div class="monitor-line" style="margin-top: 5px !important"></div>
          <div class="flex">
            <nut-button
              color="#FE4242"
              :style="{
                background: 'rgba(254, 66, 66, 0.1)',
                borderRadius: '4px',
                marginLeft: 'auto',
              }"
              size="small"
              shape="square"
              plain
              @click="alarmItemdelete(index)"
            >
              删除
            </nut-button>
          </div>
        </div>
      </div>
    </div>

    <nut-dialog
      title="确定删除该告警？"
      v-model:visible="visibleShowAlarm"
      @cancel="onCancelAlarm"
      @ok="onOkAlarm"
    />

    <nut-popup v-model:visible="showPopup" position="bottom">
      <nut-picker
        v-model="choose"
        :columns="optionList"
        :title="option === 'itemId' ? '告警项' : option === 'value' ? '阈值' : '条件'"
        @confirm="chooseOption"
        @cancel=";(showPopup = false), (choose = []), (optionList = [])"
      />
    </nut-popup>
    <div v-if="status === 3" class="position-absolute w-100% bottom-0 flex justify-center">
      <div class="mt-2.5 position-relative w-80vw mb-4">
        <nut-button type="primary" size="large" @click="submitAlarmSetting"> 提交 </nut-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Taro from '@tarojs/taro'
import AuthAPI from '../../../api/auth'
import DeviceActivationAPI from '../../../api/deviceActivation'
import BaseInfoAPI from '../../../api/device/baseInfo'
import MonitorAPI from '../../../api/device/monitor'
import Scanner from '../../../components/Scanner/Scanner.vue'
import { IconFont } from '@nutui/icons-vue-taro'
import addIcon from '../../../assets/icon_zengjia.png'
import topIcon from '@/assets/device/<EMAIL>'
import leftIcon from '@/assets/device/<EMAIL>'
import rightIcon from '@/assets/device/<EMAIL>'
import bottomIcon from '@/assets/device/<EMAIL>'
import { ZLMRTCClient } from '../../../../public/js/ZLMRTCClient'
import CryptoJS from 'crypto-js'
import Player from '@/components/Player/Player.vue'
import iconZoomIn from '@/assets/device/zoom-in.svg'
import iconZoomOut from '@/assets/device/zoom-out.svg'

import iconBottomAlarm from '@/assets/mapIcons/bottom/alarm.webp'
import iconBottomOffline from '@/assets/mapIcons/bottom/offline.webp'
import loadBigemap from '@/utils/bigemap'
import { classMap, DEFAULT_CENTER, deviceBottomIconMap, deviceIconMap2 } from '@/config/map'

import { markerAlarmMap, markerMap } from '@/config/map'

import gcoord from 'gcoord'

const mapContainer = ref<HTMLElement>()

let map: any
let BM: any

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = async () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [DEFAULT_CENTER.latitude, DEFAULT_CENTER.longitude],
    zoom: 16,
    minZoom: 5,
    zoomControl: false,
    maxZoom: 18,
  })

  drawMarker(baseData.value)
}

const deviceInfo = ref<any>({})

// 获取页面参数
const params: any = Taro.getCurrentInstance().router?.params

deviceInfo.value = JSON.parse(decodeURIComponent(params.data))

const status = ref(0)
const tabList = ref([
  {
    title: '设备信息',
    paneKey: 0,
    count: '',
  },
  {
    title: '客户信息',
    paneKey: 1,
    count: '',
  },
  {
    title: '售后信息',
    paneKey: 2,
    count: '',
  },
  // {
  //   title: '告警设置',
  //   paneKey: 3,
  //   count: '',
  // },
])

const detailInfo = ref<any>({})

const baseData = ref({
  version: '',
  iccd: '',
  coordinate: {},
}) // 基础信息

const afterSalesData = ref() // 售后信息
// const alarmSettingslData = ref() // 告警设置信息
const alarmItemsData = ref() // 告警设置信息
const alarmSettingsData = ref([]) // 设备可配置项
const showPopup = ref(false) // 条件下拉
const options = ref(['等于', '不等于', '大于', '小于', '大于等于', '小于等于', '之间']) // 条件列表
const choose = ref([]) // 通用Picker选项
const optionList = ref([]) // 通用选项列表
const alarmItemIndex = ref() // 打开下拉的item项索引
const option = ref('') // 通用选项item
// const formRefs = reactive<Record<number, any>>({}) // ref
const formRefs = ref<Array<InstanceType<any>>>(new Array()) // ref
const valueMap = ref<Record<number, any>>({}) // 告警项 valueList

// 获取设备基础信息
// 定义一个异步函数 getDetailBase，用于获取设备的基础信息
// eslint-disable-next-line complexity
const getDetailBase = async () => {
  const res = await BaseInfoAPI.getBaseInfoId(deviceInfo.value.id)
  baseData.value = Object.assign({}, res)
  deviceInfo.value.channelId = res?.misc?.channelId
  deviceInfo.value.deviceId = res?.misc?.deviceId

  // 如果设备有定位，标绘该设备
  if (res.coordinate) {
    loadBigeMap()
  }
  tabList.value = []
  if (res) {
    if (res?.internetEnable) {
      tabList.value.push({
        title: '设备信息',
        paneKey: 0,
        count: '',
      })
    }
    if (res?.customerId) {
      tabList.value.push({
        title: '客户信息',
        paneKey: 1,
        count: '',
      })
    }
    if (res?.installed) {
      tabList.value.push({
        title: '售后信息',
        paneKey: 2,
        count: '',
      })
    }
    // if (res?.internetEnable) {
    //   tabList.value.push({
    //     title: '告警设置',
    //     paneKey: 3,
    //     count: '',
    //   });
    // }

    if (res?.internetEnable) {
      startPlay()
      // getDeviceAlarmSetting()
    }
    if (res?.installed) {
      getDeviceAfter()
    }
  }

  status.value = tabList.value && tabList.value.length > 0 ? tabList.value[0].paneKey : 0
  // tableIndex.value = tabList.value && tabList.value.length > 0 ? tabList.value[0].id : 0
  // console.log(tabList.value)
}
getDetailBase()

/**
 * 跳转高德地图
 * */
function openGMap() {
  const { longitude: lonEnd, latitude: latEnd } = baseData.value.coordinate
  const destination = baseData.value.address
  window.location.href = `https://uri.amap.com/marker?position=${lonEnd},${latEnd}&name=${destination}&src=mypage&coordinate=wgs84&callnative=1`
}

function drawMarker(item) {
  // 截取name最后三位
  const num = item.serialNum.slice(-3)

  let className = classMap[item.type]
  let bottomIcon = deviceBottomIconMap[item.type]
  if (item.isAlarm) {
    bottomIcon = iconBottomAlarm
    className += ' alarm'
  } else if (item.status === 0) {
    bottomIcon = iconBottomOffline
    className += ' offline'
  }

  const deviceIcon = BM.divIcon({
    html: `
        <header class="header flex items-center w-58Px h-28Px rounded-6Px">
          <img src="${deviceIconMap2[item.type]}" class="w-16Px h-18Px object-fit ml-6Px mr-4Px"/>
          <span text="14Px white">${num}</span>
        </header>
        <footer class="flex justify-center mt-2Px">
          <div style="background: url(${bottomIcon}) center / 100% 100% no-repeat" class="footerImage w-16Px h-8Px"/>
        </footer>
        `,
    iconSize: [58, 38],
    iconAnchor: [29, 38],
    className: `deviceIcon ${className}`,
  })
  const { latitude, longitude } = item.coordinate
  const wgs84Coord = gcoord.transform([longitude, latitude], gcoord.WGS84, gcoord.BD09)
  const marker = BM.marker([wgs84Coord[1], wgs84Coord[0]], { icon: deviceIcon })

  map.flyTo([wgs84Coord[1], wgs84Coord[0]], 16)

  marker.addTo(map)

  // 为每个marker添加点击事件
  // marker.on('click', (e: any) => {
  //   e.originalEvent.stopPropagation() // 阻止事件冒泡
  //
  //   markerClick(marker)
  // })
}

const monitorLoading = ref(false)
// 开始播放
// const videoObj = ref()
const videoObjUrl = ref()
function startPlay() {
  monitorLoading.value = true
  MonitorAPI.gbPlaybackStart(deviceInfo.value.deviceId, deviceInfo.value.channelId)
    .then((res) => {
      videoObjUrl.value = res.https_flv
      getDeviceInfo(res)
    })
    .catch((err) => {
      console.log(err, 'errrrrrrrrrr')
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

// 获取编码信息
const idStreamInfo = ref()
function getDeviceInfo(row) {
  const data = {
    app: row.app, // 应用名
    stream: row.stream, // 流ID
    mediaServerId: row.mediaServerId, // 流媒体ID
  }
  MonitorAPI.gbStreamInfo(data).then((res) => {
    idStreamInfo.value = res
  })
}

function getDeviceAfter() {
  BaseInfoAPI.getAfterSaleInfo(deviceInfo.value.id).then((res) => {
    afterSalesData.value = res || {}
  })
}

// ................. 云台控制  start......................
const controSpeed = ref(30)
function ptzCamera(command) {
  console.log(`云台控制：${command}`)
  const horizonSpeed = Number.parseInt((controSpeed.value * 255) / 100)
  const verticalSpeed = Number.parseInt((controSpeed.value * 255) / 100)
  const zoomSpeed = Number.parseInt((controSpeed.value * 16) / 100)
  const data = {
    command,
    horizonSpeed,
    verticalSpeed,
    zoomSpeed,
  }
  MonitorAPI.gbPtzControl(deviceInfo.value.deviceId, deviceInfo.value.channelId, data).then(
    (res) => {
      console.log(`%c云台控制：${command}成功-----------------------------`, 'color: green')
    }
  )
}

// ................. 云台控制  end......................

// ........................添加语音  start......................
const broadcastRtc = ref()
const broadcastStatus = ref(-1) // -2 正在释放资源 -1 默认状态 0 等待接通 1 接通成功
function getBroadcastStatus() {
  if (broadcastStatus.value == -2) {
    return 'primary'
  }

  if (broadcastStatus.value == -1) {
    return 'primary'
  }

  if (broadcastStatus.value == 0) {
    return 'warning'
  }

  if (broadcastStatus.value == 1) {
    return 'danger'
  }
}

function broadcastStatusClick() {
  console.log('broadcastStatusClick')
  // if (broadcastStatus.value === -1) {
  //   return
  // }
  if (broadcastStatus.value == -1) {
    // 默认状态， 开始
    broadcastStatus.value = 0
    const data = {
      deviceId: deviceInfo.value.deviceId,
      channelId: deviceInfo.value.channelId,
      timeout: 30,
      broadcastMode: false, // true 喊话 false 对讲
    }
    MonitorAPI.gbBroadcast(data).then((res) => {
      console.log(res)
      if (res) {
        const streamInfo = res.streamInfo
        if (document.location.protocol.includes('https')) {
          startBroadcast(streamInfo.rtcs)
        } else {
          startBroadcast(streamInfo.rtc)
        }
      }
    })
  } else if (broadcastStatus.value === 1) {
    broadcastStatus.value = -1
    broadcastRtc.value.close()
  }
}

function startBroadcast(url) {
  console.log('startBroadcast', url, ZLMRTCClient, crypto)
  //  获取推流鉴权Key
  MonitorAPI.gbUserInfo()
    .then((res) => {
      console.log(res)
      const pushKey = res?.pushKey
      // 获取推流鉴权KEY
      // url += `&sign=${crypto.createHash('md5').update(pushKey, 'utf8').digest('hex')}`
      const md5Hash = CryptoJS.MD5(pushKey).toString()
      url += `&sign=${md5Hash}`
      console.log(`开始语音喊话： ${url}`)
      broadcastRtc.value = new ZLMRTCClient.Endpoint({
        debug: true, // 是否打印日志
        zlmsdpUrl: url, //流地址
        simulecast: false,
        useCamera: false,
        audioEnable: true,
        videoEnable: false,
        recvOnly: false,
      })

      broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT, (e) => {
        // 获取到了本地流
        console.error('不支持webrtc', e)
        Taro.showToast({
          title: '不支持webrtc, 无法进行语音喊话',
          icon: 'error',
        })
        broadcastStatus.value = -1
      })

      broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR, (e) => {
        // ICE 协商出错
        console.error('ICE 协商出错')
        Taro.showToast({
          title: 'ICE 协商出错',
          icon: 'error',
        })

        broadcastStatus.value = -1
      })

      broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED, (e) => {
        // offer anwser 交换失败
        console.error('offer anwser 交换失败', e)
        Taro.showToast({
          title: `offer anwser 交换失败`,
          icon: 'error',
        })
        broadcastStatus.value = -1
      })

      broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE, (e) => {
        // offer anwser 交换失败
        console.log('状态改变', e)
        if (e === 'connecting') {
          broadcastStatus.value = 0
        } else if (e === 'connected') {
          broadcastStatus.value = 1
        } else if (e === 'disconnected') {
          broadcastStatus.value = -1
        }
      })

      broadcastRtc.value.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, (e) => {
        // offer anwser 交换失败
        console.log('捕获流失败', e)
        Taro.showToast({
          title: `捕获流失败`,
          icon: 'error',
        })
        broadcastStatus.value = -1
      })
    })
    .catch(() => {
      Taro.showToast({
        title: `获取推流鉴权Key失败`,
        icon: 'error',
      })
      broadcastStatus.value = -1
    })
}

// ........................添加语音  end......................

const goBack = () => {
  if (broadcastRtc.value) {
    broadcastRtc.value.close()
  }
  broadcastRtc.value = null
  broadcastStatus.value = -1
  Taro.navigateBack()
}

const formData = ref({
  activeBy: '',
  imei: '',
  serialNum: '',
})

const getUserInfo = async () => {
  const res = await AuthAPI.me()
  formData.value.activeBy = res.nickname
}

const rules = {
  activeBy: [{ required: true, message: '请输入激活人' }],
  imei: [{ required: true, message: '请输入设备编号' }],
}

const formRef = ref()
// 失去焦点校验
const customBlurValidate = (prop) => {
  formRef.value?.validate(prop).then(({ valid, errors }) => {
    if (valid) {
      console.log('success:', formData.value)
    } else {
      console.warn('error:', errors)
    }
  })
}

const showScanner = ref(false)
const toActivation = (result: string) => {
  formData.value.imei = result
  showScanner.value = false
}

const submitLoading = ref(false)
const submit = async () => {
  submitLoading.value = true
  const { activeBy, imei } = formData.value
  const params = {
    activeBy,
    imei,
    serialNum: deviceInfo.value.serialNum,
  }
  try {
    await DeviceActivationAPI.activate(params)
    Taro.showToast({
      title: '激活成功',
      icon: 'success',
    })
    goBack()
  } finally {
    submitLoading.value = false
  }
}

// 图片预览
const handleClickImage = (url, list?) => {
  const images = list?.map((item) => item.url) || []
  Taro.previewImage({
    current: url,
    urls: images ? images : [url],
  })
}

// .............................告警设置 start..........................
/**
 * 检验数组
 */
function validateList(list) {
  if (list.length > 0) {
    return Promise.resolve()
  } else {
    return false
  }
}
/**
 * 显示选项框
 */
function handlerPopup(list, item, value, index) {
  console.log(list, item, value, index, 'showPopup')
  alarmItemIndex.value = index
  optionList.value = list
  option.value = item
  choose.value = [value]
  showPopup.value = true
}
/**
 * 选择选项
 */
function chooseOption({ selectedValue, selectedOptions }) {
  switch (option.value) {
    case 'itemId':
      alarmItemsData.value[alarmItemIndex.value].itemId = selectedValue[0]
      alarmItemsData.value[alarmItemIndex.value].itemName = selectedOptions[0].text
      alarmItemsData.value[alarmItemIndex.value].options = selectedOptions[0].options
      alarmItemsData.value[alarmItemIndex.value].operator = ''
      alarmItemsData.value[alarmItemIndex.value].value = ''
      alarmItemsData.value[alarmItemIndex.value].unit = selectedOptions[0].unit
      alarmItemsData.value[alarmItemIndex.value].pushChannel = []
      alarmItemsData.value[alarmItemIndex.value].pushMethod = []
      break
    case 'operator':
      alarmItemsData.value[alarmItemIndex.value].operator = selectedValue[0]
      break
    case 'value':
      alarmItemsData.value[alarmItemIndex.value].value = selectedValue[0]
      alarmItemsData.value[alarmItemIndex.value].valueName = selectedOptions[0].text
      break
    default:
      break
  }
  showPopup.value = false
  choose.value = []
  optionList.value = []
  option.value = ''
  alarmItemIndex.value = ''
}

/**
 * 根据类型获取设备可配置项
 */
function getDeviceAlarmSetting() {
  BaseInfoAPI.getDeviceAlarmItemSetting(baseData.value.innerModel).then((res) => {
    alarmSettingsData.value = res || []
    alarmSettingsData.value.forEach((item) => {
      item.value = item.id
      item.text = item.name
      item.options = item.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      if (item.valueList?.length > 0) {
        valueMap.value[`${item.id}`] = item.valueList.map((val) => {
          return { value: val.value, text: val.label }
        })
      }
    })
    console.log(alarmSettingsData.value, 'alarmSettingsData.value')
    getDeviceAlarmItems()
  })
}

/**
 * 提交告警设置
 */
function submitAlarmSetting() {
  let pass = 1

  validateAllForms().then((pass) => {
    if (pass) {
      const data = {
        deviceIds: [deviceInfo.value.id],
        settings: alarmItemsData.value.map((val) => {
          return {
            itemId: val.itemId,
            value: val.value,
            operator: val.operator,
            pushChannel: val.pushChannel,
            pushMethod: val.pushMethod,
          }
        }),
      }
      BaseInfoAPI.batchSetAlarmItem(data).then((res) => {
        Taro.showToast({
          title: '操作成功',
          icon: 'success',
        })
      })
    }
  })
}

/**
 * 动态表单校验
 */
async function validateAllForms() {
  let pass = true
  for (const [index, formRef] of formRefs.value.entries()) {
    try {
      const { valid } = await formRef.validate()
      if (!valid) pass = false
    } catch (error) {
      pass = false
    }
  }
  return pass
}

// /**
//  * 删除告警项行
//  */
// function alarmItemdelete(index) {
//   alarmItemsData.value.splice(index, 1)
// }
/**
 * 删除告警项行
 */
const alarmIndex = ref()
function alarmItemdelete(index) {
  // alarmItemsData.value.splice(index, 1)
  alarmIndex.value = index
  visibleShowAlarm.value = true
}

const visibleShowAlarm = ref(false)

const onCancelAlarm = () => {
  visibleShowAlarm.value = false
}

const onOkAlarm = () => {
  alarmItemsData.value.splice(alarmIndex.value, 1)
}

/**
 * 新增告警项行
 */
function alarmItemAdd() {
  alarmItemsData.value.push({
    id: '',
    value: '',
    itemId: '',
    operator: '',
    pushChannel: [],
    pushMethod: [],
  })
}

/**
 * 获取当前设备配置
 */
function getDeviceAlarmItems() {
  BaseInfoAPI.getDeviceAlarmItems(deviceInfo.value.id).then((res) => {
    alarmItemsData.value = res || {}
    alarmItemsData.value.forEach((val) => {
      val.options = val.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      valueMap.value[val.itemId]?.forEach((item) => {
        if (val.value == item.value) {
          val.valueName = item.text
        }
      })
    })
  })
}
// .............................告警设置 end.............................

// .................................长按事件 start ......................

// @touchstart.stop="handleTouchStart" @touchend.stop="handleTouchEnd"
const longPressTimer = ref<ReturnType<typeof setTimeout> | undefined>(undefined) // 定义一个计时器变量
function handleTouchStart() {
  // 清除之前的计时器，以防多次触发
  clearTimeout(longPressTimer.value)
  // 设置一个新的计时器，例如 500ms 后触发长按事件
  longPressTimer.value = setTimeout(() => {
    onLongPress()
  }, 500)
}
function handleTouchEnd() {
  // 清除计时器，表示没有长按
  clearTimeout(longPressTimer.value)
}
function onLongPress() {
  console.log('长按事件被触发')
  Taro.showToast({
    title: '长按事件被触发',
    icon: 'success',
  })
  // 在这里执行你的逻辑，比如显示菜单、弹出框等
}
// .................................长按事件 end...............................
</script>

<style scoped lang="scss">
.content {
  width: 690px;
  margin: auto;
}
.LivePlayerDiv {
  margin: 0px auto;
}
.batch-item {
  box-sizing: border-box;
  border-radius: 20px;
  background: #fff;
  display: flex;
  .batch-item-left {
    margin-right: 20px;
    img {
      width: 60px;
      height: 60px;
    }
  }
  .batch-item-right {
    flex: 1;
    font-size: 28px;
    color: #909090;
    .name {
      font-size: 32px;
      color: #000;
      font-weight: bold;
    }
    .batch {
      margin: 10px 0;
    }
    .count {
      display: flex;
      justify-content: space-between;
    }
  }
}
.label-text {
  font-size: 30px;
  padding-left: 30px;
}
.code {
  margin-top: 50px;
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item) {
  .nut-input-inner {
    height: 78px;
    padding: 0 30px;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 630px;
  margin: 0 auto 0;
  border-radius: 20px;
}
.bottom-button {
  position: fixed;
  left: 30px;
  bottom: 60px;
  display: flex;
  justify-content: space-between;
  width: 690px;
  margin: 0 auto;
  .nut-button {
    width: 330px;
  }
}

//
.monitor-title {
  font-weight: bold;
  font-size: 32px;
  color: #101010;
}
.monitor-line {
  width: 100%;
  // height: 0px;
  border-bottom: 1px solid #d4d4d4;
  margin: 30px 0;
}
// 文字左右展示
.monitor-box-bg {
  width: 690px;
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
  .flex-between {
    display: flex;
    justify-content: space-between;
  }
  .text-item {
    display: flex;
    font-weight: 400;
    font-size: 28px;
    color: #5b5b5b;
    margin: 20px 0;
    .text-item-right {
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      width: 450px;
      text-align: right;
      word-break: break-all;
      overflow-wrap: break-word; /* 允许长单词或 URL 地址换行到下一行 */
      // display: flex;
      // justify-content: flex-end;
      // flex-wrap: wrap;
    }
    .img {
      width: 82px;
      height: 82px;
      border-radius: 10px 10px 10px 10px;
      margin-left: 10px;
      margin-bottom: 10px;
    }
    .text-item-left {
      width: 140px;
    }
    .text-item-center {
      width: 380px;
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      word-break: break-all;
      overflow-wrap: break-word;
    }
  }
  .statusGd {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0px;
    top: 40px;
  }
}

.page-title {
  font-weight: bold;
  font-size: 28px;
  color: #101010;
  margin-top: 30px;
  &::before {
    content: '';
    display: inline-block;
    position: relative;
    top: 2px;
    margin-right: 15px;
    width: 6px;
    height: 28px;
    background: #019e59;
  }
}
// 外框
.frameLine {
  width: 100%;
  height: auto;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e0e0e0;
  padding: 30px;
  box-sizing: border-box;
  position: relative;
  margin-top: 30px;
}
// 云台
.yuntaiBox {
  width: 300px;
  height: 300px;
  background-image: url('../../../assets/device/<EMAIL>'); /* 替换为实际的图片路径 */
  background-size: cover; /* 背景图覆盖整个元素 */
  background-position: center; /* 背景图居中显示 */
  margin: 10px auto 30px;
  position: relative !important;

  .yuntaiTop {
    position: absolute;
    top: 60px;
    left: 50%;
    margin-left: -12px;
    width: 24px;
    height: 18px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .yuntaiLeft {
    position: absolute;
    top: 50%;
    left: 55px;
    transform: translate(0px, -50%);
    width: 20px;
    height: 18px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .yuntaiRight {
    position: absolute;
    top: 50%;
    right: 55px;
    transform: translate(0px, -50%);
    width: 20px;
    height: 18px;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .yuntaiBottom {
    position: absolute;
    bottom: 60px;
    left: 50%;
    margin-left: -12px;
    width: 24px;
    height: 18px;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.contro-speed {
  width: 360px;
  margin: auto;
}
:deep(.nut-range-button) {
  width: 26px;
  height: 26px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.16);
}

// 对讲按钮
.trank {
  width: 80%;
  // height: 180px;
  text-align: left;
  padding: 0 10%;
  overflow: auto;
  font-size: 26px;
  color: #101010;
}
.intercomImg {
  width: 200px;
  height: 200px;
}
</style>
