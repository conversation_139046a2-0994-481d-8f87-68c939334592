import request from '@/utils/request'

export default class InsectPestAPI {
  // 虫情拍照
  static snapshot(deviceId: Number) {
    return request({
      url: `/api/v1/manage/h5/device/isa/${deviceId}/photo`,
      method: 'post',
    })
  }

  static clearInset(deviceId: Number) {
    return request({
      url: `/api/v1/manage/h5/device/isa/${deviceId}/cleanup`,
      method: 'get',
    })
  }

  // 虫情图表
  static insectPestIsa(deviceId: number, xUnit: string, params: Object) {
    return request({
      url: `/api/v1/manage/h5/device/isa/${deviceId}/chart/${xUnit}`,
      method: 'get',
      params,
    })
  }
  // 虫情运行日志
  static insectRunPage(data) {
    return request({
      url: '/api/v1/manage/h5/device/isa/running-log/page',
      method: 'get',
      params: data,
    })
  }

  // 虫情分析记录记录
  static insectLogPage(data) {
    return request({
      url: '/api/v1/manage/h5/device/isa/analyze-log/page',
      method: 'get',
      params: data,
    })
  }

  // 删除运行记录
  static deleteRun(imgSerialNum: any) {
    return request({
      url: '/api/v1/manage/h5/device/isa/running-log?imgSerialNum=' + imgSerialNum,
      method: 'delete',
    })
  }

  // 删除分析记录
  static deleteLog(imgSerialNum: any) {
    return request({
      url: '/api/v1/manage/h5/device/isa/analyze-log?imgSerialNum=' + imgSerialNum,
      method: 'delete',
    })
  }
}
