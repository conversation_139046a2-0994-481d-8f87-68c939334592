<template>
  <!-- <div class="page-content"> -->
  <!-- <CustomNavTitle title="设备管理" /> -->

  <div class="list">
    <div v-if="auth('mobile:home:device:light:device:list')" @click="toList(1)" class="list-item">
      <!-- <nut-badge :value="8" color="#FF6363"> -->
      <img :src="IconShachongdeng" alt="" />
      <!-- </nut-badge> -->
      <p>智慧杀虫灯</p>
    </div>
    <div
      v-if="auth('mobile:home:device:analyzer:device:list')"
      @click="toList(2)"
      class="list-item"
    >
      <!-- <nut-badge :value="8" color="#FF6363"> -->
      <img :src="IconChongqing" alt="" />
      <!-- </nut-badge> -->
      <p>虫情分析仪</p>
    </div>
    <div v-if="auth('mobile:home:device:machine:device:list')" @click="toList(3)" class="list-item">
      <img :src="IconShangqing" alt="" />
      <p>墒情气象一体机</p>
    </div>
    <div v-if="auth('211400')" @click="toList(4)" class="list-item">
      <img :src="IconMonitor" alt="" />
      <p>苗情监控</p>
    </div>
  </div>
  <!-- </div> -->
</template>

<script setup lang="ts">
import IconShachongdeng from '../../assets/device/<EMAIL>'
import IconChongqing from '../../assets/device/<EMAIL>'
import IconShangqing from '../../assets/device/<EMAIL>'
import IconMonitor from '../../assets/device/<EMAIL>'

import Taro from '@tarojs/taro'
import { auth } from '@/store/permisson'

const toList = (type: number) => {
  let url = `/pages/deviceManagement/lamp/index`
  switch (type) {
    case 1:
      url = `/pages/deviceManagement/lamp/index`
      break
    case 2:
      url = `/pages/deviceManagement/insectPestSituation/index`
      break
    case 3:
      url = `/pages/deviceManagement/soilMoisture/index`
      break
    case 4:
      url = `/pages/deviceManagement/monitor/index`
      break
    default:
      break
  }
  Taro.navigateTo({
    url,
  })
}
</script>

<style scoped lang="scss">
.list {
  padding: 0 20px;
  display: flex;
  width: 690px;
  background: #fff;
  border-radius: 20px;
  box-sizing: border-box;
  .list-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    p {
      font-size: 22px;
      color: #101010;
      margin-top: 20px;
    }
    img {
      width: 109px;
      height: 109px;
    }
  }
}
</style>
