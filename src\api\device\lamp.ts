import request from '../../utils/request'

export default class LampAPI {
  /** 各类开关 */
  /** type:
   *  静态模式 static-mode
   *  定位开关 located
   *  灯允许开启 disabled
   *  灯强制开启 forced
   * */
  static changeStatus(type: any, data: any) {
    return request<any, any>({
      url: `/api/v1/manage/h5/device/ikl/${type}`,
      method: 'put',
      data,
    })
  }

  // 设备控制
  /**
   * @param {number} id 设备id
   * @param {number} killThreshold 杀虫阈值
   * @param {number} mode 设备模式
   * @param {array} workHours 工作时间
   */
  static deviceControlSave(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/ikl/control`,
      method: 'put',
      data,
    })
  }

  // ........................... 图表........................
  // 图表 type 电压voltage 电流 current 亮灯统计 light 杀虫虫数量 kill-count 温度temperature 湿度humidity
  static voltageChat(data: any) {
    return request({
      url: `/api/v1/manage/h5/device/ikl/${data.deviceId}/chart/${data.type}/${data.unit}?query=${data.query}`,
      method: "get",
    });
  }
}
