<template>
  <nut-tabs
    v-model="activeKey"
    title-scroll
    background="#FFFFFF"
    :class="verticalScreen ? ['vertical-tabs'] : ''"
  >
    <template #titles>
      <div
        v-for="(item, index) in tabList"
        :key="item.paneKey"
        class="custom-tab-item"
        @click="handleTabClick(item.paneKey)"
      >
        <div
          class="custom-title"
          :class="{
            active: activeKey === item.paneKey,
          }"
        >
          {{ item.title }}
          <span v-if="item.count && index === 0" class="count-badge">{{ item.count }}</span>
        </div>
      </div>
    </template>
  </nut-tabs>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TabItem {
  title: string
  paneKey: number | string
  count?: number
}

const props = defineProps<{
  modelValue: number | string
  tabList: TabItem[]
  verticalScreen: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | string): void
  (e: 'tab-click', paneKey: number | string): void
}>()

const activeKey = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const handleTabClick = (paneKey: number | string) => {
  activeKey.value = paneKey
  emit('tab-click', paneKey)
}
</script>

<style scoped lang="scss">
:deep(.nut-tab-pane) {
  display: none !important;
}
:deep(.nut-tabs__list) {
  // background: #fff !important;
}

.custom-tab-item {
  flex: 1;
  text-align: center;
  position: relative;
  margin: 0 10px;

  .custom-title {
    display: inline-block;
    font-size: 30px;
    color: #707070;
    padding: 20px 0;
    position: relative;
    z-index: 1;

    &.active {
      color: #101010;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 6px;
        background: #02a15b;
        border-radius: 2px;
      }
    }

    .count-badge {
      position: absolute;
      top: 10px;
      right: -38px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #ff4d4f;
      border-radius: 32px;
      font-size: 16px;
      color: #fff;
      font-weight: normal;
    }
  }
}
// 竖屏模式下，tab标题区域高度不够，设置overflow-y: auto
.vertical-tabs {
  :deep(.nut-tabs__titles.scrollable) {
    overflow-y: auto !important;
  }
}
</style>
