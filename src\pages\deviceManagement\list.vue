<template>
  <div class="page-content">
    <CustomNavTitle :title="type === '1' ? '安装工单' : '售后工单'" />

    <CustomTabs v-model="status" :tab-list="tabList" />
  </div>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref } from 'vue'
import CustomTabs from '../../components/CustomTabs.vue'

const status = ref(1)
const tabList = ref([
  {
    title: '待处理',
    paneKey: 1,
    count: 7,
  },
  {
    title: '已处理',
    paneKey: 2,
    count: 0,
  },
])
const type = Taro.getCurrentInstance().router?.params.type
</script>

<style scoped lang="scss">
:deep(.nut-tab-pane) {
  display: none !important;
}
:deep(.nut-tabs__list) {
  background: #fff !important;
  padding: 0 32px;
}

.custom-tab-item {
  flex: 1;
  text-align: center;
  position: relative;

  .custom-title {
    display: inline-block;
    font-size: 28px;
    color: #999;
    padding: 20px 0;
    position: relative;

    &.active {
      color: #333;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: #07c160;
        border-radius: 2px;
      }
    }

    .count-badge {
      position: absolute;
      top: 12px;
      right: -16px;
      min-width: 28px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background: #ff4d4f;
      border-radius: 14px;
      font-size: 20px;
      color: #fff;
      padding: 0 6px;
      transform: scale(0.8);
      font-weight: normal;
    }
  }
}
</style>
