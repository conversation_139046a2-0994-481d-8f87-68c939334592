import axios, { AxiosRequestConfig, AxiosResponse } from 'taro-axios'
import Taro from '@tarojs/taro'
import { nextTick } from 'vue'

// import { useRouter } from 'vue-router'
// 根据自身规范修改![](https://tvax1.sinaimg.cn/large/008i3skNgy1gxfn11mr8yj314w0u0tdg.jpg)

const instance = axios.create({
  // 超时时间 1 分钟
  timeout: 30 * 1000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

instance.interceptors.request.use((config: AxiosRequestConfig) => {
  const token = Taro.getStorageSync('token')
  config.headers = {
    Authorization: `Bearer ${token}`,
    token,
    ...config.headers,
  }
  return config
})

const showToast = (title: string) => {
  Taro.showToast({
    title,
    icon: 'none',
    duration: 3000,
  })
}

interface ApiResult<T> {
  code: number
  message?: string
  data: T
}
// Taro.showToast 和loading 是单例 所以只有成功时候hideLoading 其他情况showToast
export default function request<T>(options: AxiosRequestConfig = {}) {
  // Taro.showLoading({
  //   title: '加载中...',
  // })
  Taro.showNavigationBarLoading()
  return new Promise<T>((resolve, reject) => {
    instance(options)
      .then((response: AxiosResponse<ApiResult<T>>) => {
        // Taro.hideLoading()
        if (response?.status === 200 && response?.data?.code === 200) {
          resolve(response.data.data as T)
        } else {
          Taro.showModal({
            title: '提示',
            content: response?.data?.msg ?? '请求失败',
            showCancel: false,
          })
          reject(response.data.data)
        }
      })
      .catch((result) => {
        Taro.hideLoading()
        const statusCode = result?.status || result?.response?.status

        // 处理HTTP状态码
        if (statusCode === 401) {
          showToast('用户未授权，请重新登录')
          //TODO: 处理登录逻辑
        } else if (statusCode === 403) {
          Taro.showModal({
            title: '提示',
            content: '登录过期，请重新登录',
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                Taro.removeStorageSync('token')
                Taro.navigateTo({
                  url: '/pages/login/index',
                })
              }
            },
          })
        } else {
          showToast(result.response?.data?.msg ?? '请求失败')
        }
        reject(result)
      })
  })
}
