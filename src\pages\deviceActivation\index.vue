<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />
  <div v-else class="device-activation">
    <!-- <CustomNavTitle :title="title" :showBack="true" background="#fff" /> -->

    <div class="search-container relative">
      <nut-searchbar
        v-model="searchValue"
        placeholder="输入设备编号搜索设备"
        @update:model-value="handleSearchInput"
        @focus="
          () => {
            if (searchValue && deviceList.length > 0) {
              showDropdown = true
            }
          }
        "
      >
        <template #rightout>
          <Scan2 size="20" color="#01A15B" @click="scanDevice" />
        </template>
        <template #leftin>
          <Search2 color="#D4D4D4" />
        </template>
      </nut-searchbar>

      <!-- 下拉选择列表 -->
      <div v-if="showDropdown" class="dropdown-list">
        <div
          v-for="(device, index) in deviceList"
          :key="index"
          class="dropdown-item"
          @click="selectDevice(device)"
        >
          {{ device.serialNum }} - {{ device.deviceName }}
        </div>
        <div v-if="deviceList.length === 0" class="dropdown-empty">暂无数据</div>
      </div>

      <!-- 批次列表 -->
      <scroll-view
        class="batch-list"
        scroll-y
        :lowerThreshold="200"
        :refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="loadMore"
      >
        <div class="list-content">
          <div
            class="batch-item"
            @click="toDeviceList(item.batchNum)"
            v-for="item in batchList"
            :key="item.id"
          >
            <div class="batch-item-left">
              <img :src="iconMap[type]" alt="" />
            </div>
            <div class="batch-item-right">
              <div class="name">批次：{{ item.batchNum }}</div>
              <div class="count">
                <div>数量：{{ item.deviceTotal }}</div>
                <div>已激活：{{ item.activatedCount }}</div>
              </div>
            </div>
          </div>
          <emput v-if="batchList.length === 0" message="暂无批次数据" />
          <!-- <nut-empty v-if="batchList.length === 0" description="暂无批次数据" /> -->
          <div v-if="loading" class="loading-more">
            <span>加载中...</span>
          </div>
        </div>
      </scroll-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Search2, Scan2 } from '@nutui/icons-vue-taro'
import CustomNavTitle from '../../components/CustomNavTitle/CustomNavTitle.vue'
import DeviceActivationAPI from '../../api/deviceActivation'
import IconShachongdeng from '../../assets/device/<EMAIL>'
import IconChongqing from '../../assets/device/<EMAIL>'
import IconShangqing from '../../assets/device/<EMAIL>'
import Taro from '@tarojs/taro'
import Scanner from '../../components/Scanner/Scanner.vue'
import { eventCenter, getCurrentInstance } from '@tarojs/taro'

const { type } = Taro.getCurrentInstance().router?.params || {}
const titleMap = {
  1: '智慧杀虫灯',
  3: '虫情分析仪',
  2: '墒情气象一体机',
}
const iconMap = {
  1: IconShachongdeng,
  3: IconChongqing,
  2: IconShangqing,
}

// 设备搜索相关
interface Device {
  serialNum: string
  deviceName: string
  [key: string]: any
}

const searchValue = ref('')
const deviceList = ref<Device[]>([])
const showDropdown = ref(false)
const searchTimer = ref<NodeJS.Timeout | null>(null)

// 批次列表相关
interface BatchItem {
  id: number | string
  batchNum: string
  model: string
  deviceTotal: number
  activatedCount: number
}

const batchList = ref<BatchItem[]>([])
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loading = ref(false)
const refreshing = ref(false)

// 设备搜索处理
const handleSearchInput = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  if (!searchValue.value) {
    deviceList.value = []
    showDropdown.value = false
    return
  }

  searchTimer.value = setTimeout(async () => {
    try {
      const response = await DeviceActivationAPI.searchDevice(searchValue.value, type)
      deviceList.value = response || []
      showDropdown.value = true
    } catch (error) {
      console.error('搜索设备失败', error)
      deviceList.value = []
      showDropdown.value = false
    }
  }, 500)
}

// 跳转到设备列表页面
const toDeviceList = (batchNum: number) => {
  Taro.navigateTo({
    url: `/pages/batchDeviceList/batchDeviceList?type=${type}&batchNum=${batchNum}`,
  })
}

const showScanner = ref(false)
const scanResult = ref('')
const toActivation = async (str: string, analysisUrl = true) => {
  if (analysisUrl) {
    try {
      const serialNumMatch = str.match(/[?&]serialNum=([^&]+)/)
      scanResult.value = serialNumMatch ? serialNumMatch[1] : ''
    } catch (error) {
      console.error('序列号解析失败', error)
      scanResult.value = ''
    }
  } else {
    scanResult.value = str
  }

  showScanner.value = false
  if (!scanResult.value) {
    return
  }

  if (scanResult.value.includes('MB')) {
    Taro.showModal({
      title: '提示',
      content: '设备不支持联网，不可激活',
      showCancel: false,
    })
    return
  }

  Taro.showLoading({
    title: '设备查询中...',
  })
  try {
    const response = await DeviceActivationAPI.activated(scanResult.value)
    if (!response) {
      Taro.showModal({
        title: '提示',
        content: '设备不存在，请检查二维码',
        showCancel: false,
      })
      return
    }

    if (response.type !== Number(type)) {
      Taro.showModal({
        title: '提示',
        content: '设备类型不匹配，请检查二维码',
        showCancel: false,
      })
      return
    }

    if (response.activated) {
      Taro.showModal({
        title: '提示',
        content: '设备已激活',
        cancelText: '重新绑定',
        confirmText: '我知道了',
        success: (result) => {
          if (!result.confirm) {
            const { type, innerModelName, serialNum, id, batchNum } = response || {}

            Taro.navigateTo({
              url: `/pages/batchDeviceList/batchDeviceList?type=${type}&innerModelName=${innerModelName}&serialNum=${serialNum}&id=${id}&batchNum=${batchNum}`,
            })
          }
        },
      })
      return
    }

    const { innerModelName, batchNum, serialNum } = response || {}
    Taro.navigateTo({
      url: `/pages/batchDeviceList/batchDeviceList?type=${type}&innerModelName=${innerModelName}&serialNum=${serialNum}&batchNum=${batchNum}`,
    })
  } finally {
    Taro.hideLoading()
  }
}

// 修改扫码函数
const scanDevice = () => {
  showScanner.value = true
}

// 选择设备
const selectDevice = (device: Device) => {
  searchValue.value = device.serialNum
  showDropdown.value = false
  toActivation(device.serialNum, false)
}

// 重置列表
const resetList = () => {
  batchList.value = []
  pageNum.value = 1
  hasMore.value = true
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    resetList()
    await fetchBatchList()
  } finally {
    refreshing.value = false
  }
}

// 获取批次列表
const fetchBatchList = async () => {
  if (loading.value) return

  loading.value = true
  try {
    const params = {
      type,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      serialNum: searchValue.value,
    }

    const res = await DeviceActivationAPI.batchList(params)
    const { records = [], total = 0 } = res || {}

    batchList.value = pageNum.value === 1 ? records : [...batchList.value, ...records]
    hasMore.value = batchList.value.length < total
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value) return
  if (!hasMore.value) {
    Taro.showToast({
      title: '没有更多数据了',
      icon: 'none',
    })
    return
  }
  pageNum.value++
  fetchBatchList()
}

// 添加点击外部关闭下拉列表的处理函数
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const dropdownList = document.querySelector('.dropdown-list')
  const searchbar = document.querySelector('.nut-searchbar')

  if (dropdownList && searchbar && !dropdownList.contains(target) && !searchbar.contains(target)) {
    showDropdown.value = false
  }
}

onMounted(() => {
  // 初始加载批次列表
  const instance = getCurrentInstance()
  if (instance?.router?.onShow) {
    eventCenter.on(instance.router.onShow, () => {
      const { type } = Taro.getCurrentInstance().router?.params || {}
      document.title = titleMap[type] + '激活'

      fetchBatchList()
    })
  }
  // 添加点击事件监听器
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除点击事件监听器
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
:deep(.nut-searchbar__search-icon) {
  margin-right: 22px;
}

.device-activation {
  background: #f5f6fa;
  height: 100vh;
}

.search-container {
  position: relative;
  width: 100%;
}

.dropdown-list {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  width: 100%;
  max-height: 600px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
  margin-top: 0px;
}

.dropdown-item {
  padding: 20px 0 20px 80px;
  font-size: 28px;
  border-bottom: 1px solid #f5f5f5;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.batch-list {
  height: calc(100vh - 160px);
  .batch-item {
    width: 690px;
    padding: 30px;
    box-sizing: border-box;
    margin: 30px auto;
    border-radius: 20px;
    background: #fff;
    display: flex;
    .batch-item-left {
      margin-right: 20px;
      img {
        width: 60px;
        height: 60px;
      }
    }
    .batch-item-right {
      flex: 1;
      font-size: 28px;
      color: #909090;
      .name {
        font-size: 32px;
        color: #000;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .batch {
        margin: 10px 0 0;
      }
      .count {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 24px;
}

.dropdown-empty {
  height: 100px;
  text-align: center;
  line-height: 100px;
  font-size: 28px;
  color: #909090;
}
</style>
