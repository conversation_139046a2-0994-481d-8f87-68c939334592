<template>
  <!-- <div class="page-content"> -->
  <!-- <CustomNavTitle title="工单管理" /> -->

  <div class="list">
    <div v-if="auth('mobile:home:workOrder:install:list')" @click="toList(1)" class="list-item">
      <nut-badge
        v-if="Number(countInfo.installCount) > 0"
        :value="countInfo.installCount"
        color="#FF6363"
        style="height: 59px !important"
      >
        <img :src="IconInstall" alt="" />
      </nut-badge>
      <img v-else :src="IconInstall" alt="" />

      <p>安装工单</p>
    </div>
    <div v-if="auth('mobile:home:workOrder:afterSale:list')" @click="toList(2)" class="list-item">
      <nut-badge
        v-if="Number(countInfo.afterSalesCount) > 0"
        :value="countInfo.afterSalesCount"
        color="#FF6363"
        style="height: 59px !important"
      >
        <img :src="IconAfterSale" alt="" />
      </nut-badge>
      <img v-else :src="IconAfterSale" alt="" />

      <p>售后工单</p>
    </div>
  </div>
  <!-- </div> -->
</template>

<script setup lang="ts">
import IconInstall from '../../assets/icon_anzhuang.png'
import IconAfterSale from '../../assets/icon_shouhou.png'
import Taro from '@tarojs/taro'
import WorkOrderAPI from '../../api/workOrder'
import { onMounted, ref } from 'vue'
import { eventCenter, getCurrentInstance } from '@tarojs/taro'
import { auth } from '@/store/permisson'

const toList = (type: number) => {
  Taro.navigateTo({
    url: `/pages/workOrder/list?type=${type}`,
  })
}

const countInfo = ref<any>({})
onMounted(() => {
  const instance = getCurrentInstance()
  if (instance?.router?.onShow) {
    eventCenter.on(instance.router.onShow, () => {
      WorkOrderAPI.workOrderCount().then((res) => {
        countInfo.value = res || { installCount: 0, afterSalesCount: 0 }
      })
    })
  }
})
</script>

<style scoped lang="scss">
.list {
  display: flex;
  width: 690px;
  background: #fff;
  border-radius: 20px;
  box-sizing: border-box;

  .list-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    p {
      font-size: 22px;
      color: #101010;
      margin-top: 20px;
    }
    img {
      width: 109px;
      height: 109px;
    }
  }
}
</style>
