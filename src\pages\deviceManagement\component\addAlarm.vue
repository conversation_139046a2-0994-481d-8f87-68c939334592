<template>
    <div>
       <AlarmSettings v-if="deviceInfo"
       :deviceInfo="deviceInfo"
      @finished="handleFinished" />
        <!-- :innerModel="deviceInfo.innerModel" :deviceId="deviceInfo.deviceId" :id="deviceInfo.id"  -->

    </div>
</template>
<script setup lang="ts">
import { ref, onMounted,reactive,computed, } from 'vue'
import Taro from '@tarojs/taro'

const deviceInfo = ref<any>(null)

onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  // id.value = JSON.parse(decodeURIComponent(params.id));
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  console.log(params,deviceInfo.value, '11告警设置111', Taro.getCurrentInstance().router)

  // getUserInfo();
  // getDetailBase()
})

const handleFinished = (res: any) => {
  console.log(res, '告警设置返回')
  Taro.navigateBack({
    delta: 1,
  })
}

</script>

<style scoped lang="scss">
</style>