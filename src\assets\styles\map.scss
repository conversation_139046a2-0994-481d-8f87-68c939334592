
.bigemap-marker-icon {
  &.iconScd {
    .header {
      background: linear-gradient(180deg, #04d579 0%, #019e59 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(4, 213, 121, 0.3) 0%,
        rgba(1, 158, 89, 0.6) 40%,
        rgba(1, 158, 89, 0.2) 80%,
        transparent 100%
      );
    }
  }

  &.iconScd.close {
    .header {
      background: linear-gradient(180deg, #ffbe17 0%, #ff9803 100%);
    }
    &.active::before {
      background: radial-gradient(
        circle,
        rgba(255, 190, 23, 0.3) 0%,
        rgba(255, 152, 3, 0.6) 40%,
        rgba(255, 152, 3, 0.2) 80%,
        transparent 100%
      );
    }

  }


  &.iconSq {
    .header {
      background: linear-gradient(180deg, #45bffc 0%, #2391f2 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(69, 191, 252, 0.3) 0%,
        rgba(35, 145, 242, 0.6) 40%,
        rgba(35, 145, 242, 0.2) 80%,
        transparent 100%
      );
    }
  }

  &.iconCq {
    .header {
      background: linear-gradient(180deg, #ffbe17 0%, #ff9803 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(255, 190, 23, 0.3) 0%,
        rgba(255, 152, 3, 0.6) 40%,
        rgba(255, 152, 3, 0.2) 80%,
        transparent 100%
      );
    }
  }

  &.iconJk {
    .header {
      background: linear-gradient(180deg, #979eef 0%, #3a53de 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(151, 158, 239, 0.3) 0%,
        rgba(58, 83, 222, 0.6) 40%,
        rgba(58, 83, 222, 0.2) 80%,
        transparent 100%
      );
    }
  }

  &.alarm {
    .header {
      background: linear-gradient(180deg, #ff9377 0%, #ec3e2a 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(255, 147, 119, 0.4) 0%,
        rgba(236, 62, 42, 0.7) 40%,
        rgba(236, 62, 42, 0.3) 80%,
        transparent 100%
      );
    }
  }

  &.offline {
    .header {
      background: linear-gradient(180deg, #a8a8a8 0%, #404643 100%);
    }

    &.active::before {
      background: radial-gradient(
        circle,
        rgba(168, 168, 168, 0.2) 0%,
        rgba(64, 70, 67, 0.4) 40%,
        rgba(64, 70, 67, 0.1) 80%,
        transparent 100%
      );
    }
  }

  &.deviceIcon.active {
    // 底部添加光圈扩散效果
    position: relative;
    z-index: 1;

    // 图标跳动效果
    .header {
      animation: bounce 2.4s ease-in-out infinite;
      transform-origin: center bottom;
    }

    &::before {
      content: '';
      position: absolute;
      width: 60px;
      height: 40px;
      border-radius: 50%;
      transform: translate(-50%, 50%);
      bottom: 0;
      left: 50%;
      z-index: -1;
      animation: ripple 1.8s ease-out infinite;
    }
  }
}

// 通用扩散圆圈动画
@keyframes ripple {
  0% {
    transform: translate(-50%, 50%) scale(0.8);
    opacity: 0.8;
  }
  30% {
    transform: translate(-50%, 50%) scale(1);
    opacity: 0.7;
  }
  60% {
    transform: translate(-50%, 50%) scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, 50%) scale(1.5);
    opacity: 0;
  }
}

// 图标跳动动画
@keyframes bounce {
  0%,
  20%,
  40%,
  60%,
  80%,
  100% {
    transform: translateY(0) scale(1);
  }
  10% {
    transform: translateY(-4px) scale(1.02);
  }
  30% {
    transform: translateY(-2px) scale(1.01);
  }
  50% {
    transform: translateY(-6px) scale(1.03);
  }
  70% {
    transform: translateY(-3px) scale(1.015);
  }
  90% {
    transform: translateY(-1px) scale(1.005);
  }
}
