import request from '../utils/request'

export default class DeviceActivationAPI {
  // 搜索设备
  static searchDevice(serialNum: string, deviceType: number): Promise<Device[]> {
    return request({
      url: '/api/v1/manage/h5/device/optionsLimit',
      method: 'get',
      params: { serialNum, deviceType },
    })
  }

  // 获取批次列表
  static batchList(params: BatchListParams): Promise<BatchListResponse> {
    return request({
      url: '/api/v1/manage/h5/device/batch/page',
      method: 'get',
      params,
    })
  }

  // 查询设备是否激活以及设备信息
  static activated(serialNum: string) {
    return request<any>({
      url: `/api/v1/manage/h5/device/base`,
      method: 'get',
      params: { serialNum },
    })
  }

  // 设备激活
  static activate(data: ActivateRequest) {
    return request<any>({
      url: `/api/v1/manage/h5/device/activate`,
      method: 'post',
      data,
    })
  }

  // 设备解绑
  static unbind(id: number) {
    return request<any>({
      url: `/api/v1/manage/h5/device/${id}/unbind`,
      method: 'put',
    })
  }
}

interface ActivateRequest {
  serialNum: string
  activeBy: string
  imei: string
}

interface Device {
  serialNum: string
  deviceName: string
  [key: string]: any
}

interface BatchListParams {
  type?: number
  pageNum: number
  pageSize: number
  serialNum?: string
}

interface BatchListResponse {
  records: {
    id: number | string
    batchNum: string
    model: string
    deviceTotal: number
    activatedCount: number
  }[]
  total: number
}

export interface ListRequest {
  /**
   * 批次号
   */
  // batchNum?: string;
  /**
   * 关键字
   */
  // keywords?: string;
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  /**
   * 设备类型
   */
  type: number
  [property: string]: any
}
