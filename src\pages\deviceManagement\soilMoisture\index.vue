<template>
  <Scanner v-if="showScanner" @scanResult="toActivation" />
  <div v-else class="device-activation">
    <CustomNavTitle title="墒情气象一体机" :showBack="true" background="#fff" />

    <div class="search-container relative">
      <nut-searchbar
        v-model="searchValue"
        placeholder="请输入关键词搜索"
        @update:model-value="handleSearchInput"
        @focus="
          () => {
            if (searchValue && deviceList.length > 0) {
              showDropdown = true
            }
          }
        "
      >
        <template #rightin>
          <nut-button color="#02a15b" size="mini" style="margin-right: 0px" @click="getListChange"
            >搜索</nut-button
          >
        </template>
        <template #leftin>
          <Scan2 size="20" color="#01A15B" @click="scanDevice" />
        </template>
        <template #rightout>
          <img :src="shaixuan" mode="scaleToFill" class="shaixuanIcon" @click="openFilter" />
        </template>
      </nut-searchbar>

      <CustomTabs v-model="status" :tab-list="tabList" />

      <!-- 下拉选择列表 -->
      <div v-if="showDropdown" class="dropdown-list">
        <div
          v-for="(device, index) in deviceList"
          :key="index"
          class="dropdown-item"
          @click="selectDevice(device)"
        >
          {{ device.serialNum }} - {{ device.deviceName }}
        </div>
        <!-- <div v-if="deviceList.length === 0" class="dropdown-empty">暂无数据</div> -->
      </div>

      <!-- 墒情列表 -->
      <scroll-view
        class="batch-list"
        scroll-y
        :lowerThreshold="200"
        :refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="loadMore"
      >
        <div class="list-content">
          <div
            class="batch-item"
            v-for="item in batchList"
            :key="item.id"
            @click="monitorDetail(item)"
          >
            <div class="batch-item-left">
              <img :src="item.online ? IconOnline : UnIconOnline" alt="" />
            </div>
            <div class="batch-item-right">
              <div class="batch-item-title">
                <div class="name">{{ item.serialNum }}</div>
                <div :class="item.online ? 'online' : 'unonline'">
                  {{ item.online ? '在线' : '离线' }}
                </div>
              </div>
              <div class="batch">设备型号：{{ item.innerModelName }}</div>
              <div v-if="status === 2" class="batch2">
                客户名称：{{ item.customerName || '--' }}
              </div>
              <div v-if="status === 2" class="batch2">区域显示：{{ item.areaName || '--' }}</div>
              <!-- <div class="count">
                <div>数量：{{ item.deviceTotal }}</div>
                <div>已激活：{{ item.activatedCount }}</div>
              </div> -->
            </div>
          </div>
          <emput v-if="batchList.length === 0" message="暂无墒情数据" />
          <!-- <nut-empty v-if="batchList.length === 0" description="暂无墒情数据" /> -->
          <div v-if="loading" class="loading-more">
            <span>加载中...</span>
          </div>
        </div>
      </scroll-view>
    </div>
    <nut-popup
      v-model:visible="showFilter"
      round
      position="right"
      :close-on-click-overlay="false"
      :style="{ width: '94%', height: '100%' }"
    >
      <Filter @change="filterChange" :filterData="filterData" />
    </nut-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Search2, Scan2 } from '@nutui/icons-vue-taro'
import CustomNavTitle from '../../../components/CustomNavTitle/CustomNavTitle.vue'
import DeviceActivationAPI from '../../../api/deviceActivation'
import IconOnline from '../../../assets/device/<EMAIL>'
import UnIconOnline from '../../../assets/device/<EMAIL>'
import Taro from '@tarojs/taro'
import Scanner from '../../../components/Scanner/Scanner.vue'
import CustomTabs from '../../../components/CustomTabs.vue'
import BaseInfoAPI from '../../../api/device/baseInfo'
import Filter from '@/components/filter.vue'
import shaixuan from '@/assets/device/icon_shaixuan.png'
// import { checkPrime } from "crypto";

const status = ref(1)
const tabList = ref([
  {
    title: '未售设备',
    paneKey: 1,
    count: '',
  },
  {
    title: '已售设备',
    paneKey: 2,
    count: '',
  },
])

// 设备搜索相关
interface Device {
  serialNum: string
  deviceName: string
  [key: string]: any
}

// 筛选相关
const filterData = ref({
  online: '', // 设备状态
  areaId: '', // 区域
  customerId: '', // 客户
})

const showFilter = ref(false)

const searchValue = ref('')
const deviceList = ref<Device[]>([])
const showDropdown = ref(false)
const searchTimer = ref<NodeJS.Timeout | null>(null)

// 墒情列表相关
interface BatchItem {
  id: number | string
  batchNum: string
  model: string
  deviceTotal: number
  activatedCount: number
}

const batchList = ref<BatchItem[]>([])
const pageNum = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loading = ref(false)
const refreshing = ref(false)

// 设备搜索处理
const handleSearchInput = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  if (!searchValue.value) {
    deviceList.value = []
    showDropdown.value = false
    return
  }

  searchTimer.value = setTimeout(async () => {
    try {
      const response = await DeviceActivationAPI.searchDevice(searchValue.value, 2)
      deviceList.value = response || []
      showDropdown.value = true
    } catch (error) {
      console.error('搜索设备失败', error)
      deviceList.value = []
      showDropdown.value = false
    }
  }, 500)
}

const showScanner = ref(false)
const scanResult = ref('')
const toActivation = async (res: string, type?: any) => {
  showScanner.value = false
  scanResult.value = res
  let obj: any = {}
  let deviceId: any = ''
  if (type) {
    obj = deviceList.value.find((item) => item.serialNum === res)
    deviceId = obj.deviceId
  } else {
    const urlParams = new URLSearchParams(res.split('?')[1])
    const deviceType: any = urlParams.get('deviceType')
    if (deviceType != 2) {
      Taro.showModal({
        title: '提示',
        content: '请扫描正确的墒情二维码',
        showCancel: false,
      })
      // Taro.showToast({
      //   title: '请扫描正确的墒情二维码',
      //   icon: 'none',
      // })
      return
    }

    const serialNum = urlParams.get('serialNum')
    const ress = await BaseInfoAPI.getDeviceIdFrom(serialNum)
    if (!ress) {
      Taro.showToast({
        title: '没有查到设备信息',
        icon: 'none',
      })
      return
    }
    deviceId = ress.id
  }
  // showScanner.value = false
  // scanResult.value = res

  const data = {
    id: deviceId,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/soilMoisture/detail?data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

// 修改扫码函数
const scanDevice = () => {
  showScanner.value = true
}

// 选择设备
const selectDevice = (device: Device) => {
  searchValue.value = device.serialNum
  showDropdown.value = false
  toActivation(device.serialNum, 1)
}

// 重置列表
const resetList = () => {
  batchList.value = []
  pageNum.value = 1
  hasMore.value = true
}

// 获取下来筛选列表
const getListChange = async () => {
  resetList()
  await fetchBatchList()
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    resetList()
    await fetchBatchList()
  } finally {
    refreshing.value = false
  }
}

// ...原有代码...

watch(status, async (newValue, oldValue) => {
  console.log(newValue, oldValue)
  pageNum.value = 1
  batchList.value = []
  // 重置列表
  // resetList();
  // 重新获取墒情列表
  await fetchBatchList()
})

// ...原有代码...
// 获取墒情列表
const fetchBatchList = async () => {
  if (loading.value) return

  loading.value = true
  try {
    const params = {
      type: 2, // type  设备类型 1 智慧杀虫灯 2 墒情气象一体机 3 虫情分析仪 4 苗情监控
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      keywords: searchValue.value,
      sold: status.value === 1 ? false : true,
      online: filterData.value.online === '1' ? true : filterData.value.online === '2' ? false : '', // 设备状态
      areaId: filterData.value.areaId, // 区域
      customerId: filterData.value.customerId, // 客户
    }

    const res = await BaseInfoAPI.deviceList(params)
    const { records = [], total = 0 } = res || {}

    batchList.value = pageNum.value === 1 ? records : [...batchList.value, ...records]
    console.log(batchList.value)
    hasMore.value = batchList.value.length < total
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (loading.value) return
  if (!hasMore.value) {
    Taro.showToast({
      title: '没有更多数据了',
      icon: 'none',
    })
    return
  }
  pageNum.value++
  fetchBatchList()
}

// 添加点击外部关闭下拉列表的处理函数
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const dropdownList = document.querySelector('.dropdown-list')
  const searchbar = document.querySelector('.nut-searchbar')

  if (dropdownList && searchbar && !dropdownList.contains(target) && !searchbar.contains(target)) {
    showDropdown.value = false
  }
}

// 墒情详情
const monitorDetail = (item: any) => {
  // ?serialNum=${item.serialNum}
  const data = {
    id: item.id,
    deviceId: item.deviceId,
    channelId: item.channelId,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/soilMoisture/detail?data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

// ..............打开筛选 start .................

function openFilter() {
  showFilter.value = true
}
function filterChange(data) {
  console.log(data)
  filterData.value = JSON.parse(JSON.stringify(data))
  showFilter.value = false
  batchList.value = []
  pageNum.value = 1
  hasMore.value = true
  // 初始加载杀虫灯列表
  fetchBatchList()
}

// ..............打开筛选 end .................

onMounted(() => {
  // 初始加载墒情列表
  fetchBatchList()
  // 添加点击事件监听器
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除点击事件监听器
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
:deep(.nut-searchbar__search-icon) {
  margin-right: 22px;
}
:deep(.nut-searchbar__search-input .nut-searchbar__input-inner-icon) {
  padding: 0;
  margin-right: 0px;
}

.device-activation {
  background: #f5f6fa;
  height: 100vh;
}

.search-container {
  position: relative;
  width: 100%;
}

.dropdown-list {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  width: 100%;
  max-height: 600px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
  margin-top: 0px;
}

.dropdown-item {
  padding: 20px 0 20px 80px;
  font-size: 28px;
  border-bottom: 1px solid #f5f5f5;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.batch-list {
  height: calc(100vh - 250px);
  .batch-item {
    width: 690px;
    padding: 30px;
    box-sizing: border-box;
    margin: 30px auto;
    border-radius: 20px;
    background: #fff;
    display: flex;
    .batch-item-left {
      margin-right: 20px;
      img {
        width: 60px;
        height: 60px;
      }
    }
    .batch-item-right {
      flex: 1;
      font-size: 28px;
      color: #909090;
      .batch-item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .name {
        font-size: 32px;
        color: #000;
        font-weight: bold;
      }
      .batch {
        margin: 10px 0 0;
      }
      .batch2 {
        margin: 10px 0 0;
      }
      .count {
        display: flex;
        justify-content: space-between;
      }
      .online {
        position: relative;
        padding-left: 24px; /* 为小圆点留出空间 */
        font-weight: 400;
        font-size: 28px;
        color: #019e59;
      }
      .online::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        margin-top: 3rpx;
        border-radius: 50%;
        background-color: #019e59;
      }
      .unonline {
        position: relative;
        padding-left: 24px; /* 为小圆点留出空间 */
        font-weight: 400;
        font-size: 28px;
        color: #606060;
      }
      .unonline::before {
        content: '';
        position: absolute;
        left: 0px;
        top: 50%;
        margin-top: 3rpx;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #606060;
      }
    }
  }
}

.loading-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 24px;
}

.dropdown-empty {
  height: 100px;
  text-align: center;
  line-height: 100px;
  font-size: 28px;
  color: #909090;
}
</style>
