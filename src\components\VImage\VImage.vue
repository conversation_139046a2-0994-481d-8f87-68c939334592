<script setup lang="ts">
import { ref, watch } from 'vue'
import { getFileUrl } from '@/api/file'
import Taro from '@tarojs/taro'

interface ImageProps {
  src?: string
  previewSrcList?: string[]
  showMenuByLongpress?: boolean
}

const props = defineProps<Partial<ImageProps>>()

const addressableSrc = ref('')
watch(
  () => props.src,
  () => {
    if (!props.src) return
    getFileUrl(props.src).then((url) => {
      addressableSrc.value = url
    })
  },
  { immediate: true }
)

const addressablePreviewSrcList = ref<string[]>([])
watch(
  () => props.previewSrcList,
  () => {
    if (!props.previewSrcList?.length) return
    addressablePreviewSrcList.value = []
    for (const [index, url] of props.previewSrcList.filter(Boolean).entries()) {
      getFileUrl(url).then((res) => {
        addressablePreviewSrcList.value[index] = res
      })
    }
  },
  { immediate: true }
)

// 预览图片
function previewImage() {
  if (!addressableSrc.value) return

  const urls = addressablePreviewSrcList.value.length
    ? addressablePreviewSrcList.value
    : [addressableSrc.value]

  Taro.previewImage({
    current: addressableSrc.value, // 当前显示图片的链接
    urls: urls, // 需要预览的图片链接列表
    showmenu: props.showMenuByLongpress,
  })
}
</script>

<template>
  <image
    v-if="addressableSrc"
    :src="addressableSrc"
    mode="aspectFill"
    v-bind="$attrs"
    @click="previewImage"
  />
</template>

<style scoped lang="scss"></style>
