import request from '../utils/request'

export default class WorkOrderAPI {
  // 统计用户未完成的安装工单和售后工单
  static workOrderCount() {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/workOrderCount',
      method: 'get',
    })
  }

  // 分页查询
  static page(params: PageRequest) {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/page',
      method: 'get',
      params,
    })
  }

  // 获取工单详情
  static getDetail(workOrderId: number) {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/getDetail',
      method: 'get',
      params: { workOrderId },
    })
  }

  // 完成安装工单
  static completeInstall(data: InstallRequest) {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/completeInstall',
      method: 'put',
      data,
    })
  }

  // 修改安装工单
  static updateInstall(data: InstallRequest) {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/updateRecord',
      method: 'put',
      data,
    })
  }

  // 完成售后工单
  static completeAfterSales(data: AfterSaleRequest) {
    return request<any>({
      url: '/api/v1/manage/h5/deviceWorkOrder/completeAfterSales',
      method: 'put',
      data,
    })
  }

  // 完成售后工单
  static deviceIssue() {
    return request<any>({
      url: '/api/v1/device/common/deviceIssue',
      method: 'get',
    })
  }
}

export interface PageRequest {
  /**
   * 页码
   */
  pageNum: number
  /**
   * 每页记录数
   */
  pageSize: number

  /**
   * 1 待指派 2 已指派 4 已处理 （客户端 h5 查询状态 1 待处理，2 待评价，4 已完成）
   */
  status?: number
  /**
   * 1 安装工单 2 售后工单
   */
  type?: number
  [property: string]: any
}

export interface AfterSaleRequest {
  /**
   * 设备id
   */
  deviceId: number
  /**
   * 工单id
   */
  deviceWorkOrderId: number
  /**
   * 处置(安装)图片
   */
  handlerImage?: FileInfo[]
  /**
   * 处置(安装)描述
   */
  handlerRemark?: string
  /**
   * 处置状态1 已解决(已安装) 0 未解决(未安装)
   */
  handlerStatus: number
  /**
   * 故障类型(售后工单独有字段)
   */
  issueType?: number
  /**
   * 故障名称(售后工单独有字段)
   */
  issueTypeName?: string
  [property: string]: any
}

/**
 * 文件对象
 *
 * FileInfo
 */
export interface FileInfo {
  /**
   * 文件名称
   */
  name?: string
  /**
   * 原始名称
   */
  originalFilename?: string
  /**
   * 大小
   */
  size?: number
  /**
   * 格式
   */
  type?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 文件URL
   */
  url?: string
}

export interface InstallRequest {
  /**
   * 如果是安装工单 地址就是安装是上传地址 如果是售后工单 默认带出设备的地址
   */
  address: string
  /**
   * 位置信息
   */
  coordinate: GeoPoint
  /**
   * 设备id
   */
  deviceId: number
  /**
   * 工单id
   */
  deviceWorkOrderId: number
  /**
   * 处置(安装)图片
   */
  handlerImage: FileInfo[]
}

/**
 * 位置信息
 *
 * GeoPoint
 */
export interface GeoPoint {
  latitude?: number
  longitude?: number
  [property: string]: any
}

/**
 * 文件对象
 *
 * FileInfo
 */
export interface FileInfo {
  /**
   * 文件名称
   */
  name?: string
  /**
   * 原始名称
   */
  originalFilename?: string
  /**
   * 大小
   */
  size?: number
  /**
   * 格式
   */
  type?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 文件URL
   */
  url?: string
  [property: string]: any
}
