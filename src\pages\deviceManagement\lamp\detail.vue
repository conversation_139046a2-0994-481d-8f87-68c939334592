<template>
  <div class="page-content">
    <CustomNavTitle title="智慧杀虫灯" :showBack="true" background="#fff" />

    <div v-if="baseData.coordinate?.latitude" ref="mapContainer" class="h-30" />

    <div class="batch-item info-box-title">
      <div class="batch-item-left">
        <img :src="baseData?.online ? IconDevice : UnIconDevice" alt="" />
      </div>
      <div class="batch-item-right">
        <div class="name">{{ baseData.serialNum || '--' }}</div>
        <div class="batch">设备型号：{{ baseData.innerModelName || '--' }}</div>
        <div class="batch">安装状态：{{ baseData.installed ? '已安装' : '未安装' }}</div>

        <div v-if="baseData.coordinate?.latitude">
          <span class="vertical-mid">{{ baseData.address }}</span>
          <span
            @click.stop="openGMap"
            class="ml-1 inline-flex items-center vertical-mid gap-x-0.5"
            text="#1688d1 2.4"
          >
            <image src="@/assets/deviceMap/locate.png" alt="" class="w-2.4 h-2.5" />
            导航
          </span>
        </div>
      </div>
    </div>

    <div v-if="tabList?.length > 0" class="p-3">
      <div class="tab-top-border">
        <CustomTabs v-model="status" :tab-list="tabList" :border="true" />
      </div>
      <div v-if="status === 0">
        <div class="monitor-box-bg border-bottom-20" style="margin-top: 10px">
          <div class="flex-between" style="align-items: center">
            <div class="monitor-title">状态参数</div>
            <div style="display: flex; align-items: center">
              <span class="switch-text">开启静态模式</span>
              <nut-switch
                v-model="baseData.staticMode"
                @click="staticModeChange($event, 'static-mode')"
              />
            </div>
          </div>
          <div class="monitor-line"></div>
          <div style="display: flex; flex-wrap: wrap">
            <div class="text-item" style="width: 50%">
              <div>电压：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.voltage === null || deviceDetailData?.voltage === undefined
                    ? '--'
                    : `${deviceDetailData?.voltage} V`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>电池电量：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.batteryLevel === null ||
                  deviceDetailData?.batteryLevel === undefined
                    ? '--'
                    : `${deviceDetailData?.batteryLevel} %`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>电流：</div>
              <div class="text-1010">
                <span
                  v-if="
                    deviceDetailData?.current !== null && deviceDetailData?.current !== undefined
                  "
                >
                  {{
                    deviceDetailData?.current > 0
                      ? `${deviceDetailData?.current} A (充电)`
                      : `${deviceDetailData?.current} A (放电)`
                  }}
                </span>
                <span v-else>--</span>
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>灯电流：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.lightCurrent === null ||
                  deviceDetailData?.lightCurrent === undefined
                    ? '--'
                    : `${deviceDetailData?.lightCurrent} A`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>风机电流：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.fanCurrent === null ||
                  deviceDetailData?.fanCurrent === undefined
                    ? '--'
                    : `${deviceDetailData?.fanCurrent} A`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>太阳能板电压：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.solarPanelsCurrent === null ||
                  deviceDetailData?.solarPanelsCurrent === undefined
                    ? '--'
                    : `${deviceDetailData?.solarPanelsCurrent} V`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>充电功率：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.chargeW === null || deviceDetailData?.chargeW === undefined
                    ? '--'
                    : `${deviceDetailData?.chargeW} W`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>充电电量：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.chargeAh === null || deviceDetailData?.chargeAh === undefined
                    ? '--'
                    : `${deviceDetailData?.chargeAh} AH`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>温度：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.temperature === null ||
                  deviceDetailData?.temperature === undefined
                    ? '--'
                    : `${deviceDetailData?.temperature} ℃`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>湿度：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.humidity === null || deviceDetailData?.humidity === undefined
                    ? '--'
                    : `${deviceDetailData?.humidity} %`
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>光控：</div>
              <div :class="deviceDetailData?.lightControl == 2 ? 'text-#019E59' : 'text-1010'">
                {{
                  deviceDetailData?.lightControl === 1
                    ? '白天'
                    : deviceDetailData?.lightControl === 0
                    ? '夜晚'
                    : deviceDetailData?.lightControl === 2
                    ? '正常'
                    : '--'
                }}
              </div>
            </div>
            <div class="text-item" style="width: 50%">
              <div>雨控：</div>
              <div :class="deviceDetailData?.rainControl == 2 ? 'text-#019E59' : 'text-1010'">
                {{
                  deviceDetailData?.rainControl === 1
                    ? '雨天'
                    : deviceDetailData?.rainControl === 0
                    ? '晴天'
                    : deviceDetailData?.rainControl === 2
                    ? '正常'
                    : '--'
                }}
              </div>
            </div>
            <div class="monitor-line"></div>
            <div class="text-item" style="width: 50%">
              <div>网络状态：</div>
              <div class="text-1010">
                {{
                  deviceDetailData?.networkType === 1
                    ? '2G'
                    : deviceDetailData?.networkType === 2
                    ? '2.5G'
                    : deviceDetailData?.networkType === 3
                    ? '3G'
                    : deviceDetailData?.networkType === 4
                    ? '4G'
                    : deviceDetailData?.networkType === 5
                    ? '5G'
                    : '--'
                }}
                <img
                  v-if="deviceDetailData?.signalQuality || deviceDetailData?.signalQuality === 0"
                  :src="
                    iconList[
                      `xinhao${
                        deviceDetailData?.signalQuality <= 0
                          ? ''
                          : Math.min(Math.ceil(deviceDetailData?.signalQuality / 6.2), 5)
                      }`
                    ]
                  "
                  class="iconXinhao"
                />
              </div>
            </div>
            <div class="text-item" style="width: 100%">
              <div class="text-item-left" style="width: 35%">数据更新时间：</div>
              <div class="text-item-center" style="width: 65%">
                {{ deviceDetailData?.time || '--' }}
              </div>
            </div>
            <div class="monitor-line"></div>
            <div style="display: flex; flex-wrap: wrap">
              <div class="text-item" style="width: 50%">
                <div>跌倒状态：</div>
                <div
                  :class="
                    deviceDetailData?.dump === true
                      ? ''
                      : deviceDetailData?.dump === false
                      ? 'text-#019E59'
                      : ''
                  "
                >
                  {{
                    deviceDetailData?.dump === true
                      ? '异常'
                      : deviceDetailData?.dump === false
                      ? '正常'
                      : '--'
                  }}
                </div>
              </div>
              <div class="text-item" style="width: 50%">
                <div>定位方式：</div>
                <div class="text-1010">
                  {{
                    deviceDetailData?.locateMode === 0
                      ? '无效定位'
                      : deviceDetailData?.locateMode === 1
                      ? '卫星定位'
                      : deviceDetailData?.locateMode === 2
                      ? '基站定位'
                      : '--'
                  }}
                </div>
              </div>
              <div class="text-item" style="width: 50%">
                <div>设备状态：</div>
                <div :class="deviceDetailData?.online ? 'text-#019E59' : 'text-1010'">
                  {{ deviceDetailData?.online ? '在线' : '离线' }}
                </div>
              </div>
              <div class="text-item" style="width: 50%">
                <div>灯状态：</div>
                <div :class="deviceDetailData?.light ? 'text-#019E59' : 'text-1010'">
                  {{ deviceDetailData?.light ? '开' : '关' }}
                </div>
              </div>
            </div>
            <div class="monitor-line"></div>
            <div class="text-item" style="width: 100%">
              <div class="text-item-left" style="width: 35%">本次开机时长：</div>
              <div class="text-item-center" style="width: 65%">
                {{ runningTimeDisplay || '--' }}
              </div>
            </div>
            <div class="text-item flex-between" style="width: 100%">
              <!-- @click="checkAddress(deviceDetailData)" -->
              <div class="text-item-left" style="width: 80%">
                实时定位：
                <span v-if="deviceDetailData?.coordinate?.longitude" style="color: #019e59">
                  {{
                    `${deviceDetailData?.coordinate?.longitude},${deviceDetailData?.coordinate?.latitude} `
                  }}
                </span>
                <span v-else>--</span>
              </div>
              <div
                v-if="deviceDetailData?.coordinate?.longitude"
                class="text-item-right"
                style="width: 20%; color: #019e59"
                @click="openAddress"
              >
                <span>查看</span>
              </div>
            </div>
            <div class="monitor-line"></div>

            <div class="text-item" style="width: 100%">
              <div style="width: 50%">
                灯休眠：<nut-switch
                  v-model="deviceDetailData.disabled"
                  @click="staticModeChange($event, 'disabled')"
                />
              </div>
              <div style="width: 50%">
                灯调试：
                <nut-switch
                  v-model="deviceDetailData.forced"
                  @click="staticModeChange($event, 'forced')"
                />
              </div>
            </div>
            <div class="monitor-line"></div>
            <div class="text-item flex-between" style="width: 100%">
              <div class="">
                累计杀虫数量：
                <span style="color: #5b5b5b"> {{ deviceDetailData?.killCount || 0 }}</span>
              </div>
              <div class="completeGreenDetail" @click="goStatistics">参数统计</div>
            </div>
          </div>
        </div>
        <!-- .....................设备控制 start..................... -->
        <div class="monitor-box-bg" style="margin-top: 10px">
          <div class="flex-between" style="align-items: center">
            <div class="monitor-title">设备控制</div>
            <div class="completeGreenDetail" @click="controlSave">保存</div>
          </div>
          <div class="monitor-line"></div>
          <div class="form-box-bg margin-bottom-24">
            <div class="text-item-left text-5B5B5B margin-top-24">
              <span>杀虫阈值设置</span>
            </div>
          </div>
          <nut-input
            v-model="formData.killThreshold"
            placeholder="请输入（正整数）"
            @blur="formData.killThreshold = formData.killThreshold.replace(/[^0-9]/g, '')"
          />

          <div class="monitor-line"></div>
          <div class="text-item flex-between" style="width: 100%; align-items: center">
            <div class="text-item-left" style="width: 20%">设备模式</div>
            <div class="text-item-right" style="width: 80%; color: #019e59; margin-top: 5px">
              <nut-radio-group v-model="formData.mode" direction="horizontal" color="#019E59 ">
                <nut-radio :label="1">自由模式</nut-radio>
                <nut-radio :label="2">休眠模式</nut-radio>
              </nut-radio-group>
            </div>
          </div>
          <div v-if="formData.mode === 2">
            <div class="monitor-line"></div>
            <div class="form-box-bg flex-between">
              <div class="text-item-left text-5B5B5B">
                <span style="color: #fe5050; margin-right: 5px">*</span>
                <span>休眠模式</span>
              </div>
              <img :src="addIcon" alt="" class="img-size-48" @click="timeControlAdd" />
            </div>
            <div
              v-for="(item, index) in timeList"
              :key="index"
              class="form-box-bg flex-between margin-top-24"
              style="align-items: center"
            >
              <div style="display: flex; align-items: center; width: 86%">
                <nut-input
                  v-model="item.start"
                  placeholder="开始时间"
                  readonly
                  @click-input="chooseInput(index, 'start')"
                >
                  <template #left>
                    <IconFont
                      name="clock"
                      size="16"
                      color="#AAADB2"
                      style="margin-left: -6px"
                    ></IconFont>
                  </template>
                </nut-input>
                <span style="margin: 0 6px"> ~ </span>
                <nut-input
                  v-model="item.end"
                  placeholder="结束时间"
                  readonly
                  @click-input="chooseInput(index, 'end')"
                >
                  <template #left>
                    <IconFont
                      name="clock"
                      size="16"
                      color="#AAADB2"
                      style="margin-left: -6px"
                    ></IconFont>
                  </template>
                </nut-input>
              </div>
              <img :src="delIcon" alt="" class="img-size-48" @click="deleteTime(index)" />
            </div>
          </div>
        </div>
        <!-- ................................设备控制 end.....................  -->
      </div>

      <div v-if="status === 1" class="monitor-box-bg">
        <div class="text-item flex-between">
          <div>客户</div>
          <div class="text-item-right">{{ customerData?.name || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>客户区域</div>
          <div class="text-item-right">{{ customerData.areaName || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人</div>
          <div class="text-item-right">{{ customerData.respPerson || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>联系人电话</div>
          <div class="text-item-right">{{ customerData.phone || '--' }}</div>
        </div>
        <div class="text-item flex-between">
          <div>通讯地址</div>
          <div class="text-item-right">{{ customerData.address || '--' }}</div>
        </div>
      </div>
      <div v-if="status === 2">
        <div class="monitor-box-bg">
          <div class="monitor-title">安装信息</div>
          <div class="monitor-line"></div>
          <div class="text-item flex-between">
            <div>工单</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.workOrderNo || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装人员</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installerName || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>手机号码</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.phoneNumber || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装时间</div>
            <div class="text-item-right">
              {{ afterSalesData?.installInfo?.installTime || '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>安装经纬度</div>
            <div class="text-item-right">
              <span v-if="afterSalesData?.installInfo?.coordinate">
                {{ afterSalesData?.installInfo?.coordinate?.longitude }},
                {{ afterSalesData?.installInfo?.coordinate?.latitude }}
              </span>
              <span v-else>--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div class="text-item-left">安装照片</div>
            <div class="text-item-right">
              <VImage
                v-for="item in afterSalesData?.installInfo?.images"
                :key="item"
                class="img"
                :src="item.url"
                :preview-src-list="afterSalesData?.installInfo?.images.map((item) => item.url)"
              />
              <span
                v-if="
                  !afterSalesData?.installInfo?.images ||
                  afterSalesData?.installInfo?.images?.length < 1
                "
                >暂无图片</span
              >
            </div>
          </div>
        </div>
        <div
          class="monitor-box-bg"
          v-if="afterSalesData?.workOrders && afterSalesData?.workOrders?.length > 0"
        >
          <div class="monitor-title">工单记录</div>
          <div
            v-for="(item, index) in afterSalesData?.workOrders"
            :key="index"
            style="position: relative"
          >
            <div class="monitor-line"></div>
            <div class="text-item">
              <div class="text-item-left">工单</div>
              <div class="text-item-center">{{ item.workOrderNo || '--' }}</div>
            </div>
            <div class="text-item">
              <div class="text-item-left">工单类型</div>
              <div class="text-item-center">
                {{ item.type === 1 ? '安装工单' : item.type === 2 ? '售后工单' : '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">故障原因</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.issueTypeName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交人</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.createByName || '--' }}
              </div>
            </div>
            <div class="text-item">
              <div class="text-item-left">提交时间</div>
              <div class="text-item-center" style="width: 80%">
                {{ item.createTime || '--' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="status === 3" class="mb-20">
        <div v-for="(item, index) in alarmItemsData" :key="index" class="monitor-box-bg">
          <div class="text-item flex-between">
            <div>告警项</div>
            <div class="text-item-right">{{ item.itemName || '--' }}</div>
          </div>
          <div class="text-item flex-between">
            <div>告警阈值</div>
            <div class="text-item-right">
              <span v-if="item.valueList && item.valueList.length > 0">
                {{
                  item.valueList.some((x) => x.value === item.value)
                    ? item.valueList.filter((x) => x.value === item.value)[0].label
                    : item.value
                }}
              </span>
              <span v-else
                >{{ item.value || '--' }}
                <span>{{ item.unit || '' }}</span>
              </span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>条件</div>
            <div class="text-item-right">
              {{ item.operator ? String(options[item.operator - 1]) : '--' }}
            </div>
          </div>
          <div class="text-item flex-between">
            <div>平台推送方式</div>
            <div class="text-item-right">
              <span v-if="item.managePushMethod && item.managePushMethod.includes(1)">后台</span>
              <span
                v-if="
                  item.managePushMethod &&
                  item.managePushMethod.includes(1) &&
                  item.managePushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.managePushMethod && item.managePushMethod.includes(2)">短信</span>
              <span v-if="!item.managePushMethod || item.managePushMethod.length === 0">--</span>
            </div>
          </div>
          <div class="text-item flex-between">
            <div>客户推送方式</div>
            <div class="text-item-right">
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(1)"
                >后台</span
              >
              <span
                v-if="
                  item.customerPushMethod &&
                  item.customerPushMethod.includes(1) &&
                  item.customerPushMethod.includes(2)
                "
                >、</span
              >
              <span v-if="item.customerPushMethod && item.customerPushMethod.includes(2)"
                >短信</span
              >
              <span v-if="!item.customerPushMethod || item.customerPushMethod.length === 0"
                >--</span
              >
            </div>
          </div>
          <div class="monitor-line"></div>
          <div class="flex justify-end items-center">
            <div class="completeGreen" @click="addAlarm(item)">编辑</div>
            <div class="deleteRed left-15" @click="deleteAlarm(item)">删除</div>
          </div>
        </div>
        <emput v-if="!alarmItemsData || alarmItemsData?.length < 1" message="暂无告警设置数据" />
      </div>
    </div>

    <nut-popup v-model:visible="timePopShow" position="bottom">
      <nut-date-picker
        v-model="val"
        type="hour-minute"
        :min-date="min"
        :max-date="max"
        :three-dimensional="false"
        @confirm="confirm"
      ></nut-date-picker>
    </nut-popup>
    <nut-dialog
      title="确定改变当前状态？"
      v-model:visible="visibleShow"
      :close-on-click-overlay="false"
      @cancel="onCancel"
      @ok="onOk"
    />
    <nut-dialog
      title="确定删除该告警设置？"
      v-model:visible="visibleShowAlarm"
      @cancel="onCancelAlarm"
      @ok="onOkAlarm"
    />

    <nut-popup v-model:visible="showPopup" position="bottom">
      <nut-picker
        v-model="choose"
        :columns="optionList"
        :title="option === 'itemId' ? '告警项' : option === 'value' ? '阈值' : '条件'"
        @confirm="chooseOption"
        @cancel=";(showPopup = false), (choose = []), (optionList = [])"
      />
    </nut-popup>
    <div v-if="status === 3" class="position-absolute w-100% bottom-0 flex justify-center">
      <div class="mt-2.5 position-relative w-80vw mb-4">
        <nut-button type="primary" size="large" @click="addAlarm"> 新增 </nut-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import Taro, { eventCenter, getCurrentInstance } from '@tarojs/taro'
import IconDevice from '../../../assets/device/<EMAIL>'
import UnIconDevice from '../../../assets/device/<EMAIL>'
import addIcon from '../../../assets/icon_zengjia.png'
import delIcon from '../../../assets/icon_shanchu.png'

import xinhao from '../../../assets/device/icon_xinhao.png'
import xinhao1 from '../../../assets/device/icon_xinhao_5.png'
import xinhao2 from '../../../assets/device/icon_xinhao_5.png'
import xinhao3 from '../../../assets/device/icon_xinhao_5.png'
import xinhao4 from '../../../assets/device/icon_xinhao_5.png'
import xinhao5 from '../../../assets/device/icon_xinhao_5.png'
import { IconFont } from '@nutui/icons-vue-taro'
import BaseInfoAPI from '../../../api/device/baseInfo'
import LampAPI from '../../../api/device/lamp'
import loadBigemap from '@/utils/bigemap'
import { classMap, DEFAULT_CENTER, deviceBottomIconMap, deviceIconMap2 } from '@/config/map'
import iconBottomAlarm from '@/assets/mapIcons/bottom/alarm.webp'
import iconBottomOffline from '@/assets/mapIcons/bottom/offline.webp'
import iconBottomCq from '@/assets/mapIcons/bottom/cq.webp'

import { markerAlarmMap, markerMap } from '@/config/map'

import gcoord from 'gcoord'

const deviceInfo = ref<any>({})

const timePopShow = ref(false)

const id = ref()

const iconList = ref({
  xinhao,
  xinhao1,
  xinhao2,
  xinhao3,
  xinhao4,
  xinhao5,
})

const mapContainer = ref<HTMLElement>()

let map: any
let BM: any

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const initMap = async () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [DEFAULT_CENTER.latitude, DEFAULT_CENTER.longitude],
    zoom: 16,
    minZoom: 5,
    zoomControl: false,
    maxZoom: 18,
  })

  drawMarker(baseData.value)
}

onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  // id.value = JSON.parse(decodeURIComponent(params.id));
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  // console.log(deviceInfo.value, 11111, Taro.getCurrentInstance().router)

  // getUserInfo();
  getDetailBase()
  // 监听从back页面返回的事件
  const router = getCurrentInstance()?.router
  if (router?.onShow) {
    eventCenter.on(router.onShow, () => {
      getDeviceAlarmItems()
    })
  }
})

const status = ref(0)
const tabList = ref([
  {
    title: '设备信息',
    paneKey: 0,
    count: '',
  },
  {
    title: '客户信息',
    paneKey: 1,
    count: '',
  },
  {
    title: '售后信息',
    paneKey: 2,
    count: '',
  },
  {
    title: '告警设置',
    paneKey: 3,
    count: '',
  },
])

const baseData = ref({
  version: '',
  iccd: '',
  coordinate: {},
  staticMode: false,
}) // 基础信息

const deviceDetailData = ref({
  staticMode: '',
  locateEnabled: '',
  disabled: '',
  forced: '',
  controlConfig: {
    normalMode: {
      startTime: '', // 起始时间
      endTime: '', // 截止时间
      interval: '', // 拍照间隔时间
    },
    sleepMode: {
      enabled: false, // 是否开启
      startDate: '', // 起始日期
      endDate: '', // 截至日期
      photoTime: '', // 拍照时间
    },
  },
}) // 设备信息
const customerData = ref({}) // 客户信息
const afterSalesData = ref() // 售后信息
const alarmItemsData = ref() // 告警设置信息
const showPopup = ref(false) // 条件下拉
const options = ref(['等于', '不等于', '大于', '小于', '大于等于', '小于等于', '之间']) // 条件列表
const choose = ref([]) // 通用Picker选项
const optionList = ref([]) // 通用选项列表
const option = ref('') // 通用选项item
const valueMap = ref<Record<number, any>>({}) // 告警项 valueList

// 获取设备基础信息
async function getDetailBase() {
  const res = await BaseInfoAPI.getBaseInfoId(deviceInfo.value.id)
  baseData.value = Object.assign({}, res)
  tabList.value = []
  // 如果设备有定位，标绘该设备
  if (res.coordinate) {
    loadBigeMap()
  }
  if (res) {
    if (res?.internetEnable) {
      tabList.value.push({
        title: '设备信息',
        paneKey: 0,
        count: '',
      })
    }
    if (res?.customerId) {
      tabList.value.push({
        title: '客户信息',
        paneKey: 1,
        count: '',
      })
    }
    if (res?.installed) {
      tabList.value.push({
        title: '售后信息',
        paneKey: 2,
        count: '',
      })
    }
    if (res?.internetEnable) {
      tabList.value.push({
        title: '告警设置',
        paneKey: 3,
        count: '',
      })
    }
    if (res?.internetEnable) {
      getDeviceInfo()
      // getDeviceAlarmSetting()
      getDeviceAlarmItems()
    }
    if (res?.customerId) {
      getDeviceCustomer()
    }
    if (res?.installed) {
      getDeviceAfter()
    }
  }
  status.value = tabList.value && tabList.value.length > 0 ? tabList.value[0].paneKey : 0
}

/**
 * 跳转高德地图
 * */
function openGMap() {
  const { longitude: lonEnd, latitude: latEnd } = baseData.value.coordinate
  const destination = baseData.value.address
  window.location.href = `https://uri.amap.com/marker?position=${lonEnd},${latEnd}&name=${destination}&src=mypage&coordinate=wgs84&callnative=1`
}

function drawMarker(item) {
  // 截取name最后三位
  const num = item.serialNum.slice(-3)

  let className = classMap[item.type]
  let bottomIcon = deviceBottomIconMap[item.type]
  if (item.isAlarm) {
    bottomIcon = iconBottomAlarm
    className += ' alarm'
  } else if (item.status === 0) {
    bottomIcon = iconBottomOffline
    className += ' offline'
  } else if (item.deviceType === 1 && !item.isLight) {
    bottomIcon = iconBottomCq
    className += ' close'
  }

  const deviceIcon = BM.divIcon({
    html: `
        <header class="header flex items-center w-58Px h-28Px rounded-6Px">
          <img src="${deviceIconMap2[item.type]}" class="w-16Px h-18Px object-fit ml-6Px mr-4Px"/>
          <span text="14Px white">${num}</span>
        </header>
        <footer class="flex justify-center mt-2Px">
          <div style="background: url(${bottomIcon}) center / 100% 100% no-repeat" class="footerImage w-16Px h-8Px"/>
        </footer>
        `,
    iconSize: [58, 38],
    iconAnchor: [29, 38],
    className: `deviceIcon ${className}`,
  })
  const { latitude, longitude } = item.coordinate
  const wgs84Coord = gcoord.transform([longitude, latitude], gcoord.WGS84, gcoord.BD09)
  const marker = BM.marker([wgs84Coord[1], wgs84Coord[0]], { icon: deviceIcon })

  map.flyTo([wgs84Coord[1], wgs84Coord[0]], 16)

  marker.addTo(map)

  // 为每个marker添加点击事件
  // marker.on('click', (e: any) => {
  //   e.originalEvent.stopPropagation() // 阻止事件冒泡
  //
  //   markerClick(marker)
  // })
}

// type  设备类型 1 智慧杀虫灯 2 墒情气象一体机 3 虫情分析仪 4 苗情监控
function getDeviceInfo() {
  BaseInfoAPI.getDeviceInfo(deviceInfo.value.id, 1).then((res) => {
    if (res) {
      deviceDetailData.value = res
      formData.value.killThreshold = res?.controlConfig?.killThreshold || ''
      formData.value.mode = res?.controlConfig?.mode || 1
      timeList.value = []
      if (res?.controlConfig?.workHours?.length > 0) {
        res?.controlConfig?.workHours.forEach((item) => {
          timeList.value.push({
            start: item[0],
            end: item[1],
          })
        })
      }
    } else {
      deviceDetailData.value = {
        staticMode: '',
        locateEnabled: '',
        disabled: '',
        forced: '',
        controlConfig: {
          normalMode: {
            startTime: '', // 起始时间
            endTime: '', // 截止时间
            interval: '', // 拍照间隔时间
          },
          sleepMode: {
            enabled: false, // 是否开启
            startDate: '', // 起始日期
            endDate: '', // 截至日期
            photoTime: '', // 拍照时间
          },
        },
      }
    }
  })
}

function getDeviceCustomer() {
  BaseInfoAPI.getCustomerInfo(deviceInfo.value.id).then((res) => {
    customerData.value = res || {}
  })
}

function getDeviceAfter() {
  BaseInfoAPI.getAfterSaleInfo(deviceInfo.value.id).then((res) => {
    afterSalesData.value = res || {}
  })
}

const visibleShow = ref(false)
const changeType = ref()

// 开启静态模式
function staticModeChange(val, type) {
  // console.log(val, 'val', type, baseData.value.staticMode)
  changeType.value = type
  visibleShow.value = true
}
const onCancel = () => {
  // console.log('event cancel')
  if (changeType.value === 'static-mode') {
    baseData.value.staticMode = !baseData.value.staticMode
  } else if (changeType.value === 'disabled') {
    deviceDetailData.value.disabled = !deviceDetailData.value.disabled
  } else if (changeType.value === 'forced') {
    deviceDetailData.value.forced = !deviceDetailData.value.forced
  }
  visibleShow.value = false
}

const onOk = () => {
  const data = {
    id: deviceInfo.value.id,
    enabled: baseData.value.staticMode,
  }
  if (changeType.value === 'static-mode') {
    data.enabled = baseData.value.staticMode
  } else if (changeType.value === 'disabled') {
    data.enabled = deviceDetailData.value.disabled
  } else if (changeType.value === 'forced') {
    data.enabled = deviceDetailData.value.forced
  }
  LampAPI.changeStatus(changeType.value, data)
    .then((res) => {
      if (changeType.value === 'static-mode') {
        getDetailBase()
      } else {
        getDeviceInfo()
      }
    })
    .catch(() => {
      if (changeType.value === 'static-mode') {
        baseData.value.staticMode = !baseData.value.staticMode
      } else if (changeType.value === 'disabled') {
        deviceDetailData.value.disabled = !deviceDetailData.value.disabled
      } else if (changeType.value === 'forced') {
        deviceDetailData.value.forced = !deviceDetailData.value.forced
      }
      Taro.showToast({
        title: '操作失败',
        icon: 'none',
      })
    })
    .finally(() => {})
  console.log('event ok')
}

//  ........................ 休眠模式start..................
const timeList = ref([{ start: '', end: '' }])

// 添加时间段
function timeControlAdd() {
  timeList.value.push({ start: '', end: '' })
}

// 删除时间
function deleteTime(index) {
  timeList.value.splice(index, 1)
}

const timeIndex = ref()
const timeType = ref()
function chooseInput(index, type) {
  timeIndex.value = index
  timeType.value = type
  timePopShow.value = true
}

function controlSave() {
  const list = []
  let flag = false
  if (formData.value.mode == 2) {
    if (timeList.value && timeList.value.length > 0) {
      timeList.value.forEach((item) => {
        if (!item.start || !item.end) {
          flag = true
        }
        list.push([item.start, item.end])
      })
    } else {
      Taro.showToast({
        title: '请至少添加一个时间段',
        icon: 'none',
      })
      return
    }
  }
  if (flag) {
    Taro.showToast({
      title: '请填写完整时间',
      icon: 'none',
    })
    return
  }
  console.log(list, 'list')
  const data = {
    id: deviceInfo.value.id,
    killThreshold: formData.value.killThreshold,
    mode: formData.value.mode,
    workHours: [...list],
  }
  LampAPI.deviceControlSave(data).then((res) => {
    Taro.showToast({
      title: '保存成功',
      icon: 'none',
    })
    setTimeout(() => {
      getDeviceInfo()
      // getDetailBase()
    }, 500)
  })
}

// ..........................休眠模式end.....................
const min = new Date(2020, 1, 1, 10, 40)
const max = new Date(2030, 1, 1, 23, 29)
const val = ref(new Date(2020, 1, 1, 15, 30))
const confirm = ({ selectedValue }) => {
  console.log(selectedValue)
  if (selectedValue) {
    if (timeType.value === 'start') {
      const end = timeList.value[timeIndex.value].end
      if (end) {
        const startHours = parseInt(selectedValue[0], 10)
        const startMinutes = parseInt(selectedValue[1], 10)
        const endHours = parseInt(end.split(':')[0], 10)
        const endMinutes = parseInt(end.split(':')[1], 10)
        if (startHours < endHours || (startHours === endHours && startMinutes <= endMinutes)) {
          timeList.value[timeIndex.value].start = `${selectedValue[0]}:${selectedValue[1]}`
        } else {
          Taro.showToast({
            title: '结束时间必须大于开始时间',
            icon: 'none',
          })
          return
        }
      } else {
        timeList.value[timeIndex.value].start = `${selectedValue[0]}:${selectedValue[1]}`
      }
    } else {
      const start = timeList.value[timeIndex.value].start
      if (start) {
        const startHours = parseInt(start.split(':')[0], 10)
        const startMinutes = parseInt(start.split(':')[1], 10)
        const endHours = parseInt(selectedValue[0], 10)
        const endMinutes = parseInt(selectedValue[1], 10)
        if (endHours > startHours || (endHours === startHours && endMinutes >= startMinutes)) {
          timeList.value[timeIndex.value].end = `${selectedValue[0]}:${selectedValue[1]}`
        } else {
          Taro.showToast({
            title: '结束时间必须大于开始时间',
            icon: 'none',
          })
          return
        }
      } else {
        timeList.value[timeIndex.value].end = `${selectedValue[0]}:${selectedValue[1]}`
      }
    }
  }

  timePopShow.value = false
}

const goBack = () => {
  Taro.navigateBack()
}

// ...........................参数统计....................
function goStatistics() {
  const data = {
    staticConfig: baseData?.value?.misc?.staticConfig,
    staticMode: baseData?.value?.staticMode,
    deviceId: deviceInfo.value.id,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/lamp/statistics??data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

const formData = ref({
  killThreshold: '',
  mode: 1,
  workHours: [],
})

// 定义一个计算属性将分钟转换为天、小时和分钟
const runningTimeDisplay = computed(() => {
  const minutes = deviceDetailData?.value.runningTime
  if (!minutes || minutes == 0) return '0'

  const days = Math.floor(minutes / (24 * 60))
  const remainingMinutesAfterDays = minutes % (24 * 60)
  const hours = Math.floor(remainingMinutesAfterDays / 60)
  const finalMinutes = remainingMinutesAfterDays % 60

  let display = ''
  if (days > 0) {
    display += `${days}天`
  }

  if (hours > 0) {
    display += `${hours}小时`
  }

  if (finalMinutes > 0 || (!days && !hours)) {
    display += `${finalMinutes}分钟`
  }

  return display
})

// 查看定位
function openAddress() {
  const data = {
    latitude: deviceDetailData.value?.coordinate?.latitude,
    longitude: deviceDetailData.value?.coordinate?.longitude,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/lamp/map??data=${encodeURIComponent(JSON.stringify(data))}`,
  })
}

// .............................告警设置 start..........................

/**
 * 删除告警项行
 */
const alarmIndex = ref()

// 删除告警设置
function deleteAlarm(item) {
  alarmIndex.value = item.id
  visibleShowAlarm.value = true
}

const visibleShowAlarm = ref(false)

const onCancelAlarm = () => {
  visibleShowAlarm.value = false
}

const onOkAlarm = () => {
  // alarmItemsData.value.splice(alarmIndex.value, 1)
  const data = {
    deviceId: deviceInfo.value.id,
    ids: [alarmIndex.value],
  }
  BaseInfoAPI.deleteConfigItem(data).then((res) => {
    Taro.showToast({
      title: '删除成功',
      icon: 'success',
    })
    getDeviceAlarmItems()
  })
}

/**
 * 获取当前设备配置
 */
function getDeviceAlarmItems() {
  BaseInfoAPI.getDeviceAlarmItems(deviceInfo.value.id).then((res) => {
    alarmItemsData.value = res || []
    alarmItemsData.value.forEach((val) => {
      val.options = val.supportOperators.map((val) => {
        return { value: val, text: options.value[val - 1] }
      })
      valueMap.value[val.itemId]?.forEach((item) => {
        if (val.value == item.value) {
          val.valueName = item.text
        }
      })
    })
  })
}
// 新增告警
function addAlarm(row?) {
  const data = {
    innerModel: baseData.value.innerModel,
    deviceId: deviceInfo.value.id,
    deviceType: 1,
    ...row,
  }
  Taro.navigateTo({
    url: `/pages/deviceManagement/component/addAlarm?data=${encodeURIComponent(
      JSON.stringify(data)
    )}`,
  })
}

// .............................告警设置 end............................
</script>

<style scoped lang="scss">
.batch-item {
  width: 690px;
  padding: 30px 30px;
  box-sizing: border-box;
  margin: 30px auto 0;
  border-radius: 20px;
  background: #fff;
  display: flex;
  .batch-item-left {
    margin-right: 20px;
    img {
      width: 60px;
      height: 60px;
    }
  }
  .batch-item-right {
    flex: 1;
    font-size: 28px;
    color: #909090;
    .name {
      font-size: 32px;
      color: #000;
      font-weight: bold;
    }
    .batch {
      margin: 5px 0;
    }
    .count {
      display: flex;
      justify-content: space-between;
    }
  }
}
.label-text {
  font-size: 30px;
  padding-left: 30px;
}
.code {
  margin-top: 50px;
}
:deep(.nut-cell-group__wrap) {
  box-shadow: unset !important;
  background-color: transparent;
}
:deep(.nut-form-item__body) {
  .nut-input-box {
    height: 50px !important;
    .input-text {
      font-size: 30px !important;
    }
  }
}
:deep(.nut-form-item) {
  .nut-input-inner {
    height: 78px;
    padding: 0 30px;
    // border: 1px solid #d4d4d4;
    border-radius: 4px;
  }
}
:deep(.nut-form-item.error.line::before) {
  border: none;
}
:deep(.nut-form-item__body__tips) {
  font-size: 24px;
  margin-top: 22px;
}
.nut-form-item {
  width: 630px;
  margin: 0 auto 0;
  border-radius: 20px;
}
.bottom-button {
  position: fixed;
  left: 30px;
  bottom: 60px;
  display: flex;
  justify-content: space-between;
  width: 690px;
  margin: 0 auto;
  .nut-button {
    width: 330px;
  }
}

//
.monitor-title {
  font-weight: bold;
  font-size: 32px;
  color: #101010;
}
.monitor-line {
  width: 100%;
  // height: 0px;
  border-bottom: 1px solid #d4d4d4;
  margin: 30px 0;
}

// 文字左右展示
.monitor-box-bg {
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
  .flex-between {
    display: flex;
    justify-content: space-between;
  }
  .switch-text {
    font-weight: 400;
    font-size: 28px;
    color: #909090;
    margin-right: 18px;
  }
  .text-item {
    display: flex;
    font-weight: 400;
    font-size: 28px;
    color: #5b5b5b;
    margin: 20px 0;
    .text-item-right {
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      width: 450px;
      text-align: right;
      word-break: break-all;
      overflow-wrap: break-word; /* 允许长单词或 URL 地址换行到下一行 */
      // display: flex;
      // justify-content: flex-end;
      // flex-wrap: wrap;
    }
    .img {
      width: 82px;
      height: 82px;
      border-radius: 10px 10px 10px 10px;
      margin-left: 10px;
      margin-bottom: 10px;
    }
    .text-item-left {
      width: 140px;
    }
    .text-item-center {
      width: 380px;
      font-weight: 400;
      font-size: 28px;
      color: #101010;
      word-break: break-all;
      overflow-wrap: break-word;
    }
  }
  .statusGd {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0px;
    top: 40px;
  }
}
.text-1010 {
  font-weight: 400;
  font-size: 28px;
  color: #101010;
}
.text-5B5B5B {
  font-weight: 400;
  font-size: 28px;
  color: #5b5b5b;
}
.text-22 {
  font-size: 22px;
}
.margin-top-24 {
  margin-top: 24px;
}
.margin-bottom-24 {
  margin-bottom: 24px;
}
.img-size-48 {
  width: 48px;
  height: 48px;
}
// input 框
.nut-input {
  width: 630px;
  height: 78px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d4d4d4;
  padding-left: 30px;
}
.iconXinhao {
  width: 28px;
  height: 19px;
  margin-left: 8px;
}
.border-bottom-20 {
  border-radius: 0 0 20px 20px;
}
</style>
