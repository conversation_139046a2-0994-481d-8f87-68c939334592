{"name": "myApp", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "vue3-NutUI4", "typescript": true, "css": "Sass", "framework": "Vue3"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "lint": "eslint --ext .jsx,.ts,.tsx,.vue src", "lint:fix": "eslint --ext .jsx,.ts,.tsx,.vue src --fix"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-taro": "^4.3.0", "@tarojs/components": "3.6.35", "@tarojs/helper": "3.6.35", "@tarojs/plugin-framework-vue3": "3.6.35", "@tarojs/plugin-html": "3.6.35", "@tarojs/plugin-platform-alipay": "3.6.35", "@tarojs/plugin-platform-h5": "3.6.35", "@tarojs/plugin-platform-jd": "3.6.35", "@tarojs/plugin-platform-qq": "3.6.35", "@tarojs/plugin-platform-swan": "3.6.35", "@tarojs/plugin-platform-tt": "3.6.35", "@tarojs/plugin-platform-weapp": "3.6.35", "@tarojs/runtime": "3.6.35", "@tarojs/shared": "3.6.35", "@tarojs/taro": "3.6.35", "@vueuse/core": "^13.0.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "gcoord": "^1.0.7", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "minio-js": "^1.0.7", "pinia": "^3.0.2", "postcss": "^8.5.3", "vconsole": "^3.15.1", "vue": "^3.5.13", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@babel/core": "^7.8.0", "@nutui/auto-import-resolver": "^1.0.0", "@tarojs/cli": "3.6.35", "@tarojs/taro-loader": "3.6.35", "@tarojs/webpack5-runner": "3.6.35", "@types/node": "^18.15.11", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "@unocss/webpack": "^0.65.2", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.2.40", "@vue/eslint-config-prettier": "^6.0.0", "@vue/typescript": "^1.8.20", "babel-preset-taro": "3.6.35", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.6.35", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsonc": "^2.20.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^8.0.0", "prettier": "2.8.3", "style-loader": "1.3.0", "stylelint": "9.3.0", "taro-axios": "^1.1.1", "ts-node": "^10.9.1", "typescript": "^4.6.4", "unocss": "^0.65.2", "unocss-applet": "^0.9.0", "unplugin-vue-components": "^0.26.0", "vue-loader": "^17.0.0", "webpack": "^5.78.0", "webpack-virtual-modules": "^0.6.2"}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a"}