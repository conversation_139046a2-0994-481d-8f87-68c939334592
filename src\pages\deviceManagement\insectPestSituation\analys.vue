<template>
  <CustomNavTitle title="虫情分析" />

  <div class="page-content">
    <div class="content">
      <CustomTabs
        class="mt-2.5"
        style="border-top-left-radius: 4px; border-top-right-radius: 4px"
        v-model="status"
        :tab-list="tabList"
        @tab-click="tabClick"
      />
      <div
        class="monitor-box-bg"
        style="border-top-left-radius: 0 !important; border-top-right-radius: 0 !important"
      >
        <div class="text-2.7 text-#707070">日期</div>
        <div v-show="status == 0" class="flex flex-row justify-between">
          <div class="w-45%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerDayPopup('start', queryDate.start)"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryDate.start ? '#101010' : '#707070',
                }"
              >
                {{ queryDate.start || '开始时间' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
          <div class="w-45%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerDayPopup('end', queryDate.end)"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryDate.start ? '#101010' : '#707070',
                }"
              >
                {{ queryDate.end || '结束时间' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
        </div>
        <div v-show="status == 1" class="flex flex-row justify-between">
          <div class="w-37%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerYearPopup"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryWeek.year ? '#101010' : '#707070',
                }"
              >
                {{ queryWeek.year || '年' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
          <div class="w-58%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerWeekPopup"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryWeek.week ? '#101010' : '#707070',
                }"
              >
                {{ queryWeek.week || '周' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
        </div>
        <div v-show="status === 2" class="flex flex-row justify-between">
          <div class="w-80%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerMonthPopup"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryMonth ? '#101010' : '#707070',
                }"
              >
                {{ queryMonth || '请选择年月' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
        </div>
        <div v-show="status == 3" class="flex flex-row justify-between">
          <div class="w-80%">
            <div
              class="flex flex-row items-center h-5 mt-2.5"
              p="x-2 y-1"
              :style="{
                borderWidth: '1px',
                borderRadius: '4px',
                borderStyle: 'solid',
                borderColor: '#D4D4D4',
              }"
              @click="handlerYearPopup"
            >
              <div
                class="text-2.5"
                :style="{
                  color: queryYear ? '#101010' : '#707070',
                }"
              >
                {{ queryYear || '年' }}
              </div>
              <IconFont
                :style="{ marginLeft: 'auto', color: '#DFDFDF' }"
                size="12"
                name="arrow-down"
              ></IconFont>
            </div>
          </div>
        </div>
        <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div>
        <div class="mt-2.5 flex flex-row items-center">
          <div class="bg-#019E59 w-0.7 h-2.3 mr-1"></div>
          <div class="text-2.7 text-#101010">虫的数量统计</div>
        </div>
        <!-- {{ lineValue  }} -->
        <Chart
          v-if="lineOption && lineValue?.length > 0"
          clear-when-update
          :option="lineOption"
          class="chartsDiv"
        />
        <!-- <div v-if="!lineValue" class="charts-nodata">暂无数据</div> -->
        <emput v-if="!lineValue || lineValue?.length < 1" message="暂无数据" />
        <!-- <nut-empty v-if="!lineValue" description="暂无数据" /> -->
        <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div>
        <div class="mt-2.5 flex flex-row items-center">
          <div class="bg-#019E59 w-0.7 h-2.3 mr-1"></div>
          <div class="text-2.7 text-#101010">虫的占比</div>
        </div>
        <Chart
          v-if="pieValue && pieValue.length > 0"
          ref="chartRef"
          :option="pieOption"
          class="chartsDiv"
        />
        <!-- <div v-if="!pieValue || pieValue?.length < 1" class="charts-nodata">暂无数据</div>-->
        <!-- <nut-empty v-if="!pieValue || pieValue?.length < 1" description="暂无数据" /> -->
        <emput v-if="!pieValue || pieValue?.length < 1" message="暂无数据" />
        <!-- <div class="w-100% bg-#DFDFDF h-0.23 mt-2.5"></div> -->
        <!-- <div class="mt-2.5 flex flex-row items-center">
          <div class="bg-#019E59 w-0.7 h-2.3 mr-1"></div>
          <div class="text-2.7 text-#101010">图片详情</div>
        </div> -->
        <!-- <scroll-view
          v-if="insectPestLog"
          scroll-y
          :lowerThreshold="200"
          :refresher-enabled="true"
          :refresher-triggered="refreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="loadMore"
        >
          <div class="flex flex-col mt-2.5" v-for="(item, index) in insectPestLog" :key="item.id">
            <div v-if="index != 0" class="bg-#DFDFDF h-0.23 mt-2.5"></div>
            <div class="flex flex-row justify-between mt-2.5">
              <div class="flex flex-row justify-between">
                <div class="text-2.7 text-#101010">虫的数量：</div>
                <div class="text-2.7 text-#101010">{{ item.insectTotal }}</div>
              </div>
              <div class="text-2.7 text-#707070">{{ item.time }}</div>
            </div>
            <div class="flex">
              <div class="text-center">
                <VImage
                  class="w-20 h-20 mt-2.5"
                  :src="item.thumbnails"
                  :preview-src-list="[item.rawImage]"
                />
                <div class="text-2.7 text-#101010">原始图片</div>
              </div>
              <div class="text-center">
                <VImage
                  class="w-20 h-20 mt-2.5"
                  :src="item.thumbnails"
                  :preview-src-list="[item.rawImage]"
                />
                <div class="text-2.7 text-#101010">分析图片</div>
              </div>
            </div>


          </div> -->

        <!-- <nut-empty v-if="insectPestLog.length === 0" description="暂无虫情图片数据" /> -->
        <!-- <div v-if="loading" class="loading-more">
            <span>加载中...</span>
          </div>
        </scroll-view> -->
      </div>
    </div>
    <nut-image-preview :show="showPreview" :images="imgData" @close="hideFn" />
    <nut-popup v-model:visible="showDay" position="bottom" :close-on-click-overlay="false">
      <nut-date-picker
        v-model="chooseDay"
        :min-date="minDate"
        :max-date="maxDate"
        :three-dimensional="false"
        @confirm="confirmDay"
        @cancel=";(showDay = false), (chooseDay = '')"
      ></nut-date-picker>
    </nut-popup>
    <nut-popup v-model:visible="showWeek" position="bottom" :close-on-click-overlay="false">
      <nut-picker
        v-model="chooseWeek"
        :columns="weekColumns"
        title="选择周"
        @confirm="confirmWeek"
        @cancel=";(showWeek = false), (chooseWeek = '')"
      ></nut-picker>
    </nut-popup>
    <nut-popup v-model:visible="showYear" position="bottom" :close-on-click-overlay="false">
      <nut-picker
        v-model="chooseYear"
        :columns="yearColumns"
        title="选择年"
        @confirm="confirmYear"
        @cancel=";(showYear = false), (chooseYear = '')"
      ></nut-picker>
    </nut-popup>
    <nut-popup v-model:visible="showMonth" position="bottom" :close-on-click-overlay="false">
      <nut-date-picker
        v-model="chooseMonth"
        :min-date="minDate"
        :max-date="maxDate"
        type="year-month"
        :three-dimensional="false"
        @confirm="confirmMonth"
        @cancel=";(showMonth = false), (chooseMonth = '')"
      ></nut-date-picker>
    </nut-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { IconFont } from '@nutui/icons-vue-taro'
import CustomTabs from '@/components/CustomTabs.vue'
import InsectPestAPI from '@/api/device/insectPest'
import Taro from '@tarojs/taro'
import type { EChartsOption } from 'echarts'
import Chart from '@/components/Chart/Chart.vue'
import * as echarts from 'echarts'
import { generateWeekOptions, generateYearOptions } from '@/utils/dateUtil'
import dayjs from 'dayjs'
const status = ref(0)
const tabList = ref([
  {
    title: '日',
    paneKey: 0,
  },
  {
    title: '周',
    paneKey: 1,
  },
  {
    title: '月',
    paneKey: 2,
  },
  {
    title: '年',
    paneKey: 3,
  },
])
const queryDate = ref({
  start: dayjs().format('YYYY-MM-DD'),
  end: dayjs().format('YYYY-MM-DD'),
}) // 日期筛选
const showDay = ref(false) // 日选择
const minDate = ref()
const maxDate = ref()
const chooseDay = ref() // 选中的日期
const chooseOption = ref() // 选中对象
const deviceId = ref() // 设备id
const pieValue = ref() // 饼图参数
const lineValue = ref() // 折线图参数
const pieOption = ref() // 饼图配置
const lineOption = ref() // 曲线图配置
const colorList = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
]
const pageNum = ref(1)
const pageSize = ref(10)
const insectPestLog = ref()
const hasMore = ref(true)
const loading = ref(false)
const refreshing = ref(false)
const showPreview = ref(false)
const imgData = ref([])
const showWeek = ref(false) //  周下拉
const chooseWeek = ref() // 选中的周
const weekColumns = ref<Array<Object>>() //  周下拉
const queryWeek = ref({
  year: new Date().getFullYear(),
  week: '',
}) // 周参数
const chooseYear = ref() // 选中年
const showYear = ref(false) // 年下拉
const yearColumns = ref<Array<Object>>() // 年列表
const queryYear = ref(new Date().getFullYear()) // 筛选年
const chooseMonth = ref() // 选中月
const showMonth = ref(false) // 月下拉
const queryMonth = ref() // 月筛选

/**
 * tab点击
 */
function tabClick(item) {
  switch (item) {
    case 0:
      queryDate.value = {
        start: dayjs().format('YYYY-MM-DD'),
        end: dayjs().format('YYYY-MM-DD'),
      }
      break
    case 1:
      weekColumns.value = generateWeekOptions(new Date().getFullYear())
      // 获取当前日期
      const currentDate = new Date()
      // 假设 generateWeekOptions 生成的周列表包含 start 和 end 字段，用于表示周的开始和结束日期
      const currentWeekIndex = weekColumns.value.findIndex((item) => {
        const startDate = new Date(item.start)
        const endDate = new Date(item.end)
        return currentDate >= startDate && currentDate <= endDate
      })

      // 若当前没有选中周，则默认使用第一个周的信息
      const targetIndex = currentWeekIndex !== -1 ? currentWeekIndex : 0
      queryWeek.value = {
        year: new Date().getFullYear(),
        week: weekColumns.value[targetIndex].text,
      }
      queryDate.value = {
        start: weekColumns.value[targetIndex].start,
        end: weekColumns.value[targetIndex].end,
      }
      break
    case 2:
      let now = new Date()
      queryMonth.value = dayjs().format('YYYY-MM')
      // 为了获取当前月的日期范围，我们直接使用 now 变量中的年份和月份信息
      // 开始日期设置为当前月的第一天，结束日期设置为当前月的最后一天
      queryDate.value = {
        start: dayjs().year(now.getFullYear()).month(now.getMonth()).date(1).format('YYYY-MM-DD'),
        end: dayjs()
          .year(now.getFullYear())
          .month(now.getMonth())
          .endOf('month')
          .format('YYYY-MM-DD'),
      }
      break
    case 3:
      yearColumns.value = generateYearOptions()
      queryYear.value = new Date().getFullYear()
      queryDate.value = {
        start: yearColumns.value[yearColumns.value.length - 1].start,
        end: yearColumns.value[yearColumns.value.length - 1].end,
      }
    default:
      break
  }
  getIsa()
  // getRunLog()
}

/**
 * 选中月
 */
function confirmMonth({ selectedValue, selectedOptions }) {
  console.log(selectedValue, 'seeeeeeeeee')

  showMonth.value = false
  queryMonth.value = `${selectedValue[0]}-${selectedValue[1]}`

  queryDate.value = {
    start: dayjs()
      .year(selectedValue[0])
      .month(selectedValue[1] - 1)
      .date(1)
      .format('YYYY-MM-DD'),
    end: dayjs()
      .year(selectedValue[0])
      .month(selectedValue[1] - 1)
      .endOf('month')
      .format('YYYY-MM-DD'),
  }
  getIsa()
  // getRunLog()
}

/**
 * 打开月筛洗
 */
function handlerMonthPopup() {
  const list = queryMonth.value.split('-')
  chooseMonth.value = new Date().setMonth(list[0], list[1] - 1)
  showMonth.value = true
}

/**
 * 打开年筛选
 */
function handlerYearPopup() {
  if (status.value === 1) {
    // queryWeek.value.year = chooseYear.value
  }
  showYear.value = true
}
/**
 * 选中年
 */
function confirmYear({ selectedValue, selectedOptions }) {
  showYear.value = false
  if (status.value === 1) {
    queryWeek.value.week = ''
    chooseWeek.value = ''
    weekColumns.value = generateWeekOptions(selectedValue[0])
    queryWeek.value.year = selectedValue[0]
  } else if (status.value === 3) {
    queryYear.value = selectedValue[0]
    queryDate.value = {
      start: selectedOptions[0].start,
      end: selectedOptions[0].end,
    }
    getIsa()
    // getRunLog()
  }
}

/**
 * 打开周筛选器
 */
function handlerWeekPopup() {
  showWeek.value = true
}

/**
 * 选中周
 */
function confirmWeek({ selectedValue, selectedOptions }) {
  queryWeek.value.week = selectedOptions[0].text
  queryDate.value = {
    start: selectedOptions[0].start,
    end: selectedOptions[0].end,
  }
  getIsa()
  // getRunLog()
  showWeek.value = false
}

/**
 * 开启图片预览
 */
function showFn(item) {
  imgData.value = item
  showPreview.value = true
}
/**
 * 关闭图片预览
 */
function hideFn() {
  showPreview.value = false
}

/**
 * 获取虫情运行日志
 */
async function getRunLog() {
  const data = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    startTime: queryDate.value.start + ' 00:00:00',
    endTime: queryDate.value.end + ' 23:59:59',
  }
  loading.value = true
  InsectPestAPI.insectRunPage(data)
    .then((res) => {
      const { records = [], total = 0 } = res || {}
      insectPestLog.value = pageNum.value === 1 ? records : [...insectPestLog.value, ...records]
      hasMore.value = insectPestLog.value.length < total
    })
    .finally(() => {
      loading.value = false
    })
}
/**
 * 获取虫情图表
 */
function getIsa() {
  let xUnit = ''
  switch (status.value) {
    case 0:
      xUnit = 'day'
      break
    case 1:
      xUnit = 'day'
      break
    case 2:
      xUnit = 'month'
      break
    case 3:
      xUnit = 'year'
      break
    default:
      break
  }
  const data = {
    startDate: queryDate.value.start,
    endDate: queryDate.value.end,
  }
  InsectPestAPI.insectPestIsa(deviceId.value, xUnit, data)
    .then((res) => {
      if (res) {
        pieValue.value = res.pie
        if (pieValue.value.length > 0) {
          setPieOption()
        }
        lineValue.value = res?.lineCharts
        if (lineValue.value.length > 0) {
          let series = new Array<Object>()
          let xValue = new Set<String>()
          let index = 0
          // Object.entries(lineValue.value).forEach(([key, value]) => {
          lineValue.value.forEach((item) => {
            let yValue = new Array<Number>()
            item?.line?.forEach((val) => {
              xValue.add(val.x)
              yValue.push(val.y)
            })
            series.push({
              name: item.label,
              type: 'line',
              smooth: true,
              symbolSize: '10',
              symbol: 'emptyCircle',
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0, // 垂直渐变
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: `${colorList[index]}3A`, // 顶部透明度 0.8 (HEX格式 CC=204 → 204/255≈0.8)
                    },
                    {
                      offset: 1,
                      color: `${colorList[index]}0A`, // 底部透明度 0.1 (HEX格式 1A=26 → 26/255≈0.1)
                    },
                  ],
                },
              },
              color: colorList[index],
              data: yValue,
              // symbolOffset:
              //   item?.line.length === 1 ? [4 * letArray(item?.line[0]?.y, index), 0] : [0, 0], // 设置点的水平偏移和垂直偏移
            })
            index++
            if (index === 9) {
              index = 0
            }
          })
          setLineOption(series, xValue)
        } else {
          lineValue.value = []
          lineOption.value = {}
        }
      } else {
        pieValue.value = []
        lineValue.value = []
      }
    })
    .catch((err) => {
      pieValue.value = []
      lineValue.value = []
    })
}

function setLineOption(series, legends) {
  lineOption.value = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const axisValue = params[0]?.axisValue || '' // 获取第一个参数的 axisValue
        const str = params
          .map((item) => {
            const value = item.value
            const seriesName = item.seriesName
            const unit = '只' // 根据需要设置单位
            return `${seriesName}: ${value} ${unit}`
          })
          .join('<br/>')
        return `<div style="text-align: left;font-size:12px">${axisValue}<br/>${str}</div>`
      },
    },
    legend: {
      icon: 'circle',
      type: 'scroll',
      orient: 'horizontal',
      pageIconColor: '#5470c6',
      pageTextStyle: {
        color: '#707070',
      },
      left: 'center',
      bottom: 10,
    },
    grid: {
      top: '10%',
      left: '5%',
      right: '5%',
      bottom: '20%',
      containLabel: true, //‌防止标签溢出‌
    },
    xAxis: {
      type: 'category',
      data: Array.from(legends),
      axisLine: {
        //轴线
        show: true,
        lineStyle: {
          color: 'rgba(203, 203, 203, 1)',
        },
      },
      axisTick: {
        show: false, //坐标轴刻度线
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        //网格线
        lineStyle: {
          type: 'dashed', //设置网格线类型 dotted：虚线   solid:实线
          color: 'rgba(203, 203, 203, 1)',
        },
        show: true, //隐藏或显示
      },
      axisLine: {
        //轴线
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(203, 203, 203, 1)',
        },
      },
    },
    series: series,
  }
}
function setPieOption() {
  pieOption.value = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const { name, value, percent } = params
        const formattedName = name.length > 12 ? name.replace(/(.{12})/g, '$1<br>') : name
        return `${formattedName}:<br> ${value}（只） (${percent}%)`
      }, // 提示框显示实际数值和计算百分比
      position: 'right', // 设置提示框全部右侧展示
    },
    title: {
      text: '虫数量\n占比',
      top: '41%',
      left: '22%',
      textAlign: 'center',
      textStyle: {
        fontSize: 14,
        color: 'rgba(203, 203, 203, 1)',
        lineHeight: 20,
      },

      // subtextStyle: {
      //   fontSize: 14,
      //   color: 'rgba(203, 203, 203, 1)',
      // },
    },
    legend: {
      // top: '50%',
      // left: 'right',
      // textStyle: {
      //   color: 'rgba(23, 21, 21, 1)',
      // },
      type: 'scroll',
      orient: 'vertical',
      width: '38%',
      right: '0',
      y: 'center',
      icon: 'circle',
      textStyle: {
        // 图例文字的样式
        color: 'rgba(23, 21, 21, 1)',
      },
      pageIconColor: '#5470c6', // 滚动按钮颜色
      pageTextStyle: {
        color: '#707070', // 滚动页码文字颜色
      },
      pageButtonGap: 10, // 上下按钮与文字的间距
      formatter: function (name) {
        // 获取当前系列的数据
        const data = pieOption.value.series[0].data
        // 查找匹配当前图例名称的数据项
        const item = data.find((d) => d.name === name)
        if (item) {
          // 计算总数
          const total = data.reduce((sum, d) => sum + d.value, 0)
          // 计算百分比（保留1位小数）
          const percent = ((item.value / total) * 100).toFixed(1)
          // 返回格式：名称 数值 (百分比%)
          const maxWidth = 0.3 * 280 // 假设容器宽度为690px，40%宽度
          const ellipsisName =
            name.length > maxWidth / 12 ? name.slice(0, maxWidth / 12) + '...' : name
          return `${ellipsisName} ${item.value} (${percent}%)`
        }
        return name
      },
    },
    // },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['48%', '72%'],
        right: '53%',
        avoidLabelOverlap: true,
        label: {
          show: false, // 启用中间文本
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: pieValue.value,
      },
    ],
  }
}

// 打开时间筛选器
function handlerDayPopup(type, value) {
  let date = new Date()
  if (value) {
    date.setFullYear(value.split('-')[0], value.split('-')[1] - 1, value.split('-')[2])
  }
  chooseOption.value = type
  chooseDay.value = value ? date : ''
  showDay.value = true
}

// 选中日期
function confirmDay({ selectedValue }) {
  if (chooseOption.value === 'start') {
    queryDate.value.end = ''
  }
  queryDate.value[
    `${chooseOption.value}`
  ] = `${selectedValue[0]}-${selectedValue[1]}-${selectedValue[2]}`
  if (queryDate.value.start && queryDate.value.end) {
    getIsa()
  }
  chooseDay.value = ''
  chooseOption.value = ''
  showDay.value = false
}

// 加载更多
const loadMore = () => {
  if (loading.value) return
  if (!hasMore.value) {
    Taro.showToast({
      title: '没有更多数据了',
      icon: 'none',
    })
    return
  }
  pageNum.value++
  // getRunLog()
}

// function letArray(val, index) {
//   const lineCharts = lineValue.value
//   const dynamicData: any = []
//   lineCharts.forEach((items) => {
//     const dataY = []
//     const dataX = []
//     // lineCharts[insect].forEach((item) => {
//     items.line.forEach((item) => {
//       dataY.push(item.y)
//       dataX.push(item.x)
//     })
//     dynamicData.push({
//       name: items.label,
//       data: dataY,
//       dataX,
//     })
//   })

//   const firstValuesMap = new Map()

//   // 遍历 dynamicLineData 并存储每个第一个值的索引
//   dynamicData.forEach((item, i) => {
//     const firstValue = item.data[0]
//     if (!firstValuesMap.has(firstValue)) {
//       firstValuesMap.set(firstValue, [])
//     }

//     firstValuesMap.get(firstValue).push(i)
//   })

//   const targetValue = val
//   const matchingIndices = firstValuesMap.get(targetValue)

//   if (!matchingIndices || matchingIndices.length < 2) {
//     return 0
//   }

//   const indexInMatching = matchingIndices.indexOf(index)
//   return indexInMatching !== -1 ? indexInMatching : 0
// }

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    resetList()
    // await getRunLog()
  } finally {
    refreshing.value = false
  }
}
// 重置列表
const resetList = () => {
  insectPestLog.value = []
  pageNum.value = 1
  hasMore.value = true
}

onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  deviceId.value = JSON.parse(decodeURIComponent(params.data))
  // 创建一个新的 Date 对象，表示当前日期和时间
  let currentDate = new Date()
  // 计算三年前的年份
  let threeYearsAgoYear = currentDate.getFullYear() - 3
  // 创建一个新的 Date 对象，表示三年前的今天
  let dateThreeYearsAgo = new Date(currentDate)

  dateThreeYearsAgo.setFullYear(threeYearsAgoYear)
  maxDate.value = new Date()
  minDate.value = dateThreeYearsAgo

  weekColumns.value = generateWeekOptions(new Date().getFullYear())
  yearColumns.value = generateYearOptions()
  queryMonth.value = dayjs().format('YYYY-MM')
  getIsa()
  // getRunLog()
})
</script>

<style scoped lang="scss">
.content {
  width: 690px;
  margin: auto;
}

.monitor-box-bg {
  width: 690px;
  padding: 40px 30px;
  box-sizing: border-box;
  margin: 30px auto;
  border-radius: 20px;
  background: #fff;
}

.chartsDiv {
  width: 100%;
  height: 500px;
  // margin-top: 100px;
}
.charts-nodata {
  width: 100%;
  height: 300px;
  // margin-top:100px ;
  text-align: center;
  line-height: 300px;
  font-size: 32px;
  color: #000000;
}
</style>
