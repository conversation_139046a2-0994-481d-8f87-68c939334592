import Components from 'unplugin-vue-components/webpack'
import NutUIResolver from '@nutui/auto-import-resolver'
import path from 'node:path'
import process from 'node:process'
import { createSwcRegister } from '@tarojs/helper'
import dev from './dev'
import prod from './prod'

export default async (merge) => {
  createSwcRegister({
    only: [(filePath) => filePath.includes('@unocss')],
  })
  const UnoCSS = (await import('@unocss/webpack')).default

  const base = {
    projectName: 'myApp',
    date: '2025-3-18',
    designWidth(input) {
      if (input?.file?.replace(/\\+/g, '/').indexOf('@nutui') > -1) {
        return 375
      }
      return 750
    },
    alias: {
      '@': path.resolve(__dirname, '..', 'src'),
    },
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      828: 1.81 / 2,
      375: 2 / 1,
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    plugins: ['@tarojs/plugin-html'],
    defineConstants: {},
    copy: {
      patterns: [
        { from: 'public', to: 'dist' }, // 这会将public下的文件复制到dist根目录
      ],
      options: {},
    },
    framework: 'vue3',
    compiler: {
      type: 'webpack5',
      prebundle: { enable: false },
    },
    sass: {
      data: `@import "@nutui/nutui-taro/dist/styles/variables.scss";`,
    },
    mini: {
      webpackChain(chain) {
        chain.plugin('unplugin-vue-components').use(
          Components({
            resolvers: [
              NutUIResolver({
                importStyle: 'sass',
                taro: true,
              }),
            ],
          })
        )
        chain.plugin('unocss').use(UnoCSS())
      },
      postcss: {
        pxtransform: {
          enable: true,
          config: {
            // selectorBlackList: ['nut-']
          },
        },
        url: {
          enable: true,
          config: {
            limit: 1024, // 设定转换尺寸上限
          },
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
    },
    h5: {
      webpackChain(chain) {
        chain.plugin('unplugin-vue-components').use(
          Components({
            resolvers: [
              NutUIResolver({
                importStyle: 'sass',
                taro: true,
              }),
            ],
          })
        )
        chain.plugin('unocss').use(UnoCSS())
      },
      publicPath: '/',
      staticDirectory: 'static',
      esnextModules: ['nutui-taro', 'icons-vue-taro'],
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
    },
  }

  if (process.env.NODE_ENV === 'development') return merge({}, base, dev)

  return merge({}, base, prod)
}
