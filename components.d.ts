/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AlarmSettings: typeof import('./src/components/AlarmSettings.vue')['default']
    Chart: typeof import('./src/components/Chart/Chart.vue')['default']
    CustomNavTitle: typeof import('./src/components/CustomNavTitle/CustomNavTitle.vue')['default']
    CustomTabs: typeof import('./src/components/CustomTabs.vue')['default']
    Emput: typeof import('./src/components/emput.vue')['default']
    FileUpload: typeof import('./src/components/Upload/fileUpload.vue')['default']
    Filter: typeof import('./src/components/filter.vue')['default']
    FilterTime: typeof import('./src/components/filterTime.vue')['default']
    NutBadge: typeof import('@nutui/nutui-taro')['Badge']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCalendar: typeof import('@nutui/nutui-taro')['Calendar']
    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']
    NutCheckboxGroup: typeof import('@nutui/nutui-taro')['CheckboxGroup']
    NutCollapse: typeof import('@nutui/nutui-taro')['Collapse']
    NutCollapseItem: typeof import('@nutui/nutui-taro')['CollapseItem']
    NutDatePicker: typeof import('@nutui/nutui-taro')['DatePicker']
    NutDialog: typeof import('@nutui/nutui-taro')['Dialog']
    NutForm: typeof import('@nutui/nutui-taro')['Form']
    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
    NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutNavbar: typeof import('@nutui/nutui-taro')['Navbar']
    NutOverlay: typeof import('@nutui/nutui-taro')['Overlay']
    NutPicker: typeof import('@nutui/nutui-taro')['Picker']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRadio: typeof import('@nutui/nutui-taro')['Radio']
    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTabbar: typeof import('@nutui/nutui-taro')['Tabbar']
    NutTabbarItem: typeof import('@nutui/nutui-taro')['TabbarItem']
    NutTabs: typeof import('@nutui/nutui-taro')['Tabs']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']
    Player: typeof import('./src/components/Player/Player.vue')['default']
    PositionSelect: typeof import('./src/components/PositionSelect/PositionSelect.vue')['default']
    Scanner: typeof import('./src/components/Scanner/Scanner.vue')['default']
    TabCardDate: typeof import('./src/components/TabCardDate/TabCardDate.vue')['default']
    VImage: typeof import('./src/components/VImage/VImage.vue')['default']
  }
}
