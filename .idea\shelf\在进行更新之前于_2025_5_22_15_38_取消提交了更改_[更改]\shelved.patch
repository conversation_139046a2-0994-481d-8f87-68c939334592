Index: src/pages/afterSaleOrderDetails/afterSaleOrderDetails.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n  <PositionSelect\r\n    v-if=\"showPositionSelect\"\r\n    :showSearch=\"false\"\r\n    @close=\"showPositionSelect = false\"\r\n    :modelValue=\"coordinate\"\r\n  ></PositionSelect>\r\n  <div v-else class=\"page-content\">\r\n    <CustomNavTitle title=\"售后工单详情\" />\r\n\r\n    <scroll-view class=\"scroll-view\" scroll-y>\r\n      <div class=\"card\">\r\n        <div class=\"title\">基础信息</div>\r\n        <div class=\"content\">\r\n          <div>\r\n            <span class=\"label\">工单号</span>\r\n            <span class=\"value\">{{ info.workOrderNo || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">设备名称</span>\r\n            <span class=\"value\">{{ info?.deviceTypeName || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">安装日期</span>\r\n            <span class=\"value\">{{ info?.installDate || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">质保期</span>\r\n            <span class=\"value\">{{ info?.order?.warrantyPeriodDate || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">设备编号</span>\r\n            <span class=\"value\">{{ info?.deviceRecordList?.[0]?.deviceNo || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">问题类型</span>\r\n            <span class=\"value\">{{ info?.issueTypeName || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">问题描述</span>\r\n            <span class=\"value\">{{ info?.issueRemark }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">提交日期</span>\r\n            <span class=\"value\">{{ info?.createTime || '--' }}</span>\r\n          </div>\r\n          <div v-if=\"info?.deviceRecordList?.[0]?.address\">\r\n            <span class=\"label\">安装位置</span>\r\n            <span class=\"value btn\" @click=\"showAddress()\">{{\r\n              info?.deviceRecordList?.[0]?.address || '--'\r\n            }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">故障图片</span>\r\n            <span class=\"value\">\r\n              <VImage\r\n                v-for=\"item in info.imageList\"\r\n                :key=\"item.url\"\r\n                :src=\"item.url\"\r\n                style=\"width: 55px; height: 55px; margin-left: 8px\"\r\n              />\r\n            </span>\r\n            <span v-if=\"!info.imageList || info.imageList.length === 0\" class=\"value\">--</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"card\">\r\n        <div class=\"title\">客户信息</div>\r\n        <div class=\"content\">\r\n          <div>\r\n            <span class=\"label\">客户</span>\r\n            <span class=\"value\">{{ info.customerName || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">联系人</span>\r\n            <span class=\"value\">{{ info?.contacts || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">联系电话</span>\r\n            <span class=\"value\">{{ info?.contactsPhone || '--' }}</span>\r\n          </div>\r\n          <div>\r\n            <span class=\"label\">地址</span>\r\n            <span class=\"value\">{{ info?.address || '--' }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"card\">\r\n        <div class=\"title\">维修信息</div>\r\n\r\n        <div class=\"sub-title\">问题类型</div>\r\n\r\n        <nut-input\r\n          v-model=\"form.issueTypeName\"\r\n          readonly\r\n          placeholder=\"请选择\"\r\n          @click=\"\r\n            () => {\r\n              if (info.status === 2) showPicker = true\r\n            }\r\n          \"\r\n        ></nut-input>\r\n\r\n        <nut-popup v-model:visible=\"showPicker\" position=\"bottom\" :style=\"{ height: '300px' }\">\r\n          <nut-picker\r\n            :columns=\"deviceIssue\"\r\n            title=\"问题类型选择\"\r\n            @cancel=\"showPicker = false\"\r\n            @confirm=\"confirm\"\r\n          />\r\n        </nut-popup>\r\n\r\n        <div class=\"sub-title\">问题描述</div>\r\n        <nut-textarea\r\n          :autosize=\"{\r\n            maxHeight: 160,\r\n            minHeight: 60,\r\n          }\"\r\n          :disabled=\"info.status === 4\"\r\n          v-model=\"form.handlerRemark\"\r\n          placeholder=\"请输入问题描述\"\r\n        >\r\n        </nut-textarea>\r\n\r\n        <div class=\"results\">\r\n          <span>处理结果</span>\r\n\r\n          <nut-radio-group\r\n            v-if=\"info.status === 2\"\r\n            v-model=\"form.handlerStatus\"\r\n            direction=\"horizontal\"\r\n          >\r\n            <nut-radio :label=\"1\" icon-size=\"14\">已解决</nut-radio>\r\n            <nut-radio :label=\"0\" icon-size=\"14\">未解决</nut-radio>\r\n          </nut-radio-group>\r\n          <span v-else>{{ form.handlerStatus === 1 ? '已解决' : '未解决' }}</span>\r\n        </div>\r\n\r\n        <div class=\"sub-title\">\r\n          <span>完成图片</span>\r\n          <span class=\"upload-tip\">最多3张</span>\r\n        </div>\r\n        <div class=\"upload-list\">\r\n          <FileUpload v-if=\"info.status === 2\" v-model=\"fileList\" :limit=\"3\" />\r\n          <VImage v-else v-for=\"item in form.handlerImage\" :key=\"item.index\" :src=\"item.url\" />\r\n          <div v-if=\"info.status === 4 && !form.handlerImage?.length\" class=\"no-image\">\r\n            暂无图片\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </scroll-view>\r\n\r\n    <div class=\"bottom-buttons\">\r\n      <nut-button v-if=\"info.status === 2\" type=\"default\" @click=\"handleCancel\">取消</nut-button>\r\n      <nut-button v-if=\"info.status === 2\" type=\"primary\" @click=\"handleSubmit\">确定</nut-button>\r\n\r\n      <nut-button class=\"one-button\" v-else type=\"primary\" size=\"large\" @click=\"handleCancel\"\r\n        >确定</nut-button\r\n      >\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport Taro from '@tarojs/taro'\r\nimport { ref } from 'vue'\r\nimport WorkOrderAPI from '../../api/workOrder'\r\nimport FileUpload from '../../components/Upload/fileUpload.vue'\r\nimport { FileAPI } from '../../api/file'\r\n\r\nconst info = ref<any>({})\r\nconst id = Taro.getCurrentInstance().router?.params?.id\r\n\r\nconst getInfo = async () => {\r\n  const res = await WorkOrderAPI.getDetail(Number(id))\r\n  info.value = res\r\n  const { deviceWorkOrderId, deviceId, deviceRecordList, issueType, issueTypeName, handlerStatus } =\r\n    res\r\n  form.value = {\r\n    deviceWorkOrderId,\r\n    deviceId,\r\n    handlerImage: deviceRecordList?.[0]?.handlerImage,\r\n    handlerRemark: '',\r\n    issueType,\r\n    issueTypeName,\r\n    handlerStatus: handlerStatus === 0 ? handlerStatus : 1,\r\n  }\r\n}\r\n\r\ngetInfo()\r\n\r\nconst coordinate = ref<any>()\r\nconst showAddress = () => {\r\n  showPositionSelect.value = true\r\n  coordinate.value = info.value?.deviceRecordList[0]?.coordinate\r\n}\r\n\r\nconst fileList = ref<any[]>([])\r\n\r\nconst form = ref<any>({})\r\n\r\nconst deviceIssue = ref<any[]>([])\r\nconst showPicker = ref(false)\r\nconst getdeviceIssue = async () => {\r\n  const res = await WorkOrderAPI.deviceIssue()\r\n  deviceIssue.value = res.map((item) => ({\r\n    value: item.value,\r\n    text: item.label,\r\n  }))\r\n}\r\nconst confirm = (selectedValue) => {\r\n  showPicker.value = false\r\n  form.value.issueType = selectedValue.selectedOptions?.[0]?.value\r\n  form.value.issueTypeName = selectedValue.selectedOptions?.[0]?.text\r\n}\r\ngetdeviceIssue()\r\n\r\nconst submitLoading = ref(false)\r\nconst handleSubmit = async () => {\r\n  if (!form.value.issueType || !form.value.issueTypeName) {\r\n    Taro.showToast({\r\n      title: '请选择问题类型',\r\n      icon: 'none',\r\n    })\r\n    return\r\n  }\r\n  if (!form.value.handlerRemark) {\r\n    Taro.showToast({\r\n      title: '请输入问题描述',\r\n      icon: 'none',\r\n    })\r\n    return\r\n  }\r\n  form.value.handlerImage = fileList.value.map((item) => {\r\n    return {\r\n      name: item.name,\r\n      url: item.url,\r\n    }\r\n  })\r\n\r\n  const { id, deviceRecordList } = info.value\r\n  Object.assign(form.value, {\r\n    deviceWorkOrderId: id,\r\n    deviceId: deviceRecordList?.[0]?.deviceId,\r\n  })\r\n\r\n  submitLoading.value = true\r\n  try {\r\n    await WorkOrderAPI.completeAfterSales(form.value)\r\n    Taro.showToast({\r\n      title: '操作成功',\r\n      icon: 'success',\r\n    })\r\n    setTimeout(() => {\r\n      handleCancel()\r\n    }, 2000)\r\n  } finally {\r\n    submitLoading.value = false\r\n  }\r\n}\r\n\r\nconst handleCancel = () => {\r\n  Taro.navigateBack()\r\n}\r\n\r\nFileAPI.init()\r\n\r\n// 地图查看相关功能\r\nconst showPositionSelect = ref(false)\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\nimage {\r\n  width: 110px;\r\n  height: 110px;\r\n}\r\n.bottom-buttons {\r\n  width: 100%;\r\n  padding: 0 30px;\r\n  box-sizing: border-box;\r\n  position: absolute;\r\n  left: 50%;\r\n  bottom: 50px;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  .nut-button {\r\n    width: 300px !important;\r\n  }\r\n  .one-button {\r\n    width: 100% !important;\r\n  }\r\n}\r\n.card {\r\n  width: 690px;\r\n  background: #ffffff;\r\n  border-radius: 20px;\r\n  padding: 40px 30px;\r\n  margin: 30px auto;\r\n  box-sizing: border-box;\r\n  .title {\r\n    height: 75px;\r\n    border-bottom: 1px solid #e7e9ee;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 32px;\r\n    font-weight: bold;\r\n    align-items: center;\r\n    padding-bottom: 30px;\r\n    box-sizing: border-box;\r\n    margin-bottom: 35px;\r\n    .count {\r\n      color: #019e59;\r\n    }\r\n  }\r\n  .sub-title {\r\n    margin: 35px 0 25px;\r\n    font-size: 28px;\r\n    color: #5b5b5b;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    .upload-tip {\r\n      color: #909090;\r\n      font-size: 24px;\r\n    }\r\n  }\r\n\r\n  .results {\r\n    font-size: 28px;\r\n    color: #5b5b5b;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-top: 35px;\r\n  }\r\n\r\n  .content {\r\n    font-size: 28px;\r\n\r\n    > div {\r\n      margin-bottom: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n    .span2 {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      > div {\r\n        display: flex;\r\n      }\r\n    }\r\n    > div {\r\n      display: flex;\r\n    }\r\n    .label {\r\n      flex-shrink: 0;\r\n      color: #909090;\r\n      width: 120px;\r\n      //text-align: justify;\r\n      //text-align-last: justify;\r\n      margin-right: 8px;\r\n    }\r\n    .value {\r\n      color: #101010;\r\n      text-align: end;\r\n    }\r\n    .btn {\r\n      color: #019e59;\r\n    }\r\n  }\r\n  .custom-button {\r\n    font-size: 28px;\r\n    color: #019e59;\r\n    width: 100px;\r\n    height: 46px;\r\n    background: #ecfcf1;\r\n    border-radius: 8px 8px 8px 8px;\r\n    border: 1px solid #019e59;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n:deep(.nut-radio__label) {\r\n  font-size: 28px !important;\r\n  color: #101010;\r\n}\r\n:deep(.nut-input) {\r\n  box-sizing: border-box;\r\n  border: 1px solid #d4d4d4;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n  .nut-input-inner {\r\n    height: 58px;\r\n  }\r\n}\r\n:deep(.nut-textarea) {\r\n  border: 1px solid #d4d4d4;\r\n}\r\n.scroll-view {\r\n  height: calc(100vh - 180px);\r\n  .device-item {\r\n    width: 627px;\r\n    height: 78px;\r\n    background: #ffffff;\r\n    border-radius: 4px 4px 4px 4px;\r\n    border: 1px solid #d4d4d4;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-top: 35px;\r\n    padding: 30px 20px;\r\n    box-sizing: border-box;\r\n    font-size: 28px;\r\n    .status {\r\n      color: #019e59;\r\n      &.uninstalled {\r\n        color: #909090;\r\n      }\r\n    }\r\n  }\r\n}\r\n.upload-list {\r\n  width: 630px;\r\n  height: 166px;\r\n  background: #ffffff;\r\n  border-radius: 4px 4px 4px 4px;\r\n  border: 1px solid #d4d4d4;\r\n  margin-top: 24px;\r\n  padding: 28px;\r\n  box-sizing: border-box;\r\n  font-size: 28px;\r\n}\r\n.no-image {\r\n  color: #333;\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n:deep(.nut-textarea__textarea) {\r\n  color: #000 !important;\r\n}\r\n:deep(.nut-textarea) {\r\n  padding-left: 20px;\r\n  padding-right: 20px;\r\n}\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/pages/afterSaleOrderDetails/afterSaleOrderDetails.vue b/src/pages/afterSaleOrderDetails/afterSaleOrderDetails.vue
--- a/src/pages/afterSaleOrderDetails/afterSaleOrderDetails.vue	(revision c19bab64137165653d6d2d751b30b08cf483b97a)
+++ b/src/pages/afterSaleOrderDetails/afterSaleOrderDetails.vue	(date 1747899478855)
@@ -50,17 +50,21 @@
               info?.deviceRecordList?.[0]?.address || '--'
             }}</span>
           </div>
-          <div>
+
+          <div class="pt-1.4 !flex-col">
             <span class="label">故障图片</span>
-            <span class="value">
-              <VImage
-                v-for="item in info.imageList"
-                :key="item.url"
-                :src="item.url"
-                style="width: 55px; height: 55px; margin-left: 8px"
-              />
-            </span>
-            <span v-if="!info.imageList || info.imageList.length === 0" class="value">--</span>
+            <div class="value img-list">
+              <div v-if="info.imageList?.length" class="flex flex-wrap gap-2">
+                <VImage
+                  class="w-11 h-11"
+                  v-for="url in info.imageList.map((item) => item.url)"
+                  :key="url"
+                  :src="url"
+                  :preview-src-list="info.imageList.map((item) => item.url)"
+                />
+              </div>
+              <span v-else class="value">无</span>
+            </div>
           </div>
         </div>
       </div>
Index: components.d.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable */\r\n/* prettier-ignore */\r\n// @ts-nocheck\r\n// Generated by unplugin-vue-components\r\n// Read more: https://github.com/vuejs/core/pull/3399\r\nexport {}\r\n\r\ndeclare module 'vue' {\r\n  export interface GlobalComponents {\r\n    AlarmSettings: typeof import('./src/components/AlarmSettings.vue')['default']\r\n    Chart: typeof import('./src/components/Chart/Chart.vue')['default']\r\n    CustomNavTitle: typeof import('./src/components/CustomNavTitle/CustomNavTitle.vue')['default']\r\n    CustomTabs: typeof import('./src/components/CustomTabs.vue')['default']\r\n    Emput: typeof import('./src/components/emput.vue')['default']\r\n    FileUpload: typeof import('./src/components/Upload/fileUpload.vue')['default']\r\n    Filter: typeof import('./src/components/filter.vue')['default']\r\n    LivePlayer: typeof import('./src/components/LivePlayer/LivePlayer.vue')['default']\r\n    LivePlayerDialog: typeof import('./src/components/LivePlayer/LivePlayerDialog.vue')['default']\r\n    NutBadge: typeof import('@nutui/nutui-taro')['Badge']\r\n    NutButton: typeof import('@nutui/nutui-taro')['Button']\r\n    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']\r\n    NutCheckboxGroup: typeof import('@nutui/nutui-taro')['CheckboxGroup']\r\n    NutCollapse: typeof import('@nutui/nutui-taro')['Collapse']\r\n    NutCollapseItem: typeof import('@nutui/nutui-taro')['CollapseItem']\r\n    NutDatePicker: typeof import('@nutui/nutui-taro')['DatePicker']\r\n    NutDialog: typeof import('@nutui/nutui-taro')['Dialog']\r\n    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']\r\n    NutForm: typeof import('@nutui/nutui-taro')['Form']\r\n    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']\r\n    NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']\r\n    NutInput: typeof import('@nutui/nutui-taro')['Input']\r\n    NutNavbar: typeof import('@nutui/nutui-taro')['Navbar']\r\n    NutOverlay: typeof import('@nutui/nutui-taro')['Overlay']\r\n    NutPicker: typeof import('@nutui/nutui-taro')['Picker']\r\n    NutPopup: typeof import('@nutui/nutui-taro')['Popup']\r\n    NutRadio: typeof import('@nutui/nutui-taro')['Radio']\r\n    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']\r\n    NutRate: typeof import('@nutui/nutui-taro')['Rate']\r\n    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']\r\n    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']\r\n    NutTabbar: typeof import('@nutui/nutui-taro')['Tabbar']\r\n    NutTabbarItem: typeof import('@nutui/nutui-taro')['TabbarItem']\r\n    NutTabs: typeof import('@nutui/nutui-taro')['Tabs']\r\n    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']\r\n    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']\r\n    PositionSelect: typeof import('./src/components/PositionSelect/PositionSelect.vue')['default']\r\n    Scanner: typeof import('./src/components/Scanner/Scanner.vue')['default']\r\n    TabCardDate: typeof import('./src/components/TabCardDate/TabCardDate.vue')['default']\r\n    VImage: typeof import('./src/components/VImage/VImage.vue')['default']\r\n  }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/components.d.ts b/components.d.ts
--- a/components.d.ts	(revision c19bab64137165653d6d2d751b30b08cf483b97a)
+++ b/components.d.ts	(date 1747898831651)
@@ -24,7 +24,6 @@
     NutCollapseItem: typeof import('@nutui/nutui-taro')['CollapseItem']
     NutDatePicker: typeof import('@nutui/nutui-taro')['DatePicker']
     NutDialog: typeof import('@nutui/nutui-taro')['Dialog']
-    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
     NutForm: typeof import('@nutui/nutui-taro')['Form']
     NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
     NutImagePreview: typeof import('@nutui/nutui-taro')['ImagePreview']
@@ -35,7 +34,6 @@
     NutPopup: typeof import('@nutui/nutui-taro')['Popup']
     NutRadio: typeof import('@nutui/nutui-taro')['Radio']
     NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
-    NutRate: typeof import('@nutui/nutui-taro')['Rate']
     NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
     NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
     NutTabbar: typeof import('@nutui/nutui-taro')['Tabbar']
