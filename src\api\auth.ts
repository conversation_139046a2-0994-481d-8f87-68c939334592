import request from '../utils/request'

const AUTH_BASE_URL = '/api/v1/auth'

class AuthAPI {
  /** 登录 接口*/
  static login(data: LoginData): Promise<LoginResult> {
    const formData = new FormData()
    formData.append('authType', data.authType)
    formData.append('captchaKey', data.captchaKey)
    formData.append('captchaCode', data.captchaCode)
    if (data.authType == '0') {
      formData.append('username', data.username)
      formData.append('password', data.password)
    } else {
      formData.append('phone', data.phone)
      formData.append('phoneCode', data.phoneCode)
    }

    return request({
      url: '/api/v1/manage/auth/login',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }

  /** 注销 接口*/
  static logout() {
    return request({
      url: `/api/v1/auth/manage/logout`,
      method: 'delete',
    })
  }

  /** 获取验证码 接口*/
  static getCaptcha() {
    return request({
      url: `${AUTH_BASE_URL}/captcha`,
      method: 'get',
    })
  }

  /**发送手机验证码 接口*/
  static setPhoneCode(data: SetCode) {
    return request({
      url: `${AUTH_BASE_URL}/phone-code/${data.type}?phone=${data.phone}`,
      method: 'POST',
    })
  }
  /** 忘记密码 验证手机验证码 */
  static checkPhoneCode(data: CheckCode) {
    return request({
      url: `${AUTH_BASE_URL}/phone-code/${data.type}?phone=${data.phone}&phoneCode=${data.phoneCode}`,
      method: 'get',
    })
  }
  /** 重置密码  */
  static resetCaptcha(data: any) {
    return request({
      url: `/api/v1/manage/h5/users/forget-password`,
      method: 'put',
      data,
    })
  }
  /** 修改密码  */
  static updatePassword(data: UpdatePwdRequest) {
    return request({
      url: `/api/v1/manage/h5/users/password`,
      method: 'put',
      data,
    })
  }

  // 获取当前用户信息
  static me() {
    return request<UserInfo>({
      url: `/api/v1/manage/h5/users/me`,
      method: 'get',
    })
  }
}

export default AuthAPI

/**
 * 修改密码表格
 *
 * UpdatePasswordForm
 */
export interface UpdatePwdRequest {
  confirmPassword: string
  oldPassword: string
  password: string
  [property: string]: any
}

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 登录类型  authType 0 密码登录 1 手机号登录*/
  authType: number
  /** 手机号 */
  phone?: string
  /** 手机验证码 */
  phoneCode?: string
  /** 验证码缓存key */
  captchaKey?: string
  /** 验证码 */
  captchaCode?: string
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string
  /** 过期时间(单位：毫秒) */
  expires?: number
  /** 刷新token */
  refreshToken?: string
  /** token 类型 */
  tokenType?: string
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string
  /** 验证码图片Base64字符串 */
  captchaBase64: string
}

/** 请求验证码 */
export interface SetCode {
  /** 手机号 */
  phone: string | number
  /** 请求类型 */
  type: 'login' | 'forget-password'
}

/** 验证验证码 */
export interface CheckCode {
  /** 手机号 */
  phone: string | number
  /** 手机验证码 */
  phoneCode: string | number
  /** 请求类型 */
  // type: 'login-register' | 'forget-password'
  type: string
}

/**
 * UserInfoVO
 */
export interface UserInfo {
  avatar?: string
  /**
   * 用户拥有的菜单权限
   */
  menus: number[]
  nickname?: string
  /**
   * 用户拥有的按钮权限
   */
  perms: string[]
  /**
   * 用户的角色
   */
  roles: string[]
  userId?: number
  username?: string
}
