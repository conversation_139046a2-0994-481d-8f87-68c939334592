import { defineStore } from 'pinia'
import { store } from '@/store/index'
import AuthAPI, { UserInfo } from '@/api/auth'
import Taro from '@tarojs/taro'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: { perms: [], menus: [], roles: [] } as UserInfo,
    permMap: {},
  }),
  actions: {
    getUserInfo() {
      Taro.showLoading({ title: '加载中...' })
      return new Promise<UserInfo>((resolve, reject) => {
        AuthAPI.me()
          .then((data) => {
            if (!data) {
              reject('Verification failed, please Login again.')
              return
            }

            if (!data.roles || data.roles.length <= 0) {
              reject('getUserInfo: roles must be a non-null array!')
              return
            }

            this.user = data
            this.permMap = {}
            for (const item of [...data.perms, ...data.menus]) {
              this.permMap[item] = true
            }
          })
          .catch((error) => {
            reject(error)
          })
          .finally(() => {
            Taro.hideLoading({ noConflict: true })
          })
      })
    },
  },
})

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
