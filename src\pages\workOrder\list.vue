<template>
  <div class="page-content">
    <CustomNavTitle :title="type === '1' ? '安装工单' : '售后工单'" />

    <CustomTabs v-model="status" :tab-list="tabList" />

    <scroll-view
      class="scroll-view"
      :scroll-y="true"
      @scrolltolower="handleScrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
    >
      <div class="card" v-for="item in list" @click="handleClick(item.id)" :key="item.id">
        <!-- 安装工单列表 -->
        <div v-if="type === '1'">
          <div class="title">
            <span>工单号：{{ item.workOrderNo || '--' }}</span>
            <RectRight color="#909090" />
          </div>
          <div class="content">
            <div class="span2">
              <div>
                <span class="label">设备名称</span>：
                <span class="value">{{ item.deviceTypeName || '--' }}</span>
              </div>
              <div>
                <span class="label">设备数量</span>：
                <span class="value">{{
                  `${item.handlerDeviceCount || 0}/${item.deviceCount}`
                }}</span>
              </div>
            </div>
            <div>
              <span class="label">安装日期</span>：
              <span class="value">{{ item.installDate || '--' }}</span>
            </div>
            <div>
              <span class="label">客户</span>：
              <span class="value">{{ item.customerName || '--' }}</span>
            </div>
            <div>
              <span class="label">联系人</span>：
              <span class="value">{{ item.contacts || '--' }}</span>
            </div>
            <div>
              <span class="label">联系电话</span>：
              <span class="value">{{ item.contactsPhone || '--' }}</span>
            </div>
            <div>
              <span class="label">地址</span>：
              <span class="value">{{ item.address || '--' }}</span>
            </div>
          </div>
          <div class="btn">
            <div class="call" @click.stop="handleCall(item.contactsPhone)">呼叫</div>
          </div>
        </div>

        <!-- 售后工单列表 -->
        <div v-else>
          <div class="title">
            <span>设备名称：{{ item.deviceTypeName || '--' }}</span>
            <RectRight color="#909090" />
          </div>
          <div class="content" style="border-bottom: none">
            <div>
              <span class="label">问题类型</span>：
              <span class="value">{{ item.issueTypeName || '--' }}</span>
            </div>
            <div>
              <span class="label">问题描述</span>：
              <span class="value">{{ item.issueRemark || '--' }}</span>
            </div>
            <div>
              <span class="label">{{ status === 1 ? '提交时间' : '处理时间' }}</span
              >：
              <span class="value">{{
                (status === 1 ? item.createTime : item.modifyTime) || '--'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- <div v-if="list.length === 0" class="empty-text">暂无数据</div> -->
      <emput v-if="list.length === 0" message="暂无数据" />
    </scroll-view>
  </div>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref, reactive, watch, onMounted } from 'vue'
import CustomTabs from '../../components/CustomTabs.vue'
import WorkOrderAPI from '../../api/workOrder'
import { RectRight } from '@nutui/icons-vue-taro'
import { eventCenter, getCurrentInstance } from '@tarojs/taro'

const status = ref(1)
const statusMap = {
  1: 2,
  2: 4,
}

const type = ref('1')

const tabList = ref<any[]>([])
const countInfo = ref<any>({})

eventCenter.on(getCurrentInstance().router.onShow, () => {
  type.value = Taro.getCurrentInstance().router?.params?.type || '1'
  getList(true)
  WorkOrderAPI.workOrderCount().then((res) => {
    countInfo.value = res
    tabList.value = [
      {
        title: '待处理',
        paneKey: 1,
        count:
          type.value === '1'
            ? Number(countInfo.value.installCount) || 0
            : Number(countInfo.value.afterSalesCount) || 0,
      },
      {
        title: '已处理',
        paneKey: 2,
        count:
          type.value === '1'
            ? Number(countInfo.value.completeInstallCount) || 0
            : Number(countInfo.value.completeAfterSalesCount) || 0,
      },
    ]
  })
})

const pageParams = reactive<any>({
  pageNum: 1,
  pageSize: 10,
  status: statusMap[status.value],
})

const list = ref<any[]>([])
const hasMore = ref(true)
const isRefreshing = ref(false)
const loading = ref(false)

const getList = async (isRefresh = false) => {
  if (loading.value) return

  try {
    loading.value = true
    if (!isRefresh) {
      Taro.showToast({
        title: '加载中...',
        icon: 'loading',
        duration: 1000,
      })
    }

    pageParams.type = Number(type.value)

    const res = await WorkOrderAPI.page(pageParams)
    if (isRefresh) {
      list.value = res.records
    } else {
      list.value = [...list.value, ...res.records]
    }

    hasMore.value = res.records.length === pageParams.pageSize

    if (!hasMore.value && !isRefresh && list.value.length > pageParams.pageSize) {
      Taro.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 1500,
      })
    }
  } catch (error) {
    console.error('获取工单列表失败:', error)
    Taro.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

const handleRefresh = async () => {
  isRefreshing.value = true
  pageParams.pageNum = 1
  await getList(true)
  isRefreshing.value = false
}

const handleScrollToLower = async () => {
  if (!hasMore.value || loading.value) return
  pageParams.pageNum++
  await getList()
}

watch(
  () => status.value,
  () => {
    pageParams.status = statusMap[status.value]
    pageParams.pageNum = 1
    getList(true)
  }
)

const handleClick = (id: string) => {
  const url =
    type.value === '1'
      ? '/pages/workOrderDetails/installDetails'
      : '/pages/afterSaleOrderDetails/afterSaleOrderDetails'
  Taro.navigateTo({
    url: `${url}?id=${id}`,
  })
}

const handleCall = (phone: string) => {
  console.log(phone)
  if (!phone || phone === '--') {
    Taro.showToast({
      title: '无效的联系电话',
      icon: 'none',
    })
    return
  }

  Taro.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      Taro.showToast({
        title: '拨号失败',
        icon: 'none',
      })
    },
  })
}
</script>

<style scoped lang="scss">
.scroll-view {
  padding: 30px;
  height: calc(100vh - 150px);
  box-sizing: border-box;
}
.card {
  width: 100%;
  background: #ffffff;
  border-radius: 20px;
  padding: 40px 30px;
  box-sizing: border-box;
  margin-bottom: 30px;
  .title {
    height: 75px;
    border-bottom: 1px solid #e7e9ee;
    display: flex;
    justify-content: space-between;
    font-size: 32px;
    font-weight: bold;
    align-items: center;
    padding-bottom: 30px;
    box-sizing: border-box;
    margin-bottom: 35px;
  }
  .content {
    font-size: 28px;
    border-bottom: 1px solid #e7e9ee;

    > div {
      margin-bottom: 12px;
    }
    .span2 {
      display: flex;
      justify-content: space-between;
      > div {
        display: flex;
      }
    }
    > div {
      display: flex;
    }
    .label {
      color: #909090;
      width: 120px;
      text-align: justify;
      text-align-last: justify;
      margin-right: 8px;
    }
    .value {
      color: #101010;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    .call {
      font-size: 28px;
      color: #019e59;
      width: 120px;
      height: 56px;
      background: #ecfcf1;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #019e59;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.empty-text {
  text-align: center;
  color: #909090;
  font-size: 28px;
  padding: 40px 0;
}

.nut-textarea {
}
</style>
