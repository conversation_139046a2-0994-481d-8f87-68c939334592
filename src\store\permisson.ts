import { defineStore } from 'pinia'
import { useUserStore } from '@/store/user'

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    routeMap: {},
    routes: [],
  }),
  actions: {},
})

/**
 * 验证按钮权限
 * */
export function auth(value: string | string[]) {
  const userStore = useUserStore()
  const { roles } = userStore.user
  const perMap = userStore.permMap

  // 超级管理员 拥有所有权限
  if (roles.includes('ROOT')) {
    return true
  }

  if (!value) return false

  return typeof value === 'string' ? perMap[value] : value.some((perm) => perMap[perm])
}
