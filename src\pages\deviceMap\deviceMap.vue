<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import loadBigemap from '../../utils/bigemap'
import { DEFAULT_CENTER, DEFAULT_ZOOM } from '../../config/map'
import type { DeviceCount, Farmland } from '../../api/deviceMap'
import DeviceMapAPI from '../../api/deviceMap'
import MonitorAPI from '../../api/device/monitor'
import Taro from '@tarojs/taro'
import chongqing from '@/assets/deviceMap/<EMAIL>'
import chongqingO from '@/assets/deviceMap/<EMAIL>'
import chongqingA from '@/assets/deviceMap/icon_chongqing@2x(2).png'
import chongqingS from '@/assets/deviceMap/icon_chongqing@2x(1).png'
import chongqingN from '@/assets/deviceMap/icon_chongqingfenxiyi_n.png'
import chongqingAS from '@/assets/deviceMap/icon_chongqing_as.png'
import shachong from '@/assets/deviceMap/<EMAIL>'
import shachongO from '@/assets/deviceMap/<EMAIL>'
import shachongA from '@/assets/deviceMap/icon_shachongdeng@2x(2).png'
import shachongS from '@/assets/deviceMap/icon_shachongdeng@2x(1).png'
import shachongN from '@/assets/deviceMap/icon_shachongdeng_n.png'
import shachongAS from '@/assets/deviceMap/icon_shachongdeng_as.png'
import shangqing from '@/assets/deviceMap/<EMAIL>'
import shangqingO from '@/assets/deviceMap/<EMAIL>'
import shangqingA from '@/assets/deviceMap/icon_shangqing@2x(2).png'
import shangqingS from '@/assets/deviceMap/icon_shangqing@2x(1).png'
import shangqingN from '@/assets/deviceMap/icon_shangqing_n.png'
import shangqingAS from '@/assets/deviceMap/icon_shangqing_as.png'
import shexiangtou from '@/assets/deviceMap/<EMAIL>'
import shexiangtouO from '@/assets/deviceMap/<EMAIL>'
import shexiangtouA from '@/assets/deviceMap/icon_jiankong@2x(2).png'
import shexiangtouS from '@/assets/deviceMap/icon_jiankong@2x(1).png'
import shexiangtouN from '@/assets/deviceMap/icon_jiankong_n.png'
import shexiangtouAS from '@/assets/deviceMap/icon_jiankong_as.png'
import xinhao from '@/assets/deviceMap/icon_xinhao.png'
import xinhao1 from '@/assets/deviceMap/icon_xinhao_1.png'
import xinhao2 from '@/assets/deviceMap/icon_xinhao_2.png'
import xinhao3 from '@/assets/deviceMap/icon_xinhao_3.png'
import xinhao4 from '@/assets/deviceMap/icon_xinhao_4.png'
import xinhao5 from '@/assets/deviceMap/icon_xinhao_5.png'
import { Search2 } from '@nutui/icons-vue-taro'
import gcoord from 'gcoord'

import iconBottomAlarm from '@/assets/mapIcons/bottom/alarm.webp'
import iconBottomOffline from '@/assets/mapIcons/bottom/offline.webp'

import iconScd from '@/assets/mapIcons/<EMAIL>'
import iconBottomScd from '@/assets/mapIcons/bottom/scd.webp'

import iconSq from '@/assets/mapIcons/<EMAIL>'
import iconBottomSq from '@/assets/mapIcons/bottom/sq.webp'

import iconCq from '@/assets/mapIcons/<EMAIL>'
import iconBottomCq from '@/assets/mapIcons/bottom/cq.webp'

import iconJk from '@/assets/mapIcons/<EMAIL>'
import iconBottomJk from '@/assets/mapIcons/bottom/jk.webp'

const deviceIconMap2 = {
  1: iconScd,
  2: iconSq,
  3: iconCq,
  4: iconJk,
}

const deviceBottomIconMap = {
  1: iconBottomScd,
  2: iconBottomSq,
  3: iconBottomCq,
  4: iconBottomJk,
}

const classMap = {
  1: 'iconScd',
  2: 'iconSq',
  3: 'iconCq',
  4: 'iconJk',
}

const emit = defineEmits(['change', 'close'])

const xinhaoMap = {
  0: xinhao,
  1: xinhao1,
  2: xinhao2,
  3: xinhao3,
  4: xinhao4,
  5: xinhao5,
}

const infoIconMap = {
  1: shachongN,
  2: shangqingN,
  3: chongqingN,
  4: shexiangtouN,
}

const mapContainer = ref<HTMLElement>()

let map: any
let BM: any
let currentSelectedMarker: any = null
let markerCluster: any // MarkerCluster 实例

onMounted(() => {
  loadBigeMap()
})

const loadBigeMap = () => {
  loadBigemap.then(() => {
    BM = (window as any).BM
    initMap()
  })
}

const customerId = ref<number>()
const customerName = ref<string>('')
const customerList = ref<any[]>([])
const dropdownList = ref<any[]>([])
const showDropdown = ref(false)
const getCustomerList = async () => {
  const res = await DeviceMapAPI.listCustomer()
  customerList.value = res
  dropdownList.value = res
  customerId.value = res[0]?.id
  customerName.value = res[0]?.name
  customerChange()
}

const search = () => {
  dropdownList.value = customerList.value.filter((item) =>
    item.name.includes(customerName.value.trim())
  )
}

const customerSelect = (itemData: any) => {
  showDetail.value = false
  customerId.value = itemData.id
  customerName.value = itemData.name
  showDropdown.value = false
  customerChange()
}
const customerChange = () => {
  // 获取设备点位列表
  getMarkerList()
  // 获取设备统计数据
  getDeviceCount()
  // 获取客户地块
  getLandList()
}

// 初始化点聚合
const initMarkerCluster = () => {
  // 创建MarkerClusterGroup实例
  markerCluster = new BM.MarkerClusterGroup({
    showCoverageOnHover: false, // 不显示聚合边界
    zoomToBoundsOnClick: true, // 点击聚合点放大到边界
    removeOutsideVisibleBounds: false,
    animate: true, // 启用动画
    maxClusterRadius: 30, // 聚合半径
    disableClusteringAtZoom: null,
    // 自定义聚合点图标
    // iconCreateFunction(cluster) {
    //   const count = cluster.getChildCount()
    //   return BM.divIcon({
    //     html: `<div class="marker-content">${count}</div>`,
    //     className: 'custom-marker',
    //     iconSize: [38, 38],
    //     iconAnchor: [19, 19],
    //   })
    // },
  })

  // 添加聚合组到地图
  map.addLayer(markerCluster)

  // 聚合点点击事件
  markerCluster.on('clusterclick', (a) => {
    // 可以添加自定义行为，默认行为是放大到包含所有子标记点的范围
  })
}

const initMap = async () => {
  BM.Config.HTTP_URL = 'https://map.vankeytech.com:9100'
  BM.Config.HTTPS_URL = 'https://map.vankeytech.com:9100'
  map = BM.map(mapContainer.value, 'bigemap.1cwjdiiu', {
    crs: BM.CRS.Baidu,
    center: [DEFAULT_CENTER.latitude, DEFAULT_CENTER.longitude],
    zoom: 16,
    minZoom: 5,
    zoomControl: false,
    maxZoom: 18,
  })

  // 获取所有客户列表、默认选中首个客户
  getCustomerList()

  // 初始化点聚合
  initMarkerCluster()
}

const polygonColors = [
  { color: '#FFC72F', fillColor: 'rgb(255, 199, 47)', fillOpacity: 0.12 },
  { color: '#FF691F', fillColor: 'rgb(255,105,31)', fillOpacity: 0.12 },
  { color: '#20F6E9', fillColor: 'rgb(32,246,233)', fillOpacity: 0.12 },
  { color: '#21EBAB', fillColor: 'rgb(33,235,171)', fillOpacity: 0.12 },
  { color: '#1F98FF', fillColor: 'rgb(31,152,255)', fillOpacity: 0.12 },
  { color: '#CE54EC', fillColor: 'rgb(206,84,236)', fillOpacity: 0.12 },
  { color: '#295BFF', fillColor: 'rgb(41,91,255)', fillOpacity: 0.12 },
  { color: '#D9F421', fillColor: 'rgb(217,244,33)', fillOpacity: 0.12 },
  { color: '#44F421', fillColor: 'rgb(68,244,33)', fillOpacity: 0.12 },
  { color: '#72D2FF', fillColor: 'rgb(114,210,255)', fillOpacity: 0.12 },
]

let polygonList: any = []
const getLandList = async () => {
  if (polygonList.length > 0) {
    polygonList.forEach((item) => {
      item.remove()
    })
    polygonList.length = 0
  }
  const res: Farmland[] = await DeviceMapAPI.farmland(customerId.value as number)
  res.forEach((item: Farmland, index: number) => {
    const polygon = BM.polygon(
      item.range.coordinates?.map((point) => [point.latitude, point.longitude]),
      polygonColors[index % polygonColors.length]
    )
    polygon.addTo(map)
    polygonList.push(polygon)
  })
}

const showDetail = ref(false)
const unfold = ref(['name1'])

// 设备点位列表功能块
const markerMap = {
  1: {
    0: shachongO,
    1: shachong,
  },
  2: {
    0: shangqingO,
    1: shangqing,
  },
  3: {
    0: chongqingO,
    1: chongqing,
  },
  4: {
    0: shexiangtouO,
    1: shexiangtou,
  },
}
const markerSelectedMap = {
  1: {
    false: shachongS,
    true: shachongAS,
  },
  2: {
    false: shangqingS,
    true: shangqingAS,
  },
  3: {
    false: chongqingS,
    true: chongqingAS,
  },
  4: {
    false: shexiangtouS,
    true: shexiangtouAS,
  },
}
const markerAlarmMap = {
  1: shachongA,
  2: shangqingA,
  3: chongqingA,
  4: shexiangtouA,
}
const markerList: any = []
const getMarkerList = async () => {
  const res: any = await DeviceMapAPI.mapList(customerId.value as number)

  if (markerList.length > 0) {
    markerList.forEach((item) => {
      item.remove()
    })
    markerList.length = 0
  }
  const bounds = new BM.LatLngBounds()

  clearAllMarkers()

  res.forEach((item) => {
    // 截取name最后三位
    const num = item.name.slice(-3)

    let className = classMap[item.deviceType]
    let bottomIcon = deviceBottomIconMap[item.deviceType]
    if (item.isAlarm) {
      bottomIcon = iconBottomAlarm
      className += ' alarm'
    } else if (item.status === 0) {
      bottomIcon = iconBottomOffline
      className += ' offline'
    } else if (item.deviceType === 1 && !item.isLight) {
      bottomIcon = iconBottomCq
      className += ' close'
    }

    const deviceIcon = BM.divIcon({
      html: `
        <header class="header flex items-center w-58Px h-28Px rounded-6Px">
          <img src="${
            deviceIconMap2[item.deviceType]
          }" class="w-16Px h-18Px object-fit ml-6Px mr-4Px"/>
          <span text="14Px white">${num}</span>
        </header>
        <footer class="flex justify-center mt-2Px">
          <div style="background: url(${bottomIcon}) center / 100% 100% no-repeat" class="footerImage w-16Px h-8Px"/>
        </footer>
        `,
      iconSize: [58, 38],
      iconAnchor: [29, 38],
      className: `deviceIcon ${className}`,
    })
    const { latitude, longitude } = item.point
    const wgs84Coord = gcoord.transform([longitude, latitude], gcoord.WGS84, gcoord.BD09)
    const marker = BM.marker([wgs84Coord[1], wgs84Coord[0]], {
      icon: deviceIcon,
      deviceType: item.deviceType, // 保存设备类型
      status: item.status,
      isAlarm: item.isAlarm,
      position: { lng: wgs84Coord[0], lat: wgs84Coord[1] },
      serialNum: item.code,
      id: item.id,
    })
    item.point.latitude = wgs84Coord[1]
    item.point.longitude = wgs84Coord[0]

    // 为每个marker添加点击事件
    marker.on('click', (e: any) => {
      e.originalEvent.stopPropagation() // 阻止事件冒泡

      markerClick(marker)
    })

    markerList.push(marker)

    // marker.addTo(map)
    bounds.extend([wgs84Coord[1], wgs84Coord[0]])
    markerCluster.addLayer(marker) // 添加到聚合组而不是地图
  })

  // 添加地图点击事件，处理空白处点击
  map.on('click', () => {
    showDetail.value = false
    if (currentSelectedMarker) {
      currentSelectedMarker._icon.classList.remove('active')
      currentSelectedMarker = null
    }
  })

  map.fitBounds(bounds, {
    padding: [50, 50],
    maxZoom: 16,
    animate: true,
  })
}

// 清除所有标记
const clearAllMarkers = () => {
  if (markerCluster) {
    markerCluster.clearLayers()
  }

  markerList.length = 0
}

const markerClick = (marker) => {
  getMarkerInfo(marker.options.id, marker.options.deviceType)

  if (currentSelectedMarker) {
    currentSelectedMarker._icon.classList.remove('active')
  }
  marker._icon.classList.add('active')
  currentSelectedMarker = marker
}

// 获取设备详细信息
const deviceData = ref<any>({})
const shangqingConfig = ref<any[]>([])
const getMarkerInfo = async (deviceId: number, deviceType: number) => {
  const baseData = await DeviceMapAPI.base(deviceId)
  const detailData = await DeviceMapAPI.detail(deviceId, deviceType)
  Promise.all([baseData, detailData]).then(() => {
    const { serialNum, address, online, type, typeName, coordinate } = baseData
    deviceData.value = { ...detailData, serialNum, address, online, type, typeName, coordinate }
    if (type === 4) startPlay()
  })
  showDetail.value = true
  if (deviceType === 2) {
    const res = await DeviceMapAPI.param(deviceId)
    shangqingConfig.value = res.filter((item) => item.enabled)
  }
}

// 开始播放
// const videoObj = ref()
const videoObjUrl = ref()
const monitorLoading = ref(false)
function startPlay() {
  monitorLoading.value = true
  MonitorAPI.gbPlaybackStart(deviceData.value.deviceId, deviceData.value.channelId)
    .then((res) => {
      // console.log(res)
      // videoObj.value = res
      // 判断当前页面的协议是否为https
      if (document.location.protocol === 'https:') {
        // 如果是https协议，使用https相关的视频地址
        videoObjUrl.value = res.https_flv
      } else {
        // 如果是http协议，使用http相关的视频地址
        videoObjUrl.value = res.flv
      }
    })
    .catch((err) => {
      console.log(err, 'errrrrrrrrrr')
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

// 定义计算属性，将 usedSpace 转换为 GB
const usedSpaceInGB = computed(() => {
  const usedSpace = deviceData.value?.usedSpace || 0
  return (usedSpace / 1024 / 1024 / 1024).toFixed(0) // 保留两位小数
})
// 定义计算属性，将 totalSpace 转换为 GB
const totalSpaceInGB = computed(() => {
  // totalSpaceInGB
  const totalSpace = deviceData.value?.totalSpace || 0
  return (totalSpace / 1024 / 1024 / 1024).toFixed(0) // 保留两位小数
})

// 定义一个计算属性将分钟转换为天、小时和分钟
const runningTimeDisplay = computed(() => {
  const minutes = deviceData?.value.uptime
  if (!minutes || minutes == 0) return 0

  const days = Math.floor(minutes / (24 * 60))
  const remainingMinutesAfterDays = minutes % (24 * 60)
  const hours = Math.floor(remainingMinutesAfterDays / 60)
  const finalMinutes = remainingMinutesAfterDays % 60

  let display = ''
  if (days > 0) {
    display += `${days}天`
  }

  if (hours > 0) {
    display += `${hours}小时`
  }

  if (finalMinutes > 0 || (!days && !hours)) {
    display += `${finalMinutes}分钟`
  }

  return display
})

// 设备统计
const statistics = ref<DeviceCount[]>([])
const getDeviceCount = async () => {
  const res = await DeviceMapAPI.statistics(customerId.value as number)
  statistics.value = res
  let alarmTotal = 0
  res.forEach((item) => {
    alarmTotal += item.alarmCount || 0
  })
  statistics.value.push({
    typeName: '告警数量',
    total: alarmTotal,
  })
}

// 扫码
const showScan = ref(false)
const scanDevice = (result: string) => {
  showScan.value = false
  if (!result) return

  const urlObj = new URLSearchParams(result.split('?')[1])
  const serialNum = urlObj.get('serialNum') || ''
  if (!serialNum) {
    Taro.showModal({
      title: '提示',
      content: '二维码格式不正确，请检查二维码',
      showCancel: false,
    })
    return
  }
  console.log(markerList, serialNum)
  const marker = markerList.find((item) => item.options.serialNum === serialNum)
  if (marker) {
    map.flyTo(marker.getLatLng())
    markerClick(marker)
  } else {
    Taro.showModal({
      title: '提示',
      content: '未找到该设备',
      showCancel: false,
    })
  }
}

// 获取当前定位
const getUserPosition = () => {
  const geolocation =
    navigator.geolocation || window.Geolocation || window.webkitGeolocation || window.mozGeolocation

  if (geolocation) {
    Taro.showLoading({
      title: '定位中...',
    })
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords
        const result = gcoord.transform(
          [longitude, latitude], // 经纬度坐标
          gcoord.WGS84, // 当前坐标系
          gcoord.BD09 // 目标坐标系
        )
        map.flyTo([result[1], result[0]], DEFAULT_ZOOM)

        Taro.hideLoading()
      },
      () => {
        Taro.showModal({
          title: '提示',
          content: '获取定位失败',
          showCancel: false,
        })
        Taro.hideLoading()
      }
    )
  } else {
    Taro.hideLoading()
    Taro.showModal({
      title: '提示',
      content: '当前浏览器不支持定位获取',
      showCancel: false,
    })
  }
}

/**
 * 跳转高德地图
 * */
function openGMap(data) {
  const { longitude: lonEnd, latitude: latEnd } = data.coordinate
  const destination = data.address
  window.location.href = `https://uri.amap.com/marker?position=${lonEnd},${latEnd}&name=${destination}&src=mypage&coordinate=wgs84&callnative=1`
}
</script>

<template>
  <Scanner v-if="showScan" @scanResult="scanDevice" />
  <div v-show="!showScan">
    <div class="container">
      <div class="choose">
        <nut-searchbar
          v-model="customerName"
          @change="search"
          @clear="showDropdown = true"
          @focus="showDropdown = true"
          placeholder="输入关键词搜索客户"
        >
          <template #rightin>
            <Search2 />
          </template>
        </nut-searchbar>

        <!-- 下拉选择列表 -->
        <div v-if="showDropdown" class="dropdown-list">
          <div
            v-for="item in dropdownList"
            :key="item.id"
            class="dropdown-item"
            @click="customerSelect(item)"
          >
            {{ item.name }}
          </div>
          <div v-if="dropdownList.length === 0" class="dropdown-empty">暂无数据</div>
        </div>
      </div>

      <div ref="mapContainer" class="map-container" />
    </div>

    <div class="right-buttons">
      <image src="../../assets/icon_shaomiao.png" @click="showScan = true"> </image>
      <image src="../../assets/icon_shaixuan.png" @click="getUserPosition"> </image>
    </div>

    <div class="bottom-total" v-if="!showDetail">
      <div class="bottom-total-item" v-for="item in statistics" :key="item.type">
        <div class="count total-count">{{ item.total }}</div>
        <div>{{ item.type === 2 ? '墒情' : item.typeName }}</div>
      </div>
    </div>

    <div v-else class="info-box">
      <nut-collapse v-model="unfold">
        <nut-collapse-item name="name1">
          <template #icon>
            <image class="icon-more" src="../../assets/icon_more.png"> </image>
          </template>
          <template #title>
            <div class="info-box-title">
              <image class="left" :src="infoIconMap[deviceData.type]" alt="" />
              <div class="right">
                <div>
                  <div class="device-number">
                    {{ deviceData.type === 4 ? deviceData.name : deviceData.typeName }}
                  </div>
                  <div class="device-address">
                    <div class="flex justify-between">
                      <span>{{ deviceData.serialNum }}</span>
                      <span class="device-status" :class="deviceData.online ? '' : 'offline'">{{
                        deviceData.online ? '在线' : '离线'
                      }}</span>
                    </div>
                    <div>
                      <span class="vertical-mid">{{ deviceData.address }}</span>
                      <span
                        @click.stop="openGMap(deviceData)"
                        class="ml-1 inline-flex items-center vertical-mid gap-x-0.5"
                        text="#1688d1 2.4"
                      >
                        <image src="@/assets/deviceMap/locate.png" alt="" class="w-2.4 h-2.5" />
                        导航
                      </span>
                    </div>
                  </div>
                </div>
                <div></div>
              </div>
            </div>
          </template>
          <div
            v-if="deviceData.type === 1 && !deviceData.serialNum.includes('MB')"
            class="info-content"
          >
            <div class="parameter">
              <div class="label">电压</div>
              <div class="value">{{ deviceData.voltage || '--' }} V</div>
            </div>
            <div class="parameter">
              <div class="label">电池电量</div>
              <div class="value">{{ deviceData.batteryLevel || '--' }} %</div>
            </div>
            <div class="parameter">
              <div class="label">电流</div>
              <div class="value">
                <span v-if="deviceData?.current !== null && deviceData?.current !== undefined">
                  {{
                    deviceData?.current > 0
                      ? `${deviceData?.current} A (充电)`
                      : `${deviceData?.current} A (放电)`
                  }}
                </span>
                <span v-else>--</span>
              </div>
            </div>
            <div class="parameter">
              <div class="label">灯电流</div>
              <div class="value">
                {{
                  deviceData.lightCurrent
                    ? deviceData.lightCurrent
                    : deviceData.lightCurrent === 0
                    ? 0
                    : '--'
                }}
                A
              </div>
            </div>
            <div class="parameter">
              <div class="label">风机电流</div>
              <div class="value">
                {{
                  deviceData.fanCurrent
                    ? deviceData.fanCurrent
                    : deviceData.fanCurrent === 0
                    ? 0
                    : '--'
                }}
                A
              </div>
            </div>
            <div class="parameter">
              <div class="label">太阳能板电压</div>
              <div class="value">
                {{
                  deviceData?.solarPanelsCurrent === null ||
                  deviceData?.solarPanelsCurrent === undefined
                    ? '--'
                    : `${deviceData?.solarPanelsCurrent} V`
                }}
              </div>
            </div>
            <div class="parameter">
              <div class="label">充电功率</div>
              <div class="value">
                {{ deviceData.chargeW ? deviceData.chargeW : deviceData.chargeW === 0 ? 0 : '--' }}
                W
              </div>
            </div>
            <div class="parameter">
              <div class="label">充电电量</div>
              <div class="value">{{ deviceData.chargeAh || '--' }} Ah</div>
            </div>
            <div class="parameter">
              <div class="label">光控</div>
              <div class="value">
                {{
                  deviceData?.lightControl === 0
                    ? '夜晚'
                    : deviceData?.lightControl === 1
                    ? '白天'
                    : deviceData?.lightControl === 2
                    ? '正常'
                    : '--'
                }}
              </div>
            </div>
            <div class="parameter">
              <div class="label">雨控</div>
              <div class="value">
                {{
                  deviceData?.rainControl === 0
                    ? '晴天'
                    : deviceData?.rainControl === 1
                    ? '雨天'
                    : deviceData?.rainControl === 2
                    ? '正常'
                    : '--'
                }}
              </div>
            </div>
            <div class="parameter">
              <div class="label">网络状态</div>
              <div class="value">
                {{
                  deviceData?.networkType === 1
                    ? '2G'
                    : deviceData?.networkType === 2
                    ? '2.5G'
                    : deviceData?.networkType === 3
                    ? '3G'
                    : deviceData?.networkType === 4
                    ? '4G'
                    : deviceData?.networkType === 5
                    ? '5G'
                    : '--'
                }}
                <image
                  v-if="deviceData?.signalQuality || deviceData?.signalQuality === 0"
                  :src="
                    deviceData?.signalQuality <= 0
                      ? xinhao
                      : xinhaoMap[Math.min(Math.ceil(deviceData?.signalQuality / 6), 5)]
                  "
                  class="w-3 h-2"
                />
              </div>
            </div>
            <div class="parameter">
              <div class="label">温度</div>
              <div class="value">{{ deviceData.temperature || '--' }} ℃</div>
            </div>
            <div class="parameter">
              <div class="label">湿度</div>
              <div class="value">{{ deviceData.humidity || '--' }} %</div>
            </div>

            <div class="parameter">
              <div class="label">灯状态</div>
              <div class="value">
                <span :class="deviceData?.light ? 'text-#019E59' : 'text-#909090'">
                  {{ deviceData?.light ? '开' : '关' }}
                </span>
              </div>
            </div>

            <div class="parameter">
              <div class="label">跌倒状态</div>
              <div class="value">
                <span
                  :class="
                    deviceData?.dump === true
                      ? 'text-#FF1D1D'
                      : deviceData?.dump === false
                      ? 'text-#019E59'
                      : ''
                  "
                >
                  {{
                    deviceData?.dump === true ? '异常' : deviceData?.dump === false ? '正常' : '--'
                  }}
                </span>
              </div>
            </div>
            <div class="parameter">
              <div class="label">定位方式</div>
              <div class="value">
                {{
                  deviceData?.locateMode === 0
                    ? '无效定位'
                    : deviceData?.locateMode === 1
                    ? '卫星定位'
                    : deviceData?.locateMode === 2
                    ? '基站定位'
                    : '--'
                }}
              </div>
            </div>

            <div class="parameter">
              <div class="label">累计杀虫数量</div>
              <div class="value">{{ deviceData?.killCount || '--' }}</div>
            </div>
            <!-- <div class="parameter">
            <div class="label">本次开机时长</div>
            <div class="value">{{ runningTimeDisplay || '--' }}</div>
          </div>  -->
            <div class="parameter">
              <div class="label">数据更新时间</div>
              <div class="value time">{{ deviceData.time || '--' }}</div>
            </div>
          </div>
          <div
            class="text-center"
            v-if="deviceData.type === 1 && deviceData.serialNum.includes('MB')"
          >
            MB型号暂无参数数据
          </div>
          <div v-if="deviceData.type === 2" class="info-content">
            <scroll-view
              style="max-height: 220px; gap: 5px"
              class="flex flex-wrap"
              :scroll-y="true"
            >
              <div v-for="item in shangqingConfig" class="parameter" :key="item.paramCode">
                <span class="label">{{ item.paramName }}</span>
                <span class="value">{{ deviceData[item.columnName] || '--' }} {{ item.unit }}</span>
              </div>
            </scroll-view>
          </div>
          <div v-if="deviceData.type === 3" class="info-content flex flex-wrap">
            <div class="parameter">
              <span class="label">电量</span>
              <span class="value">
                <span v-if="deviceData?.batteryLevel" class="text-#019E59">{{
                  deviceData?.batteryLevel + ' %'
                }}</span>
                <span v-else-if="deviceData?.batteryLevel === 0">0</span>
                <span v-else>--</span>
              </span>
            </div>
            <div class="parameter">
              <span class="label">储存空间</span>
              <span class="value">
                <span>
                  {{ usedSpaceInGB }} /
                  {{ totalSpaceInGB }}
                  GB
                </span>
              </span>
            </div>
            <div class="parameter">
              <span class="label">累计拍照次数</span>
              <span class="value">
                {{ deviceData?.snapCount || '--' }}
              </span>
            </div>
            <div class="parameter">
              <span class="label">本次运行时间</span>
              <span class="value">
                {{ runningTimeDisplay || '--' }}
              </span>
            </div>

            <div class="parameter">
              <span class="label">最近拍照时间</span>
              <span class="value time">
                {{ deviceData?.lastSnapTime || '2025-03-04 12:12:12' }}
              </span>
            </div>
            <div class="parameter">
              <span class="label">数据更新时间</span>
              <span class="value time">
                {{ deviceData?.time || '--' }}
              </span>
            </div>
          </div>
          <div v-if="deviceData.type === 4">
            <div v-if="monitorLoading" class="LivePlayerDiv">视频加载中...</div>

            <Player
              v-else
              class="LivePlayerDiv"
              :video-url="videoObjUrl"
              :loading="monitorLoading"
            />
          </div>
        </nut-collapse-item>
      </nut-collapse>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dropdown-list {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  width: 100%;
  max-height: 600px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
  margin-top: 0px;
}

.dropdown-item {
  padding: 20px 0 20px 80px;
  font-size: 28px;
  border-bottom: 1px solid #f5f5f5;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.dropdown-empty {
  height: 100px;
  text-align: center;
  line-height: 100px;
  font-size: 28px;
  color: #909090;
}
.choose {
  display: flex;
  align-items: center;
  position: relative;
  background: #fff;
  span {
    width: 70px !important;
    font-size: 28px;
  }
}
.LivePlayerDiv {
  width: 100%;
  height: 380px;
  margin: 10px auto 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  color: #fff;
}
.container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
  width: 100vw;
  overflow: hidden;
}
.map-container {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.right-buttons {
  position: absolute;
  top: 100px;
  right: 0;
  display: flex;
  flex-direction: column;
  image {
    width: 110px;
    height: 110px;
  }
}

.bottom-total {
  position: absolute;
  bottom: 170px;
  left: 20px;
  width: calc(100vw - 40px);
  height: 190px;
  background: #ffffff;
  box-shadow: 0px 10px 16px 1px rgba(120, 129, 149, 0.2);
  border-radius: 20px;
  display: flex;
  justify-content: space-around;
  .bottom-total-item {
    font-size: 28px;
    color: #303030;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .total-count {
      font-size: 38px;
      color: #02a15b;
    }
    &:last-child {
      .total-count {
        color: #ff1d1d;
      }
    }
    // .count {
    //   font-size: 45px;
    //   color: #303030;
    // }
    // .total-count {
    //   color: #101010;
    // }
    // .online-count {
    //   color: #02a15b;
    // }
    // .alarm-count {
    //   color: #ffa200;
    // }
  }
}
.info-box {
  width: calc(100vw - 40px);
  // height: 779px;
  background: #ffffff;
  box-shadow: 0px 10px 16px 1px rgba(120, 129, 149, 0.2);
  border-radius: 20px 20px 20px 20px;
  position: absolute;
  bottom: 170px;
  left: 20px;
  padding: 40px 40px 0;
  box-sizing: border-box;
  display: flex;
  .info-box-title {
    display: flex;
    width: 100%;
    .left {
      width: 60px;
      height: 60px;
      margin-right: 20px;
      margin-top: 6px;
    }
    .right {
      display: flex;
      flex: 1;
      justify-content: space-between;
      > div:nth-child(2) {
        flex-shrink: 0;
      }
      .device-number {
        margin: 8px 0 10px;
        font-size: 32px;
        color: #000000;
        font-weight: bold;
      }
      .device-address {
        font-size: 28px;
        color: #909090;
      }
      .device-status {
        position: relative;
        left: 30px;
        color: #019e59;
        &::before {
          content: '';
          display: block;
          width: 12px;
          height: 12px;
          background: #019e59;
          border-radius: 50%;
          position: absolute;
          left: -25px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .offline {
        color: #909090;
        &::before {
          background: #909090;
        }
      }
    }
  }
  .info-content {
    width: 630px;
    border-radius: 10px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .label {
      color: #909090;
      white-space: nowrap;
    }
    .value {
      color: #019f59;
      text-align: center;
    }
    .time {
      padding: 0 35px;
    }
    .parameter {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: #f8f8f8;
      font-size: 24px;
      width: calc((100% - 20px) / 3);
      padding: 10px 0;
    }
  }
}
.icon-more {
  width: 30px;
  height: 18px;
}
:deep(.nut-collapse-item__title) {
  padding: 0 !important;
  margin-bottom: 30px;
}
:deep(.nut-collapse-item__border .nut-collapse-item__title::after) {
  border: none;
}
:deep(.nut-collapse__item-wrapper) {
  margin-top: 20px;
  border-top: 1px solid #e7e9ee;
}
:deep(.nut-collapse) {
  width: 100%;
  padding-bottom: 30px;
}
:deep(.nut-collapse-item__title-icon) {
  position: relative;
  top: -70px;
}
:deep(.nut-collapse__item-wrapper__content) {
  padding: 34px 0 !important;
}
</style>
