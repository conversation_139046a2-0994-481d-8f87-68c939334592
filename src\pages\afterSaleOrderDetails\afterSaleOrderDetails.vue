<template>
  <PositionSelect
    v-if="showPositionSelect"
    :showSearch="false"
    @close="showPositionSelect = false"
    :modelValue="coordinate"
  ></PositionSelect>
  <div v-else class="page-content">
    <CustomNavTitle title="售后工单详情" />

    <scroll-view class="scroll-view" scroll-y>
      <div class="card">
        <div class="title">基础信息</div>
        <div class="content">
          <div>
            <span class="label">工单号</span>
            <span class="value">{{ info.workOrderNo || '--' }}</span>
          </div>
          <div>
            <span class="label">设备名称</span>
            <span class="value">{{ info?.deviceTypeName || '--' }}</span>
          </div>
          <div>
            <span class="label">安装日期</span>
            <span class="value">{{ info?.installDate || '--' }}</span>
          </div>
          <div>
            <span class="label">质保期</span>
            <span class="value">{{ info?.order?.warrantyPeriodDate || '--' }}</span>
          </div>
          <div>
            <span class="label">设备编号</span>
            <span class="value">{{ info?.deviceRecordList?.[0]?.deviceNo || '--' }}</span>
          </div>
          <div>
            <span class="label">问题类型</span>
            <span class="value">{{ info?.issueTypeName || '--' }}</span>
          </div>
          <div>
            <span class="label">问题描述</span>
            <span class="value">{{ info?.issueRemark }}</span>
          </div>
          <div>
            <span class="label">提交日期</span>
            <span class="value">{{ info?.createTime || '--' }}</span>
          </div>
          <div v-if="info?.deviceRecordList?.[0]?.address">
            <span class="label">安装位置</span>
            <span class="value btn" @click="showAddress()">{{
              info?.deviceRecordList?.[0]?.address || '--'
            }}</span>
          </div>

          <div class="pt-1.4 !flex-col">
            <span class="label">故障图片</span>
            <div class="value img-list justify-end">
              <div v-if="info.imageList?.length" class="flex flex-wrap gap-2">
                <VImage
                  class="w-11 h-11"
                  v-for="url in info.imageList.map((item) => item.url)"
                  :key="url"
                  :src="url"
                  :preview-src-list="info.imageList.map((item) => item.url)"
                />
              </div>
              <span v-else class="value">无</span>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="title">客户信息</div>
        <div class="content">
          <div>
            <span class="label">客户</span>
            <span class="value">{{ info.customerName || '--' }}</span>
          </div>
          <div>
            <span class="label">联系人</span>
            <span class="value">{{ info?.contacts || '--' }}</span>
          </div>
          <div>
            <span class="label">联系电话</span>
            <span class="value">{{ info?.contactsPhone || '--' }}</span>
          </div>
          <div>
            <span class="label">地址</span>
            <span class="value">{{ info?.address || '--' }}</span>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="title">维修信息</div>

        <div class="sub-title">问题类型</div>

        <nut-input
          v-model="form.issueTypeName"
          readonly
          placeholder="请选择"
          @click="
            () => {
              if (info.status === 2) showPicker = true
            }
          "
        ></nut-input>

        <nut-popup v-model:visible="showPicker" position="bottom" :style="{ height: '300px' }">
          <nut-picker
            :columns="deviceIssue"
            title="问题类型选择"
            @cancel="showPicker = false"
            @confirm="confirm"
          />
        </nut-popup>

        <div class="sub-title">问题描述</div>
        <nut-textarea
          :autosize="{
            maxHeight: 160,
            minHeight: 60,
          }"
          v-model="form.handlerRemark"
          placeholder="请输入问题描述"
        >
        </nut-textarea>

        <div class="results">
          <span>处理结果</span>

          <nut-radio-group
            v-if="info.status === 2"
            v-model="form.handlerStatus"
            direction="horizontal"
          >
            <nut-radio :label="1" icon-size="14">已解决</nut-radio>
            <nut-radio :label="0" icon-size="14">未解决</nut-radio>
          </nut-radio-group>
          <span v-else>{{ form.handlerStatus === 1 ? '已解决' : '未解决' }}</span>
        </div>

        <div class="sub-title">
          <span>完成图片</span>
          <span class="upload-tip">最多3张</span>
        </div>
        <div class="upload-list">
          <FileUpload v-if="info.status === 2" v-model="fileList" :limit="3" />
          <VImage v-else v-for="item in form.handlerImage" :key="item.index" :src="item.url" />
          <div v-if="info.status === 4 && !form.handlerImage?.length" class="no-image">
            暂无图片
          </div>
        </div>
      </div>
    </scroll-view>

    <div class="bottom-buttons gap-x-4">
      <nut-button class="flex-1" type="default" @click="handleCancel">取消</nut-button>
      <nut-button
        v-if="info.status === 2 && auth('mobile:home:workOrder:afterSale:handle')"
        type="primary"
        class="flex-1"
        @click="handleSubmit"
        >确定</nut-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { ref } from 'vue'
import WorkOrderAPI from '../../api/workOrder'
import FileUpload from '../../components/Upload/fileUpload.vue'
import { FileAPI } from '../../api/file'
import { auth } from '@/store/permisson'

const info = ref<any>({})
const id = Taro.getCurrentInstance().router?.params?.id

const getInfo = async () => {
  const res = await WorkOrderAPI.getDetail(Number(id))
  info.value = res
  const { deviceWorkOrderId, deviceId, deviceRecordList, handlerStatus } = res
  const { issueType, issueTypeName, handlerRemark, handlerImage } = deviceRecordList[0]
  console.log(deviceRecordList[0])
  form.value = {
    deviceWorkOrderId,
    deviceId,
    handlerImage,
    handlerRemark,
    issueType,
    issueTypeName,
    handlerStatus: handlerStatus === 0 ? handlerStatus : 1,
  }
}

getInfo()

const coordinate = ref<any>()
const showAddress = () => {
  showPositionSelect.value = true
  coordinate.value = info.value?.deviceRecordList[0]?.coordinate
}

const fileList = ref<any[]>([])

const form = ref<any>({})

const deviceIssue = ref<any[]>([])
const showPicker = ref(false)
const getdeviceIssue = async () => {
  const res = await WorkOrderAPI.deviceIssue()
  deviceIssue.value = res.map((item) => ({
    value: item.value,
    text: item.label,
  }))
}
const confirm = (selectedValue) => {
  showPicker.value = false
  form.value.issueType = selectedValue.selectedOptions?.[0]?.value
  form.value.issueTypeName = selectedValue.selectedOptions?.[0]?.text
}
getdeviceIssue()

const submitLoading = ref(false)
const handleSubmit = async () => {
  if (!form.value.issueType || !form.value.issueTypeName) {
    Taro.showToast({
      title: '请选择问题类型',
      icon: 'none',
    })
    return
  }
  if (!form.value.handlerRemark) {
    Taro.showToast({
      title: '请输入问题描述',
      icon: 'none',
    })
    return
  }
  form.value.handlerImage = fileList.value.map((item) => {
    return {
      name: item.name,
      url: item.url,
    }
  })

  const { id, deviceRecordList } = info.value
  Object.assign(form.value, {
    deviceWorkOrderId: id,
    deviceId: deviceRecordList?.[0]?.deviceId,
  })

  submitLoading.value = true
  try {
    await WorkOrderAPI.completeAfterSales(form.value)
    Taro.showToast({
      title: '操作成功',
      icon: 'success',
    })
    setTimeout(() => {
      handleCancel()
    }, 2000)
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  Taro.navigateBack()
}

FileAPI.init()

// 地图查看相关功能
const showPositionSelect = ref(false)
</script>

<style scoped lang="scss">
image {
  width: 110px;
  height: 110px;
}
.bottom-buttons {
  width: 100%;
  padding: 0 30px;
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  bottom: 50px;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-between;
  .nut-button {
    width: 300px !important;
  }
  .one-button {
    width: 100% !important;
  }
}
.card {
  width: 690px;
  background: #ffffff;
  border-radius: 20px;
  padding: 40px 30px;
  margin: 30px auto;
  box-sizing: border-box;
  .title {
    height: 75px;
    border-bottom: 1px solid #e7e9ee;
    display: flex;
    justify-content: space-between;
    font-size: 32px;
    font-weight: bold;
    align-items: center;
    padding-bottom: 30px;
    box-sizing: border-box;
    margin-bottom: 35px;
    .count {
      color: #019e59;
    }
  }
  .sub-title {
    margin: 35px 0 25px;
    font-size: 28px;
    color: #5b5b5b;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .upload-tip {
      color: #909090;
      font-size: 24px;
    }
  }

  .results {
    font-size: 28px;
    color: #5b5b5b;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 35px;
  }

  .content {
    font-size: 28px;

    > div {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
    }
    .span2 {
      display: flex;
      justify-content: space-between;
      > div {
        display: flex;
      }
    }
    > div {
      display: flex;
    }
    .label {
      flex-shrink: 0;
      color: #909090;
      width: 120px;
      //text-align: justify;
      //text-align-last: justify;
      margin-right: 8px;
    }
    .value {
      color: #101010;
      text-align: end;
    }
    .btn {
      color: #019e59;
    }
  }
  .custom-button {
    font-size: 28px;
    color: #019e59;
    width: 100px;
    height: 46px;
    background: #ecfcf1;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #019e59;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

:deep(.nut-radio__label) {
  font-size: 28px !important;
  color: #101010;
}
:deep(.nut-input) {
  box-sizing: border-box;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  padding: 10px 20px;
  .nut-input-inner {
    height: 58px;
  }
}
:deep(.nut-textarea) {
  border: 1px solid #d4d4d4;
}
.scroll-view {
  height: calc(100vh - 180px);
  .device-item {
    width: 627px;
    height: 78px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #d4d4d4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 35px;
    padding: 30px 20px;
    box-sizing: border-box;
    font-size: 28px;
    .status {
      color: #019e59;
      &.uninstalled {
        color: #909090;
      }
    }
  }
}
.upload-list {
  width: 630px;
  height: 166px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d4d4d4;
  margin-top: 24px;
  padding: 28px;
  box-sizing: border-box;
  font-size: 28px;
}
.no-image {
  color: #333;
  margin-top: 30px;
  text-align: center;
}
:deep(.nut-textarea__textarea) {
  color: #000 !important;
}
:deep(.nut-textarea) {
  padding-left: 20px;
  padding-right: 20px;
}
</style>
