import request from '../utils/request'

export default class DeviceMapAPI {
  // 获取所有客户列表
  static listCustomer() {
    return request<DeviceCount[]>({
      url: '/api/v1/manage/h5/customer/listCustomer',
      method: 'get',
    })
  }

  // 设备统计
  static statistics(customerId: number) {
    return request<DeviceCount[]>({
      url: '/api/v1/manager/h5/device_map/statistics',
      method: 'get',
      params: { type: 4, customerId },
    })
  }

  // 点位列表
  static mapList(customerId: number) {
    return request<MapPointVo[]>({
      url: '/api/v1/manager/h5/device_map/mapList',
      method: 'get',
      params: { type: 4, customerId },
    })
  }

  // 地块列表
  static farmland(customerId: number) {
    return request<Farmland[]>({
      url: '/api/v1/manager/h5/device_map/listFarmland',
      method: 'get',
      params: { customerId },
    })
  }

  // 获取设备信息
  static base(deviceId: number) {
    return request<any>({
      url: `/api/v1/manage/h5/device/${deviceId}/base`,
      method: 'get',
    })
  }

  // 获取设备详情
  static detail(deviceId: number, deviceType: number) {
    return request<any>({
      url: `/api/v1/manage/h5/device/${deviceId}/detail`,
      method: 'get',
      params: { deviceType },
    })
  }

  // 列出参数配置信息
  static param(deviceId: number) {
    return request<any>({
      url: `/api/v1/manage/h5/device/smd/${deviceId}/param`,
      method: 'get',
    })
  }
}
/**
 * 地块实体
 *
 * Farmland
 */
export interface Farmland {
  /**
   * 地块地址
   */
  address?: string
  /**
   * 区域中心点位
   */
  coordinate?: GeoPoint
  createBy?: number
  createTime?: string
  /**
   * 客户 ID
   */
  customerId?: number
  deleted?: boolean
  /**
   * 描述
   */
  description?: string
  id?: number
  modifyBy?: number
  modifyTime?: string
  /**
   * 地块面积(亩)
   */
  mu?: number
  /**
   * 地块名称
   */
  name?: string
  /**
   * 区域范围
   */
  range: GeoPolygon
  /**
   * 负责人
   */
  respPerson?: string
  /**
   * 负责人联系电话
   */
  respPhone?: string
  [property: string]: any
}

/**
 * 区域中心点位
 *
 * GeoPoint
 *
 * com.vankeytech.wuhua.plugin.mybatis.type.GeoPoint
 */
export interface GeoPoint {
  latitude?: number
  longitude?: number
}

/**
 * 区域范围
 *
 * GeoPolygon
 */
export interface GeoPolygon {
  coordinates?: GeoPoint[]
  [property: string]: any
}

/**
 * com.vankeytech.wuhua.model.vo.MapPointVo
 *
 * MapPointVo
 */
export interface MapPointVo {
  /**
   * code
   */
  code?: string
  deviceCount?: number
  deviceType?: DeviceType
  deviceTypeName?: string
  /**
   * id
   */
  id?: number
  /**
   * 监测数据
   */
  monitorData?: Array<Array<{ [key: string]: any }[]>>
  /**
   * 名称
   */
  name?: string
  /**
   * 位置信息
   */
  point?: GeoPoint
  /**
   * type  1 省 2 市 3 区县  4 设备
   */
  pointType?: number
  /**
   * 1 正常 0 离线 2 正常开灯，正常关灯
   */
  status?: number
  [property: string]: any
}

export enum DeviceType {
  Camera = 'CAMERA',
  ISA = 'ISA',
  Ikl = 'IKL',
  Smd = 'SMD',
}

/**
 * 位置信息
 *
 * GeoPoint
 */
export interface GeoPoint {
  latitude?: number
  longitude?: number
  [property: string]: any
}

export interface DeviceCount {
  /**
   * 告警数量
   */
  alarmCount?: number
  childList?: DeviceCount[]
  /**
   * 没有销售数据
   */
  notSaleCount?: number
  /**
   * 离线数量
   */
  offlineCount?: number
  /**
   * 离线率
   */
  offlineRate?: number
  /**
   * 在线数量
   */
  onlineCount?: number
  /**
   * 在线率
   */
  onlineRate?: number
  /**
   * 已售
   */
  saleCount?: number
  /**
   * 总数
   */
  total?: number
  totalRate?: number
  /**
   * 名称id
   */
  type?: number
  /**
   * 名称
   */
  typeName?: string
  [property: string]: any
}
