import { createApp } from 'vue'
import './app.scss'
import 'uno.css'
import Taro from '@tarojs/taro'
import { FileAPI } from '@/api/file'
import VConsole from 'vconsole'
import { setupStore } from '@/store'
import { useUserStore } from '@/store/user'
import '@/assets/styles/index.scss'

// const vConsole = new VConsole()

const App = createApp({
  onShow(options) {},
  // 系统启动时触发
  onLaunch() {
    const token = Taro.getStorageSync('token')
    if (!token) {
      Taro.navigateTo({
        url: '/pages/login/index',
      })
      return
    }

    const userStore = useUserStore()
    userStore.getUserInfo()
    FileAPI.init()
  },
  // 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
})

setupStore(App)

export default App
