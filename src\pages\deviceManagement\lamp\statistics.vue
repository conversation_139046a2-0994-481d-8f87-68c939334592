<template>
  <div class="page-content">
    <CustomNavTitle title="参数统计" :showBack="true" background="#fff" />

    <div class="content">
      <div class="content-titlt">
        <div style="width: 68%">
          <CustomTabs v-model="status" :tab-list="tabList" />
        </div>
        <TabCardDate
          v-model:tab-list="tabListDate"
          class="timeTab"
          @change="handleChangeDateTab($event, 0)"
        />
        <div class="content-line"></div>
      </div>
      <div class="dateInput" @click="openTimePop(0)">
        <div>{{ date }}</div>
        <IconFont name="rect-down" size="12" color="#707070"></IconFont>
      </div>
      <Chart v-if="dataY0?.length > 0" ref="chartRef" :option="option0" class="chartsDiv" />
      <div class="marginTop-80" >
        <emput v-if="!dataY0 || dataY0?.length < 1"  message="暂无数据" />
      </div>
      <!-- <div v-if="!dataY0 || dataY0?.length < 1" class="charts-nodata">暂无数据</div> -->
    </div>
    <div class="content">
      <div class="content-titlt">
        <div style="width: 68%">
          <CustomTabs v-model="status1" :tab-list="tabList1" />
        </div>
        <TabCardDate
          v-model:tab-list="tabListDate"
          class="timeTab"
          @change="handleChangeDateTab($event, 1)"
        />
        <div class="content-line"></div>
      </div>
      <div class="dateInput" @click="openTimePop(1)">
        <div>{{ date1 }}</div>
        <IconFont name="rect-down" size="12" color="#707070"></IconFont>
      </div>
      <Chart
        v-if="dataY?.length > 0 && tabList1[status1].type !== 'temperature-humidity'"
        ref="chartRef"
        :option="option"
        class="chartsDiv"
      />
      <Chart
        v-if="dataY && dataY.length > 0 && tabList1[status1].type === 'temperature-humidity'"
        ref="chartRef"
        :option="option1"
        class="chartsDiv"
      />
      <!-- <div v-if="!dataY || dataY?.length < 1" class="charts-nodata">暂无数据</div> -->
      <div class="marginTop-80" >
        <emput v-if="!dataY || dataY?.length < 1"  message="暂无数据" />
      </div>
    </div>
    <nut-popup v-model:visible="showPop" position="bottom">
      <nut-picker
        v-model="dateYear"
        v-if="chooseDeteType == 'year'"
        :columns="columns"
        title=""
        @confirm="confirm"
        @cancel="cancel"
      />
      <nut-date-picker
        v-else
        v-model="detePop"
        :type="chooseDeteType"
        :min-date="min"
        :max-date="max"
        :three-dimensional="false"
        @confirm="confirm"
        @cancel="cancel"
      ></nut-date-picker>
    </nut-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, watch } from 'vue'
import Taro from '@tarojs/taro'
import dayjs from 'dayjs'
import type { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import LampAPI from '../../../api/device/lamp'
import { IconFont } from '@nutui/icons-vue-taro'
import { set } from '@vueuse/core'

const currentYear = new Date().getFullYear()
const columns = ref([
  ...Array.from({ length: 30 }, (_, index) => ({
    text: `${currentYear - index}`,
    value: `${currentYear - index}`,
  })),
])

const deviceInfo = ref<any>({})

onMounted(() => {
  // 获取页面参数
  const params: any = Taro.getCurrentInstance().router?.params
  deviceInfo.value = JSON.parse(decodeURIComponent(params.data))
  console.log(deviceInfo.value, 'deviceId.value')
  // getChat('charging-current')
  // setTimeout(() => {
  //   handleType.value=1
  //   getChat('light');
  // }, 1000);
  getStaticData()
  setTimeout(() => {
    handleType.value = 1
    getStaticData()
  }, 500)
})

const loading = ref(false)

const handleType = ref(0) // 操作的第一个还是第二个echarts相关

const date = ref(dayjs(new Date()).format('YYYY-MM-DD'))
const date1 = ref(dayjs(new Date()).format('YYYY-MM-DD'))

const status = ref(0)
const tabList = ref([
  {
    title: '充电电流',
    paneKey: 0,
    count: '',
    type: 'charging-current',
    unit: 'A',
  },
  {
    title: '放电电流',
    paneKey: 1,
    count: '',
    type: 'discharge-current',
    unit: 'A',
  },
  {
    title: '电压',
    paneKey: 2,
    count: '',
    type: 'voltage',
    unit: 'V',
  },
])
const status1 = ref(0)
const tabList1 = ref([
  {
    title: '亮灯统计',
    paneKey: 0,
    count: '',
    type: 'light',
    unit: '小时',
  },
  {
    title: '杀虫数量',
    paneKey: 1,
    count: '',
    type: 'kill-count',
    unit: '只',
  },
  {
    title: '温湿度',
    paneKey: 2,
    count: '',
    type: 'temperature-humidity',
  },
])

const tabListDate = reactive([
  { id: 0, key: 'day', name: '日', type: 'daily' },
  { id: 1, key: 'month', name: '月', type: 'monthly' },
  { id: 2, key: 'year', name: '年', type: 'yearly' },
])

const tableIndexDate = ref(tabListDate[0].id)
const tableIndexDate1 = ref(tabListDate[0].id)

watch(status, (newStatus) => {
  handleType.value = 0
  setTimeout(() => {
    getStaticData()
  }, 500)
})

watch(status1, (newStatus) => {
  handleType.value = 1
  setTimeout(() => {
    getStaticData()
  }, 500)
})

// 根据静态与否请求数据
function getStaticData() {
  console.log(handleType.value, 'handleType.value')
  // if (deviceInfo.value.staticMode === true) {
  //   if (handleType.value == 0) {
  //     const obj = randomEhartsData(
  //       deviceInfo.value?.staticConfig[tabList.value[status.value].type][
  //         tabListDate[tableIndexDate.value].type
  //       ].rules,date.value,
  //       tabListDate[tableIndexDate.value].key
  //     )
  //     dataX0.value = obj.dataX
  //     dataY0.value = obj.dataY
  //   } else {
  //     if (tabList1.value[status1.value].type === 'temperature-humidity') {
  //       const obj = randomEhartsData(
  //         deviceInfo.value?.staticConfig.temperature[tabListDate[tableIndexDate1.value].type].rules,
  //         date1.value,
  //         tabListDate[tableIndexDate1.value].key
  //       )
  //       const obj1 = randomEhartsData(
  //         deviceInfo.value?.staticConfig.humidity[tabListDate[tableIndexDate1.value].type].rules,
  //         date1.value, tabListDate[tableIndexDate1.value].key
  //       )
  //       dataX.value = obj.dataX
  //       dataY.value = obj.dataY
  //       dataX1.value = obj1.dataX
  //       dataY1.value = obj1.dataY
  //     } else {
  //       const obj = randomEhartsData(
  //         deviceInfo.value?.staticConfig[tabList1.value[status1.value].type][
  //           tabListDate[tableIndexDate1.value].type
  //         ].rules, date1.value, tabListDate[tableIndexDate1.value].key
  //       )
  //       dataX.value = obj.dataX
  //       dataY.value = obj.dataY
  //     }
  //   }
  // } else {
  if (handleType.value === 0) {
    getChat(tabList.value[status.value].type)
  } else {
    getChat(tabList1.value[status1.value].type)
  }
  // }
}

// "rules": [
//         { "range": [6, 11], "x": [1, 3] },
//         { "range": [14, 28], "x": [4, 10] },
//         { "range": [11, 16], "x": [11, 12] }
//       ]
// // 根据规则生成随机数组
// function randomEhartsData(rules) {
//   // x 日期数组
//   // y 数据数组
//   const dataX1 = []
//   const dataY1 = []

//   rules.forEach((rule) => {
//     const [min, max] = rule.range
//     for (let i = rule.x[0]; i <= rule.x[1]; i++) {
//       dataX1.push(i)
//       // 在 range 范围内随机生成一个数字
//       const randomY = Math.floor(Math.random() * (max - min + 1)) + min
//       dataY1.push(randomY)
//     }
//   })
//   // 抛出值
//   return {
//     dataX: dataX1,
//     dataY: dataY1,
//   }
// }
// 根据规则生成随机数组
function randomEhartsData(rules, dateTime, timeType) {
  // const timeType = tabListDate[tableIndexDate.value].key
  // x 日期数组
  // y 数据数组
  const dataX1 = []
  const dataY1 = []
  let startValue = 0
  let endValue = 0

  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1
  const currentDay = now.getDate()

  const [selectedYear, selectedMonth, selectedDay] = dateTime.split('-').map(Number)

  if (timeType === 'day') {
    startValue = 0
    if (
      selectedYear === currentYear &&
      selectedMonth === currentMonth &&
      selectedDay === currentDay
    ) {
      endValue = now.getHours()
    } else {
      endValue = 23
    }
  } else if (timeType === 'month') {
    startValue = 1
    if (selectedYear === currentYear && selectedMonth === currentMonth) {
      endValue = now.getDate()
    } else {
      const nextMonth = new Date(selectedYear, selectedMonth, 0)
      endValue = nextMonth.getDate()
    }
  } else if (timeType === 'year') {
    startValue = 1
    if (selectedYear === currentYear) {
      endValue = now.getMonth() + 1
    } else {
      endValue = 12
    }
  }

  for (let i = startValue; i < endValue + 1; i++) {
    const found = false
    let lastMatchingRule = null
    rules.forEach((rule) => {
      if (i >= rule.x[0] && i <= rule.x[1]) {
        lastMatchingRule = rule
      }
    })

    if (lastMatchingRule) {
      const [min, max] = lastMatchingRule.range
      if (timeType === 'day') {
        // dataX1.push(`${i}:00:00`)
        // dataX1.push(`${i}时`)
        dataX1.push(`${i}`)
      } else if (timeType === 'month') {
        dataX1.push(`${i}日`)
      } else if (timeType === 'year') {
        dataX1.push(`${i}月`)
      }

      // 在 range 范围内随机生成一个数字
      const randomY = Math.floor(Math.random() * (max * 10 - min * 10 + 1) + min * 10) / 10
      dataY1.push(randomY)
      console.log(min, max, 'min, max', i, dataY1)
    } else {
      if (timeType === 'day') {
        // dataX1.push(`${i}:00:00`)
        // dataX1.push(`${i}时`)
        dataX1.push(`${i}`)
      } else if (timeType === 'month') {
        dataX1.push(`${i}日`)
      } else if (timeType === 'year') {
        dataX1.push(`${i}月`)
      }

      dataY1.push(0)
    }
  }

  // 抛出值
  return {
    dataX: dataX1,
    dataY: dataY1,
  }
}

// 移除重复声明的 chooseDeteType，因为在 handleChangeDateTab 函数上方已经有注释掉的声明
const chooseDeteType: Ref<string> = ref('date')

const dateYear = ref()

function handleChangeDateTab(item, type) {
  console.log(item, type)
  handleType.value = type
  if (type === 1) {
    tableIndexDate1.value = tabListDate[item].id
  } else {
    tableIndexDate.value = tabListDate[item].id
  }
  if (tabListDate[item].key === 'day') {
    if (type === 1) {
      date1.value = dayjs(new Date()).format('YYYY-MM-DD')
    } else {
      date.value = dayjs(new Date()).format('YYYY-MM-DD')
    }
  } else if (tabListDate[item].key === 'month') {
    if (type === 1) {
      date1.value = dayjs(new Date()).format('YYYY-MM')
    } else {
      date.value = dayjs(new Date()).format('YYYY-MM')
    }
  } else {
    if (type === 1) {
      date1.value = dayjs(new Date()).format('YYYY')
    } else {
      date.value = dayjs(new Date()).format('YYYY')
    }
  }
  // if(type===1){
  //   getChat(tabList1.value[status1.value].type) // 当 tableIndex 改变时，调用 getChat 方法
  // }else{
  //   getChat(tabList.value[status.value].type) // 当 tableIndex 改变时，调用 getChat 方法
  // }
  getStaticData()
}

// const chooseDeteType=ref('date')

const showPop = ref(false)
const min = ref(new Date(2020, 0, 1))
const max = ref(new Date(2025, 11, 31))
const detePop = ref(new Date())
const confirm = (val: any) => {
  if (val && val.selectedValue) {
    if (handleType.value === 1) {
      date1.value = val.selectedValue.join('-')
    } else {
      date.value = val.selectedValue.join('-')
    }
  }
  showPop.value = false
  // if(handleType.value===1){
  //   getChat(tabList1.value[status1.value].type)
  // }else{
  //   getChat(tabList.value[status.value].type)
  // }
  getStaticData()
}

function cancel() {
  showPop.value = false
}

function openTimePop(type) {
  handleType.value = type
  let key = ''
  if (type === 1) {
    key = tabListDate[tableIndexDate1.value].key
  } else {
    key = tabListDate[tableIndexDate.value].key
  }

  if (key === 'day') {
    chooseDeteType.value = 'date'
    detePop.value = new Date()
  } else if (key === 'month') {
    chooseDeteType.value = 'year-month'
    detePop.value = new Date()
  } else {
    chooseDeteType.value = 'year'
    dateYear.value = [dayjs(new Date()).format('YYYY')]
  }
  showPop.value = true
}
const dataX0 = ref()
const dataY0 = ref()
const dataX = ref()
const dataY = ref()
const dataX1 = ref([])
const dataY1 = ref()
const title = ref()
const title0 = ref()

// 获取图标数据
const getChat = async (type) => {
  console.log(type, 'tableIndexDate')
  //   // loading.value = true

  if (type === 'temperature-humidity') {
    // console.log(tableIndexDate.value,'tableIndexDate111')
    const [resCharging, resDischarging] = await Promise.all([
      LampAPI.voltageChat({
        deviceId: deviceInfo.value.deviceId,
        type: 'temperature',
        unit: tabListDate[tableIndexDate1.value].key,
        query: date1.value,
      }),
      LampAPI.voltageChat({
        deviceId: deviceInfo.value.deviceId,
        type: 'humidity',
        unit: tabListDate[tableIndexDate1.value].key,
        query: date1.value,
      }),
    ])
    console.log(resCharging, 'resCharging')
    console.log(resDischarging, 'resDischarging')
    loading.value = false
    dataX.value = []
    dataX1.value = []
    dataY.value = []
    dataY1.value = []

    title.value = '温湿度'
    if (resCharging && resCharging.values.length > 0) {
      resCharging.values.forEach((item) => {
        // dataX.value.push(item.x)
        // if (tableIndexDate.value === 1 || tableIndexDate1.value === 1) {
        if(tableIndexDate1.value === 1){
          console.log(item.x, 'item.x')
          dataX.value.push(`${item.x}日`)
        } else if ( tableIndexDate1.value === 2) {
          dataX.value.push(`${item.x}月`)
        } else {
          // dataX.value.push(`${item.x}时`)
          dataX.value.push(`${item.x}`)
        }
        dataY.value.push(item.y)
      })
    }

    if (resDischarging && resDischarging.values.length > 0) {
      resDischarging.values.forEach((item) => {
        if (!dataX1.value.includes(item.x)) {
          // dataX1.value.push(item.x)
          // if (tableIndexDate.value === 1 || tableIndexDate1.value === 1) {
          if ( tableIndexDate1.value === 1) {
            console.log(item.x, 'item.x')
            dataX1.value.push(`${item.x}日`)
          } else if ( tableIndexDate1.value === 2) {
            dataX1.value.push(`${item.x}月`)
          } else {
            // dataX1.value.push(`${item.x}时`)
            dataX1.value.push(`${item.x}`)
          }
        }

        dataY1.value.push(item.y)
      })
    }

    //     console.log(dataX.value, 'dataX.value')
    //     console.log(dataX1.value, 'dataX1.value', dataY.value, dataY1.value)
  } else {
    // console.log(tabList,tabList.value[status.value].type,'tabList[status.value].type')
    const res = await LampAPI.voltageChat({
      deviceId: deviceInfo.value.deviceId,
      type:
        handleType.value === 1
          ? tabList1.value[status1.value].type
          : tabList.value[status.value].type,
      unit:
        handleType.value === 1
          ? tabListDate[tableIndexDate1.value].key
          : tabListDate[tableIndexDate.value].key,
      query: handleType.value === 1 ? date1.value : date.value,
    })
    console.log(res, 'res')
    loading.value = false
    if (handleType.value === 1) { // 第二个echarts
      dataX.value = []
      dataY.value = []
      title.value = res.chartName
      if (res && res.values.length > 0) {
        res.values.forEach((item) => {
          // dataX.value.push(item.x)
          if ( tableIndexDate1.value === 1) {
            console.log(item.x, 'item.x')
            dataX.value.push(`${item.x}日`)
          } else if ( tableIndexDate1.value === 2) {
            dataX.value.push(`${item.x}月`)
          } else {
            // dataX.value.push(`${item.x}时`)
            dataX.value.push(`${item.x}`)
          }
          dataY.value.push(item.y)
        })
      }
    } else {
      dataX0.value = []
      dataY0.value = []
      title0.value = res.chartName
      if (res && res.values.length > 0) {
        res.values.forEach((item) => {
          // dataX0.value.push(item.x)
          if (tableIndexDate.value === 1 ) {
            console.log(item.x, 'item.x')
            dataX0.value.push(`${item.x}日`)
          } else if (tableIndexDate.value === 2 ) {
            dataX0.value.push(`${item.x}月`)
          } else {
            // dataX0.value.push(`${item.x}时`)
            dataX0.value.push(`${item.x}`)
          }
          dataY0.value.push(item.y)
        })
      }
    }
  }
}

// const dataX=ref([1,2,4,5,8])
// const dataY=ref([1,2,4,5,8])

const option0 = computed<EChartsOption>(() => {
  // 根据 dynamicLineData 长度动态设置 xAxis 的 axisLabel 间隔
  // const axisLabelInterval = dataX0?.value?.length < 5 ? 0 : 3
  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        return `${params[0].axisValue}<br/>${tabList.value[status.value].title}: ${value} ${
          tabList.value[status.value].unit
        }`
      },
    },
    xAxis: [
      {
        type: 'category',
        data: dataX0.value,
        boundaryGap: true, // 是否留白
        axisLine: {
          lineStyle: {
            color: '#CCCFD7',
          },
        },
        axisLabel: {
          // rotate: tableIndexDate.value === 0
          //   ? 45
          //   : 0, // 旋转角度，当 tableIndexDate.value 等于 1 时旋转 45 度
          formatter: (value) => {
            const maxLength = 10 // 每行最多显示的字符数
            if (value.length > maxLength) {
              return value.match(new RegExp(`.{1,${maxLength}}`, 'g')).join('\n')
            }
            return value
          }, // 保证标签文字一半时换行
        },
      },
    ],
    yAxis: [
      {
        name: tabList.value[status.value].title + '(' + tabList.value[status.value].unit + ')',
        type: 'value',
        // splitNumber: 4,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#CCCFD7',
          },
        },
        splitArea: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: tabList.value[status.value].title,
        type: 'line',
        symbol: 'emptyCircle', // 使用空心圆
        // symbol: 'circle', // 使用实心圆
        symbolSize: 8,
        data: dataY0.value,
        itemStyle: {
          normal: {
            color: '#00CC73',
            // borderWidth: 8,  // 边框宽度
            // symbolSize: 80,
            borderColor: '#00CC73', // 边框颜色
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255,255,255,1)',
                },
                {
                  offset: 1,
                  color: 'rgba(0,204,115, 0.1)',
                },
              ]),
            },
          },
        },
        smooth: true,
      },
    ],
    dataZoom: [
      {
        type: 'inside', // 内置缩放
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        zoomLock: false, // 允许缩放
      },
      {
        type: 'slider', // 滑动条单独显示
        show: dataX0.value.length > 4 ? true : false, // 是否显示滑动条
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        height: 6, // 滑动条组件高度
        bottom: 0, // 距离图表区域下边的距离
        borderRadius: 5,
        showDetail: true, // 拖拽时显示详情
        showDataShadow: false,
        fillerColor: '#019e59', // 平移条的填充颜色
        borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
        backgroundColor: '#CCCFD7', // 背景颜色
        handleStyle: {
          color: '#019e59', // 手柄颜色
        },
        textStyle: {
          fontSize: 12,
        },
      },
    ],
    grid: {
      top: '12%',
      left: '4%',
      right: '8%',
      bottom: '10%',
      containLabel: true, //‌防止标签溢出‌
    },
  }
})
const option = computed<EChartsOption>(() => {
  // 根据 dynamicLineData 长度动态设置 xAxis 的 axisLabel 间隔
  // const axisLabelInterval = dataX?.value?.length < 5 ? 0 : 3

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const value = params[0].value
        if (status1.value === 0 && tableIndexDate1.value === 0) {
          return `${params[0].axisValue} <br/>${tabList1.value[status1.value].title}: ${
            value === 0 ? '关' : value === 1 ? '开' : ''
          }`
        }
        return `${params[0].axisValue}<br/>${tabList1.value[status1.value].title}: ${value} ${
          tabList1.value[status1.value].unit
        }`
      },
    },
    xAxis: [
      {
        type: 'category',
        data: dataX.value,
        boundaryGap: true, // 是否留白
        axisLine: {
          lineStyle: {
            color: '#CCCFD7',
          },
        },
        axisLabel: {
          // rotate: tableIndexDate.value === 0
          //   ? 45
          //   : 0, // 旋转角度，当 tableIndexDate.value 等于 1 时旋转 45 度
          formatter: (value) => {
            const maxLength = 10 // 每行最多显示的字符数
            if (value.length > maxLength) {
              return value.match(new RegExp(`.{1,${maxLength}}`, 'g')).join('\n')
            }
            return value
          }, // 保证标签文字一半时换行
        },
      },
    ],
    // xAxis: [
    //   {
    //     type: 'category',
    //     data: dataX.value,
    //     axisLine: {
    //       lineStyle: {
    //         color: '#CCCFD7',
    //       },
    //     },
    //     axisLabel: {
    //       interval: 0,
    //     },
    //   },
    // ],
    yAxis: [
      {
        name:
          tableIndexDate1.value == 0 && status1.value == 0
            ? tabList1.value[status1.value].title
            : tabList1.value[status1.value].title + '(' + tabList1.value[status1.value].unit + ')',
        type: 'value',
        axisLabel: {
          formatter: (value) => {
            if (status1.value == 0 && tableIndexDate1.value === 0) {
              return value === 0 ? '关' : value === 1 ? '开' : ''
            }
            return status1.value == 1 ? Math.round(value) : value
          },
          hideOverlap: true, // 自动隐藏重叠的标签
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#CCCFD7',
          },
          // 杀虫数量，年/月不显示分割线
          // show: !([1, 2].includes(tableIndexDate1.value) && status1.value === 1),
        },
        splitArea: {
          show: false,
        },
        min: status1.value == 1 ? 0 : undefined,  
        max: status1.value == 1 ? Math.ceil(Math.max(...dataY.value)) : undefined,
        // 杀虫灯数量interval显示整数，且按最多11个显示
        interval: status1.value === 1 && dataY.value && dataY.value.length > 0  && (Math.max(...dataY.value) - 0)>10
          ? Math.ceil((Math.max(...dataY.value) - 0) / 10) :
          status1.value === 1 && dataY.value && dataY.value.length > 0  && (Math.max(...dataY.value) - 0)<11?1
          : undefined, // Ensure only integers are displayed
      },
    ],
    series: [
      {
        name: tabList1.value[status1.value].title,
        type: 'line',
        symbol: 'emptyCircle', // 使用空心圆
        // symbol: 'circle', // 使用实心圆
        symbolSize: 8,
        data: dataY.value,
        itemStyle: {
          normal: {
            color: '#00CC73',
            // borderWidth: 8,  // 边框宽度
            // symbolSize: 80,
            borderColor: '#00CC73', // 边框颜色
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255,255,255,1)',
                },
                {
                  offset: 1,
                  color: 'rgba(0,204,115, 0.1)',
                },
              ]),
            },
          },
        },
        smooth: true,
      },
    ],
    dataZoom: [
      {
        type: 'inside', // 内置缩放
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        zoomLock: false, // 允许缩放
      },
      {
        type: 'slider', // 滑动条单独显示
        show: dataX.value.length > 4 ? true : false, // 是否显示滑动条
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        height: 6, // 滑动条组件高度
        bottom: 0, // 距离图表区域下边的距离
        borderRadius: 5,
        showDetail: true, // 拖拽时显示详情
        showDataShadow: false,
        fillerColor: '#019e59', // 平移条的填充颜色
        borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
        backgroundColor: '#CCCFD7', // 背景颜色
        handleStyle: {
          color: '#019e59', // 手柄颜色
        },
        textStyle: {
          fontSize: 12,
        },
      },
    ],
    // dataZoom: [
    //   {
    //     type: 'slider', // 滑动条单独显示
    //     show: dataX.value.length>4 ? true : false, // 是否显示滑动条
    //     // start: 0, // 左边的滑块位置，表示从0开始显示
    //     // end: 20, // 右边的滑块位置，表示只显示3个点（33.33333333333333%表示总长度的30%）
    //     startValue: 0, // 展示区域内容的起始数值
    //     endValue: 5, // 展示区域内容的结束数值 当前展示x坐标下标为0-7
    //     height: 6, // 滑动条组件高度
    //     bottom: 0, // 距离图表区域下边的距离
    //     borderRadius: 5,
    //     showDetail: false, // 拖拽时是否显示详情
    //     showDataShadow: false,
    //     fillerColor: '#019e59', // 平移条的填充颜色
    //     borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
    //     backgroundColor: '#CCCFD7', // 背景颜色
    //     zoomLock: true, // 锁定视图
    //     brushSelect: false, // 不可缩放 滑动条默认是有手柄可以进行展示的内容区域缩放的，不太美观
    //     // 通过该属性可以只滑动，不显示缩放功能
    //     handleStyle: {
    //       opacity: 0,
    //     },
    //     lineStyle: {
    //       opacity: 0,
    //     },
    //     textStyle: {
    //       fontSize: 0,
    //     },
    //   },
    // ],
    grid: {
      top: '12%',
      left: '6%',
      right: '8%',
      bottom: '10%',
      containLabel: true, //‌防止标签溢出‌
    },
  }
})

const option1 = computed<EChartsOption>(() => {
  // 根据 dynamicLineData 长度动态设置 xAxis 的 axisLabel 间隔
  // const axisLabelInterval = dataX1.value.length < 5 ? 0 : 4

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const temperature = params[0].value
        const humidity = params[1].value
        return `${params[0].axisValue}<br/>温度: ${temperature} ℃<br/>湿度: ${humidity} %`
      },
    },
    grid: {
      top: '10%',
      left: '4%',
      right: '8%',
      bottom: '10%',
      containLabel: true, //‌防止标签溢出‌
    },
    xAxis: [
      {
        type: 'category',
        data: dataX.value,
        boundaryGap: true, // 是否留白
        axisLine: {
          lineStyle: {
            color: '#CCCFD7',
          },
        },
        axisLabel: {
          // rotate: tableIndexDate.value === 0
          //   ? 45
          //   : 0, // 旋转角度，当 tableIndexDate.value 等于 1 时旋转 45 度
          formatter: (value) => {
            const maxLength = 10 // 每行最多显示的字符数
            if (value.length > maxLength) {
              return value.match(new RegExp(`.{1,${maxLength}}`, 'g')).join('\n')
            }
            return value
          }, // 保证标签文字一半时换行
        },
      },
    ],
    // xAxis: [
    //   {
    //     type: 'category',
    //     data: dataX.value,
    //     axisLine: {
    //       lineStyle: {
    //         color: '#CCCFD7',
    //       },
    //     },
    //     axisLabel: {
    //       interval: 0,
    //     },
    //   },
    // ],
    yAxis: [
      {
        type: 'value',
        // splitNumber: 1,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#CCCFD7',
          },
        },
        splitArea: {
          show: false,
        },
      },
      // {
      //   type: 'value',
      //   splitNumber: 1,
      //   splitLine: {
      //     lineStyle: {
      //       type: 'dashed',
      //       color: '#CCCFD7',
      //     },
      //   },
      //   splitArea: {
      //     show: false,
      //   },
      // },
    ],
    series: [
      {
        name: '温度',
        type: 'line',
        symbol: 'emptyCircle', // 使用空心圆
        // symbol: 'circle', // 使用实心圆
        symbolSize: 8,
        data: dataY.value,
        itemStyle: {
          normal: {
            color: '#00CC73',
            // borderWidth: 8,  // 边框宽度
            // symbolSize: 80,
            borderColor: '#00CC73', // 边框颜色
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255,255,255,1)',
                },
                {
                  offset: 1,
                  color: 'rgba(0,204,115, 0.1)',
                },
              ]),
            },
          },
        },
        smooth: true,
      },
      {
        name: '湿度',
        type: 'line',
        symbol: 'emptyCircle', // 使用空心圆
        // symbol: 'circle', // 使用实心圆
        symbolSize: 8,
        data: dataY1.value,
        itemStyle: {
          normal: {
            color: '#5398FF',
            // borderWidth: 8,  // 边框宽度
            // symbolSize: 80,
            borderColor: '#5398FF', // 边框颜色
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255,255,255,1)',
                },
                {
                  offset: 1,
                  color: 'rgba(83,152,255, 0.1)',
                },
              ]),
            },
          },
        },
        smooth: true,
      },
    ],
    dataZoom: [
      {
        type: 'inside', // 内置缩放
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        zoomLock: false, // 允许缩放
      },
      {
        type: 'slider', // 滑动条单独显示
        show: dataX1.value.length > 4, // 是否显示滑动条
        start: 0, // 默认展示所有数据
        end: 100, // 默认展示所有数据
        height: 6, // 滑动条组件高度
        bottom: 0, // 距离图表区域下边的距离
        borderRadius: 5,
        showDetail: true, // 拖拽时显示详情
        showDataShadow: false,
        fillerColor: '#019e59', // 平移条的填充颜色
        borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
        backgroundColor: '#CCCFD7', // 背景颜色
        handleStyle: {
          color: '#019e59', // 手柄颜色
        },
        textStyle: {
          fontSize: 12,
        },
      },
    ],
    // dataZoom: [
    //   {
    //     type: 'slider', // 滑动条单独显示
    //     show: dataX1.value.length>4 ? true : false, // 是否显示滑动条
    //     // start: 0, // 左边的滑块位置，表示从0开始显示
    //     // end: 20, // 右边的滑块位置，表示只显示3个点（33.33333333333333%表示总长度的30%）
    //     startValue: 0, // 展示区域内容的起始数值
    //     endValue: 5, // 展示区域内容的结束数值 当前展示x坐标下标为0-7
    //     height: 6, // 滑动条组件高度
    //     bottom: 0, // 距离图表区域下边的距离
    //     borderRadius: 5,
    //     showDetail: false, // 拖拽时是否显示详情
    //     showDataShadow: false,
    //     fillerColor: '#019e59', // 平移条的填充颜色
    //     borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
    //     backgroundColor: '#CCCFD7', // 背景颜色
    //     zoomLock: true, // 锁定视图
    //     brushSelect: false, // 不可缩放 滑动条默认是有手柄可以进行展示的内容区域缩放的，不太美观
    //     // 通过该属性可以只滑动，不显示缩放功能
    //     handleStyle: {
    //       opacity: 0,
    //     },
    //     lineStyle: {
    //       opacity: 0,
    //     },
    //     textStyle: {
    //       fontSize: 0,
    //     },
    //   },
    // ],
  }
})

const goBack = () => {
  Taro.navigateBack()
}
</script>

<style scoped lang="scss">
.content {
  width: 690px;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  margin: 30px auto;
  position: relative;
  padding-bottom: 30px;
  overflow: hidden;
  .content-titlt {
    width: 100%;
    // height: 0px;
    // border-bottom: 1px solid #d4d4d4;
    position: relative;
    .content-line {
      width: 100%;
      height: 0px;
      border-bottom: 1px solid #d4d4d4;
      position: absolute;
      bottom: 6px;
      left: 0;
      z-index: 0;
    }
  }
}
.timeTab {
  position: absolute;
  top: 22px;
  right: 30px;
  // width: 100px;
  // height: 40px;
  // background: #FFFFFF;
  // border-radius: 20px 20px 0px 0px;
  // z-index: 999;
}
.chartsDiv {
  width: 100%;
  height: 500px;
  margin-top: 100px;
}
.charts-nodata {
  width: 100%;
  height: 500px;
  // margin-top:100px ;
  text-align: center;
  line-height: 500px;
  font-size: 32px;
  color: #000000;
}

// input 框
.dateInput {
  width: 160px;
  height: 58px;
  line-height: 58px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #d4d4d4;
  font-weight: 400;
  font-size: 22px;
  color: #000000;
  padding: 0 15px;
  position: absolute;
  right: 30px;
  top: 130px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.marginTop-80{
  margin-top: 80px;
}
</style>
